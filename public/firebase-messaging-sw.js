/**
 * Firebase Cloud Messaging Service Worker
 *
 * This service worker handles background push notifications when the
 * ERA Dispatch Application is not in the foreground.
 */

// Import Firebase scripts for service worker
importScripts(
  'https://www.gstatic.com/firebasejs/10.12.2/firebase-app-compat.js'
)
importScripts(
  'https://www.gstatic.com/firebasejs/10.12.2/firebase-messaging-compat.js'
)

// --- ENVIRONMENT CONFIG INJECTION ---
// This block will be replaced at build time by Vite or your build tool
// Vite: use the vite-plugin-replace or a similar plugin to replace __FIREBASE_CONFIG__

const firebaseConfig_prod = {
  apiKey: 'AIzaSyDFZBrSxYDRHlPfzSSOfOj3lE0bZhSg9Z4',
  authDomain: 'era-dispatch-and-control.firebaseapp.com',
  databaseURL: 'https://era-dispatch-and-control.firebaseio.com',
  projectId: 'era-dispatch-and-control',
  storageBucket: 'era-dispatch-and-control.appspot.com',
  messagingSenderId: '111653508714',
  appId: '1:111653508714:web:70adee1ba81e7e39ee9c13',
  measurementId: 'G-8KDS2WQKJZ',
}

const firebaseConfig_dev = {
  apiKey: 'AIzaSyBMi8q2Oj6rnrkD9y5B7One-DhlW1IR_aA',
  authDomain: 'era-test-app.firebaseapp.com',
  projectId: 'era-test-app',
  storageBucket: 'era-test-app.appspot.com',
  messagingSenderId: '765748041152',
  appId: '1:765748041152:web:f5b8c646943dd760db2425',
  measurementId: 'G-F67669PLR2',
}

// Use a placeholder that can be replaced at build time, fallback to dev
const ENV = self.ERA_ENV || 'development' // Set this in your build process if needed
const firebaseConfig =
  ENV === 'production' ? firebaseConfig_prod : firebaseConfig_dev
// --- END ENVIRONMENT CONFIG INJECTION ---

firebase.initializeApp(firebaseConfig)

// Get messaging instance
const messaging = firebase.messaging()

// Handle background messages
messaging.onBackgroundMessage(function (payload) {
  console.log('Received background message:', payload)

  // Extract notification data
  const notificationTitle =
    payload.notification?.title || 'ERA Dispatch Notification'
  const notificationOptions = {
    body: payload.notification?.body || 'You have a new notification',
    icon: payload.notification?.icon || '/favicon.ico',
    badge: '/favicon.ico',
    tag: payload.data?.tag || 'era-dispatch',
    data: payload.data || {},
    actions: [
      {
        action: 'open',
        title: 'Open App',
        icon: '/favicon.ico',
      },
      {
        action: 'dismiss',
        title: 'Dismiss',
        icon: '/favicon.ico',
      },
    ],
    requireInteraction: true,
    silent: false,
  }

  // Show notification
  return self.registration.showNotification(
    notificationTitle,
    notificationOptions
  )
})

self.addEventListener('notificationclick', function (event) {
  event.notification.close()
  event.waitUntil(clients.openWindow('/'))
})
