{"name": "era-dispatch-app-v2", "version": "2.0.0", "type": "module", "private": true, "description": "ERA Dispatch App v2", "scripts": {"dev": "vite", "dev:check": "concurrently \"npm run dev\" \"npm run type-check:watch\"", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "check-all": "npm run type-check && npm run lint && npm run format:check", "fix-all": "npm run format && npm run lint:fix", "prepare": "husky install", "build:analyze": "vite build --mode analyze"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@aws-amplify/ui-react": "^6.0.7", "@googlemaps/react-wrapper": "^1.2.0", "@hookform/resolvers": "^3.3.2", "@mantine/core": "^8.1.3", "@mantine/dates": "^8.1.3", "@mantine/dropzone": "^8.1.3", "@mantine/form": "^8.1.3", "@mantine/hooks": "^8.1.3", "@mantine/modals": "^8.1.3", "@mantine/notifications": "^8.1.3", "@mantine/spotlight": "^8.1.3", "@sentry/react": "^9.42.1", "@sentry/tracing": "^7.120.3", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "@types/google.maps": "^3.58.1", "antd": "^5.12.8", "aws-amplify": "^6.0.7", "axios": "^1.6.2", "date-fns": "^2.30.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "firebase": "^12.0.0", "immer": "^10.0.3", "lucide-react": "^0.525.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.20.1", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/node": "^24.0.15", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.1.1", "@vitest/coverage-v8": "^1.0.0", "@vitest/ui": "^1.0.0", "concurrently": "^9.2.0", "eslint": "^8.54.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "husky": "^8.0.3", "jsdom": "^23.0.1", "lint-staged": "^15.1.0", "prettier": "^3.1.0", "rollup-plugin-visualizer": "^5.9.2", "tailwindcss": "^4.1.11", "typescript": "^5.2.2", "vite": "^5.0.0", "vitest": "^1.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}