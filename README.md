# ERA Dispatch App v2

Modern React application for emergency response dispatch management, built with cutting-edge technologies for optimal performance and developer experience.

## 🚀 Tech Stack

- **Framework**: React 18 with TypeScript (strict mode)
- **Build Tool**: Vite
- **State Management**: 
  - TanStack Query v4 (server state)
  - <PERSON><PERSON><PERSON> (client state)
- **Routing**: React Router v6
- **Forms**: React Hook Form + Zod validation
- **UI Framework**: Ant Design v5
- **Authentication**: AWS Amplify v6
- **Testing**: Vitest + Testing Library
- **Code Quality**: ESLint + Prettier + Husky

## 📋 Prerequisites

- Node.js >= 18.0.0
- npm >= 9.0.0

## 🛠️ Setup Instructions

1. **Clone the repository**
   ```bash
   <NAME_EMAIL>:Emergency-Response-Africa/dispatch-app-v2.git
   cd dispatch-app-v2
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment setup**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration values
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

## 📝 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run test` - Run tests
- `npm run test:ui` - Run tests with UI
- `npm run test:coverage` - Run tests with coverage
- `npm run lint` - Lint code
- `npm run lint:fix` - Fix linting issues
- `npm run format` - Format code with Prettier
- `npm run type-check` - Type check without emitting
- `npm run build:analyze` - Build with bundle analyzer

## 🏗️ Project Structure

```
src/
├── api/              # API layer and HTTP client
├── components/       # Reusable UI components
├── config/          # App configuration
├── hooks/           # Custom React hooks
├── pages/           # Page components
├── providers/       # Context providers
├── routes/          # Route definitions
├── store/           # Zustand stores
├── styles/          # Global styles
├── test/            # Test utilities
├── types/           # TypeScript type definitions
└── utils/           # Utility functions
```

## 🔧 Configuration

### Environment Variables

See `.env.example` for all required environment variables.

### AWS Amplify Setup

1. Configure your AWS Cognito User Pool
2. Update environment variables with your pool details
3. Ensure redirect URLs are configured in Cognito

### Development Tools

- **ESLint**: Configured with TypeScript strict rules
- **Prettier**: Consistent code formatting
- **Husky**: Pre-commit hooks for code quality
- **Vitest**: Fast unit testing with coverage

## 🚀 Deployment

The app is configured for deployment to:
- AWS S3 + CloudFront
- Vercel
- Netlify

Build the app with:
```bash
npm run build
```

## 🧪 Testing

Run tests with:
```bash
npm run test          # Run tests
npm run test:ui       # Interactive test UI
npm run test:coverage # With coverage report
```

## 📊 Performance

- Bundle analysis available with `npm run build:analyze`
- Optimized chunk splitting for better caching
- Tree-shaking enabled for smaller bundles

## 🤝 Contributing

1. Follow the existing code style
2. Write tests for new features
3. Ensure all checks pass before committing
4. Use conventional commit messages

## 📄 License

Private - ERA Internal Use Only