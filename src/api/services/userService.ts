import type { AxiosResponse } from 'axios'
import apiClient from '../client'
import { handleApiError } from '@/utils/apiErrorHandling'
import authService from './authService'
import { User, UsersLookupResponse, UsersResponse } from '@/types/api/users'

class UserService {
  /**
   * Get users
   */
  async getUsers(): Promise<User[]> {
    try {
      const precinctInfo = await authService.getPrecinct()

      const params: Record<string, string> = {
        limit: '0',
      }

      if (precinctInfo?.precinct_id) {
        params.precinct_id = precinctInfo.precinct_id
      }

      const response: AxiosResponse<UsersResponse> = await apiClient.get(
        'users',
        {
          params,
          validateStatus: () => true,
        }
      )

      if (response.status !== 200) {
        throw new Error('Failed to fetch users')
      }

      return response.data.data.users
    } catch (error) {
      throw handleApiError(error, 'getUsers')
    }
  }

  /**
   * Get users lookup
   */
  async getUsersLookup(): Promise<UsersLookupResponse['data']['users']> {
    try {
      const precinctInfo = await authService.getPrecinct()

      const params: Record<string, string> = {
        limit: '0',
      }

      if (precinctInfo?.precinct_id) {
        params.precinct_id = precinctInfo.precinct_id
      }

      const response: AxiosResponse<UsersLookupResponse> = await apiClient.get(
        'users/lookup',
        { params }
      )

      return response.data.data.users
    } catch (error) {
      throw handleApiError(error, 'getUsersLookup')
    }
  }
}

// Export singleton instance
export const userService = new UserService()
export default userService
