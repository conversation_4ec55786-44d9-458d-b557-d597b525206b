/**
 * Authentication API Service
 *
 * API service for dispatcher and precinct management
 */

import type { AxiosResponse } from 'axios'
import { apiClient } from '../client'
import { API_ENDPOINTS } from '@/config/api'
import { handleApiError } from '@/utils/apiErrorHandling'
import { useAuthStore } from '@/store/authStore'
import {
  Dispatcher,
  DispatcherResponse,
  UpdateDispatcherRequest,
} from '@/types/api/auth'

class AuthService {
  /**
   * Get dispatcher by ID
   */
  async getDispatcher(dispatcherId: string): Promise<Dispatcher> {
    try {
      // Fetch fresh data from API
      const response: AxiosResponse<DispatcherResponse> = await apiClient.get(
        API_ENDPOINTS.DISPATCHER_BY_ID(dispatcherId)
      )

      const dispatcher = response.data?.data?.dispatcher
      return dispatcher
    } catch (error) {
      throw handleApiError(error, 'getDispatcher')
    }
  }

  /**
   * Update dispatcher information for the current dispatcher
   * Automatically retrieves dispatcher ID from auth store
   */
  async updateDispatcher(data: UpdateDispatcherRequest): Promise<Dispatcher> {
    try {
      // Get dispatcher ID from auth store
      const dispatcherId = this.getCurrentDispatcherId()

      if (!dispatcherId) {
        throw new Error(
          'No authenticated dispatcher found. Please log in again.'
        )
      }

      const response: AxiosResponse<DispatcherResponse> = await apiClient.put(
        API_ENDPOINTS.DISPATCHER_BY_ID(dispatcherId),
        data
      )

      return response.data.data.dispatcher
    } catch (error) {
      throw handleApiError(error, 'updateDispatcher')
    }
  }

  /**
   * Get precinct information for the current dispatcher
   * Automatically retrieves dispatcher ID from auth store
   */
  async getPrecinct(): Promise<{ precinct_id: string; precinct_name: string }> {
    try {
      // Get dispatcher ID from auth store
      const dispatcherId = this.getCurrentDispatcherId()

      if (!dispatcherId) {
        throw new Error(
          'No authenticated dispatcher found. Please log in again.'
        )
      }

      // Fetch dispatcher data from API to get precinct information
      const dispatcher = await this.getDispatcher(dispatcherId)
      const precinctData = {
        precinct_id: dispatcher.precinct_id,
        precinct_name: dispatcher.precinct_name,
      }

      return precinctData
    } catch (error) {
      throw handleApiError(error, 'getPrecinct')
    }
  }

  /**
   * Get current dispatcher ID from auth store
   */
  getCurrentDispatcherId(): string | null {
    try {
      const authStore = useAuthStore.getState()
      return authStore.user?.sub || null
    } catch (error) {
      console.error('Failed to get dispatcher ID:', error)
      return null
    }
  }

  /**
   * Get current user context from auth store
   */
  getCurrentUserContext(): { user_id: string; token: string } | null {
    try {
      // This would typically come from your auth store
      // For now, we'll return null and let the interceptor handle auth
      return null
    } catch (error) {
      console.error('Failed to get user context:', error)
      return null
    }
  }
}

// Export singleton instance
export const authService = new AuthService()
export default authService
