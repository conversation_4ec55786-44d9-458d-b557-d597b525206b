import type { AxiosResponse } from 'axios'
import apiClient from '../client'
import { handleApiError } from '@/utils/apiErrorHandling'
import authService from './authService'
import {
  Hospital,
  HospitalsResponse,
  HospitalTransferResponse,
  HospitalCaseAssignmentResponse,
  HospitalCase,
  HospitalCasesResponse,
} from '@/types/api/hospital'

class HospitalService {
  /**
   * Add hospital transfer
   */
  async addHospitalTransfer(
    hospitalId: string,
    caseId: string,
    report: string,
    notes: string
  ): Promise<HospitalTransferResponse['data']> {
    try {
      const response: AxiosResponse<HospitalTransferResponse> =
        await apiClient.post(`hospitals/${hospitalId}/cases`, {
          caseId,
          report,
          notes,
        })

      return response.data.data
    } catch (error) {
      throw handleApiError(error, 'addHospitalTransfer')
    }
  }

  /**
   * Assign hospital case
   */
  async assignHospitalCase(
    hospitalId: string,
    caseId: string
  ): Promise<HospitalCaseAssignmentResponse['data']> {
    try {
      const response: AxiosResponse<HospitalCaseAssignmentResponse> =
        await apiClient.post(
          'hospitals/convertcase',
          { case_id: caseId, hospital_id: hospitalId },
          { validateStatus: () => true }
        )

      if (response.status !== 200) {
        throw new Error('Failed to assign hospital case')
      }

      return response.data.data
    } catch (error) {
      throw handleApiError(error, 'assignHospitalCase')
    }
  }

  /**
   * Get hospital cases
   */
  async getHospitalCases(): Promise<HospitalCase[]> {
    try {
      const precinctInfo = await authService.getPrecinct()

      const params: Record<string, string> = {
        status: 'new',
        limit: '0',
      }

      if (precinctInfo?.precinct_id) {
        params.precinct_id = precinctInfo.precinct_id
      }

      const response: AxiosResponse<HospitalCasesResponse> =
        await apiClient.get('hospitals/cases', {
          params,
          validateStatus: () => true,
        })

      if (response.status !== 200) {
        throw new Error('Failed to fetch hospital cases')
      }

      return response.data.data.cases
    } catch (error) {
      throw handleApiError(error, 'getHospitalCases')
    }
  }

  /**
   * Get all hospitals (initial load)
   */
  async getAllHospitalsInit(): Promise<Hospital[]> {
    try {
      const precinctInfo = await authService.getPrecinct()

      const params: Record<string, string> = {
        task: 'trim',
      }

      if (precinctInfo?.precinct_id) {
        params.precinct_id = precinctInfo.precinct_id
      }

      const response: AxiosResponse<HospitalsResponse> = await apiClient.get(
        'hospitals',
        {
          params,
          validateStatus: () => true,
        }
      )

      if (response.status !== 200) {
        throw new Error('Failed to fetch hospitals')
      }

      return response.data.data
    } catch (error) {
      throw handleApiError(error, 'getAllHospitalsInit')
    }
  }

  /**
   * Get all hospitals
   */
  async getAllHospitals(): Promise<Hospital[]> {
    try {
      const precinctInfo = await authService.getPrecinct()

      const params: Record<string, string> = {
        limit: '0',
      }

      if (precinctInfo?.precinct_id) {
        params.precinct_id = precinctInfo.precinct_id
      }

      const response: AxiosResponse<HospitalsResponse> = await apiClient.get(
        'hospitals',
        {
          params,
          validateStatus: () => true,
        }
      )

      if (response.status !== 200) {
        throw new Error('Failed to fetch hospitals')
      }

      if (response?.data?.data) {
        let hospitals = response.data.data

        hospitals = hospitals.map((item: Hospital, index: number) => {
          if (!item.severity) {
            item.severity = 'Low'
          }
          item.key = index
          return item
        })

        return hospitals
      }

      return []
    } catch (error) {
      throw handleApiError(error, 'getAllHospitals')
    }
  }
}

// Export singleton instance
export const hospitalService = new HospitalService()
export default hospitalService
