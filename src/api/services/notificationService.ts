/**
 * Notifications API Service
 *
 * API service for notification management operations
 */

import type { AxiosResponse } from 'axios'
import { apiClient } from '../client'
import { API_ENDPOINTS } from '@/config/api'
import { handleApiError } from '@/utils/apiErrorHandling'
import { authService } from './authService'
import {
  Notification,
  NotificationSummary,
  NotificationResponse,
  NotificationListResponse,
  NotificationListParams,
  BulkMarkReadRequest,
  BulkNotificationResponse,
  FirebaseTokenResponse,
  NotificationPreferences,
  NotificationPreferencesResponse,
} from '@/types/api/notifications'
import { FirebaseTokenRegistrationRequest } from '@/types/api/auth'

class NotificationService {
  /**
   * Get notifications list with pagination and filtering
   */
  async getNotifications(
    params: NotificationListParams = {},
    dispatcherId?: string
  ): Promise<NotificationListResponse['data']> {
    try {
      // Get precinct info for filtering if dispatcher ID is provided
      let precinctInfo = null
      if (dispatcherId) {
        precinctInfo = await authService.getPrecinct()
      }

      // Build query parameters
      const queryParams = {
        entity: 'dispatcher',
        limit: params.limit || 20,
        precinct_id: params.precinct_id || precinctInfo?.precinct_id,
        ...params,
      }

      const response: AxiosResponse<any> = await apiClient.get(
        API_ENDPOINTS.NOTIFICATIONS,
        {
          params: queryParams,
        }
      )

      // Handle the actual API response structure
      const responseData = response.data?.data

      // Ensure we have valid response data
      if (!responseData) {
        console.warn(
          'getNotifications: No data in response, returning empty result'
        )
        return {
          items: [],
          pagination: {
            currentPage: 1,
            totalPages: 1,
            totalItems: 0,
            itemsPerPage: queryParams.limit,
            hasNextPage: false,
            hasPreviousPage: false,
          },
        }
      }

      // Transform the API response to match expected format
      return {
        items: responseData.notifications || [],
        pagination: {
          currentPage: 1,
          totalPages: 1,
          totalItems: responseData.count || 0,
          itemsPerPage: queryParams.limit,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      }
    } catch (error) {
      throw handleApiError(error, 'getNotifications')
    }
  }

  /**
   * Get notification by ID
   */
  async getNotification(notificationId: string): Promise<Notification> {
    try {
      const response: AxiosResponse<NotificationResponse> = await apiClient.get(
        API_ENDPOINTS.NOTIFICATION_BY_ID(notificationId)
      )

      return response.data.data.notification
    } catch (error) {
      throw handleApiError(error, 'getNotification')
    }
  }

  /**
   * Mark notification as read
   */
  async markNotificationAsRead(
    notificationId: string,
    entity: 'dispatcher' | 'responder' = 'dispatcher'
  ): Promise<Notification> {
    try {
      const response: AxiosResponse<NotificationResponse> = await apiClient.put(
        `${API_ENDPOINTS.NOTIFICATION_BY_ID(notificationId)}?entity=${entity}`
      )

      return response.data.data.notification
    } catch (error) {
      throw handleApiError(error, 'markNotificationAsRead')
    }
  }

  /**
   * Mark multiple notifications as read
   */
  async bulkMarkAsRead(
    data: BulkMarkReadRequest
  ): Promise<BulkNotificationResponse['data']> {
    try {
      const response: AxiosResponse<BulkNotificationResponse> =
        await apiClient.put(`${API_ENDPOINTS.NOTIFICATIONS}/bulk-read`, data)

      return response.data.data
    } catch (error) {
      throw handleApiError(error, 'bulkMarkAsRead')
    }
  }

  /**
   * Register Firebase token for push notifications
   */
  async registerFirebaseToken(
    data: FirebaseTokenRegistrationRequest
  ): Promise<FirebaseTokenResponse['data']> {
    try {
      const response: AxiosResponse<FirebaseTokenResponse> =
        await apiClient.post(API_ENDPOINTS.NOTIFICATIONS_REGISTER, data)

      return response.data.data
    } catch (error) {
      throw handleApiError(error, 'registerFirebaseToken')
    }
  }

  /**
   * Unregister Firebase token
   */
  async unregisterFirebaseToken(tokenId: string): Promise<void> {
    try {
      await apiClient.delete(
        `${API_ENDPOINTS.NOTIFICATIONS_REGISTER}/${tokenId}`
      )
    } catch (error) {
      throw handleApiError(error, 'unregisterFirebaseToken')
    }
  }

  /**
   * Get notification preferences
   */
  async getNotificationPreferences(
    userId: string
  ): Promise<NotificationPreferences> {
    try {
      const response: AxiosResponse<NotificationPreferencesResponse> =
        await apiClient.get(
          `${API_ENDPOINTS.NOTIFICATIONS}/preferences/${userId}`
        )

      return response.data.data.preferences
    } catch (error) {
      throw handleApiError(error, 'getNotificationPreferences')
    }
  }

  /**
   * Update notification preferences
   */
  async updateNotificationPreferences(
    userId: string,
    preferences: Partial<NotificationPreferences>
  ): Promise<NotificationPreferences> {
    try {
      const response: AxiosResponse<NotificationPreferencesResponse> =
        await apiClient.put(
          `${API_ENDPOINTS.NOTIFICATIONS}/preferences/${userId}`,
          preferences
        )

      return response.data.data.preferences
    } catch (error) {
      throw handleApiError(error, 'updateNotificationPreferences')
    }
  }

  /**
   * Get recent notifications
   */
  async getRecentNotifications(
    limit: number = 10
  ): Promise<NotificationSummary[]> {
    try {
      const result = await this.getNotifications({
        limit,
        sort_by: 'created_at',
        sort_order: 'desc',
      })

      return result.items
    } catch (error) {
      throw handleApiError(error, 'getRecentNotifications')
    }
  }

  /**
   * Get unread notifications
   */
  async getUnreadNotifications(
    limit: number = 50
  ): Promise<NotificationSummary[]> {
    try {
      const result = await this.getNotifications({
        limit,
        // status: ['unread'],
        // sort_by: 'created_at',
        // sort_order: 'desc',
      })

      // Ensure result and items exist to prevent undefined return
      if (!result?.items) {
        console.warn(
          'getUnreadNotifications: No items in result, returning empty array'
        )
        return []
      }

      // Map the API response to NotificationSummary format
      return result.items.map((notification: any) => ({
        id: notification.notification_id,
        title: notification.title,
        message: notification.body,
        type: notification.data_type || 'update',
        status: notification.status === 'new' ? 'unread' : 'read',
        priority: 'medium' as const,
        created_at: notification.createdAt,
        read_at:
          notification.status === 'read' ? notification.updatedAt : undefined,
        related_entity_type: notification.data_group,
        related_entity_id: notification.data_id,
        action_url: undefined,
      }))
    } catch (error) {
      console.error('getUnreadNotifications error:', error)
      throw handleApiError(error, 'getUnreadNotifications')
    }
  }

  /**
   * Mark all notifications as read
   */
  async markAllAsRead(
    entity: 'dispatcher' | 'responder' = 'dispatcher'
  ): Promise<number> {
    try {
      // Get all unread notifications
      const unreadNotifications = await this.getUnreadNotifications(1000)

      if (unreadNotifications.length === 0) {
        return 0
      }

      // Mark all as read
      const result = await this.bulkMarkAsRead({
        notification_ids: unreadNotifications.map(n => n.id),
        entity,
      })

      return result.success_count
    } catch (error) {
      throw handleApiError(error, 'markAllAsRead')
    }
  }
}

// Export singleton instance
export const notificationService = new NotificationService()
export default notificationService
