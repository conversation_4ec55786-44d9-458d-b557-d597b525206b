/**
 * Additional API Service
 *
 * API service for additional endpoints not covered by main services
 */

import type { AxiosResponse } from 'axios'
import { apiClient } from '../client'
import { API_ENDPOINTS } from '@/config/api'
import { handleApiError } from '@/utils/apiErrorHandling'
import { NHISVerificationResponse } from '@/types/api/additional'

class AdditionalService {
  /**
   * Verify NHIS enrollment
   */
  async verifyNHIS(phone: string): Promise<NHISVerificationResponse['data']> {
    try {
      const response: AxiosResponse<NHISVerificationResponse> =
        await apiClient.get(API_ENDPOINTS.NHIS_VERIFY(phone), {
          headers: {
            'api-key': import.meta.env.VITE_NHIS_API_KEY,
            'skip-auth': true, // Skip auth for external API
          },
        })

      return response.data.data
    } catch (error) {
      throw handleApiError(error, 'verifyNHIS')
    }
  }
}

// Export singleton instance
export const additionalService = new AdditionalService()
export default additionalService
