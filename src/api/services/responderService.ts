import type { AxiosResponse } from 'axios'
import apiClient from '../client'
import { API_ENDPOINTS } from '@/config/api'
import { handleApiError } from '@/utils/apiErrorHandling'
import authService from './authService'
import {
  RespondersResponse,
  NotifyResponderResponse,
  NotifyResponderRequest,
} from '@/types/api/responders'

class ResponderService {
  /**
   * Get all responders
   */
  async getAllResponders(
    pageNumber: number = 1,
    numberPerPage: number = 500
  ): Promise<RespondersResponse['data']> {
    try {
      const precinctInfo = await authService.getPrecinct()

      const params: Record<string, string | number> = {
        entity: 'responders',
        skip_cache: 'true',
        numberPerPage,
        pageNumber,
        task: 'trim',
      }

      if (precinctInfo?.precinct_id) {
        params.precinct_id = precinctInfo.precinct_id
      }

      const response: AxiosResponse<any> = await apiClient.get(
        API_ENDPOINTS.ENTITIES,
        {
          params,
          validateStatus: () => true,
        }
      )

      if (response.status !== 200) {
        throw new Error('Failed to fetch responders')
      }

      // Ensure we have an array for items - the actual data is nested deeper
      const itemsArray = Array.isArray(response.data.data.data)
        ? response.data.data.data
        : []

      // Transform the actual API response to match the expected PaginatedResponse structure
      const transformedData: RespondersResponse['data'] = {
        items: itemsArray,
        pagination: {
          currentPage: pageNumber,
          totalPages: Math.ceil(
            (response.data.data.total_count || 0) / numberPerPage
          ),
          totalItems: response.data.data.total_count || 0,
          itemsPerPage: numberPerPage,
          hasNextPage: response.data.data.last_key ? true : false,
          hasPreviousPage: pageNumber > 1,
        },
      }

      return transformedData
    } catch (error) {
      throw handleApiError(error, 'getAllResponders')
    }
  }

  /**
   * Notify responders
   */
  async notifyResponder(
    data: NotifyResponderRequest
  ): Promise<NotifyResponderResponse['data']> {
    try {
      const response: AxiosResponse<NotifyResponderResponse> =
        await apiClient.post(
          `${API_ENDPOINTS.DISPATCHERS}/notify-responders`,
          data
        )

      return response.data.data
    } catch (error) {
      throw handleApiError(error, 'notifyResponder')
    }
  }
}

// Export singleton instance
export const responderService = new ResponderService()
export default responderService
