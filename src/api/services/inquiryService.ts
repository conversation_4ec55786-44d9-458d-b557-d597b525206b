/**
 * Inquiries API Service
 *
 * API service for inquiry management operations
 */

import type { AxiosResponse } from 'axios'
import { apiClient } from '../client'
import { API_ENDPOINTS } from '@/config/api'
import { handleApiError } from '@/utils/apiErrorHandling'
import { authService } from './authService'
import {
  Inquiry,
  InquirySummary,
  InquiryResponse,
  InquiryListResponse,
  CreateInquiryRequest,
  UpdateInquiryRequest,
  AssignInquiryRequest,
  ResolveInquiryRequest,
  InquiryListParams,
  InquiryStats,
  InquiryStatsResponse,
  InquiryActivity,
  InquiryActivityResponse,
  InquiryComment,
  InquiryCommentsResponse,
} from '@/types/api/inquiries'

class InquiryService {
  /**
   * Get inquiries list with pagination and filtering
   */
  async getInquiries(
    params: InquiryListParams = {}
  ): Promise<InquiryListResponse['data']> {
    try {
      // Get precinct info for filtering
      const precinctInfo = await authService.getPrecinct()

      // Build query parameters
      const queryParams = {
        limit: params.limit || 20,
        precinct_id: params.precinct_id || precinctInfo?.precinct_id,
        ...params,
      }

      const response: AxiosResponse<InquiryListResponse> = await apiClient.get(
        API_ENDPOINTS.INQUIRIES,
        { params: queryParams }
      )

      return response.data.data
    } catch (error) {
      throw handleApiError(error, 'getInquiries')
    }
  }

  /**
   * Get inquiry by ID
   */
  async getInquiry(inquiryId: string): Promise<Inquiry> {
    try {
      const response: AxiosResponse<InquiryResponse> = await apiClient.get(
        API_ENDPOINTS.INQUIRY_BY_ID(inquiryId)
      )

      return response.data.data.inquiry
    } catch (error) {
      throw handleApiError(error, 'getInquiry')
    }
  }

  /**
   * Create a new inquiry
   */
  async createInquiry(data: CreateInquiryRequest): Promise<Inquiry> {
    try {
      const response: AxiosResponse<InquiryResponse> = await apiClient.post(
        API_ENDPOINTS.INQUIRIES,
        data
      )

      return response.data.data.inquiry
    } catch (error) {
      throw handleApiError(error, 'createInquiry')
    }
  }

  /**
   * Update an existing inquiry
   */
  async updateInquiry(
    inquiryId: string,
    data: UpdateInquiryRequest
  ): Promise<Inquiry> {
    try {
      const response: AxiosResponse<InquiryResponse> = await apiClient.put(
        API_ENDPOINTS.INQUIRY_BY_ID(inquiryId),
        data
      )

      return response.data.data.inquiry
    } catch (error) {
      throw handleApiError(error, 'updateInquiry')
    }
  }

  /**
   * Assign inquiry to a user
   */
  async assignInquiry(
    inquiryId: string,
    data: AssignInquiryRequest
  ): Promise<Inquiry> {
    try {
      const response: AxiosResponse<InquiryResponse> = await apiClient.put(
        `${API_ENDPOINTS.INQUIRY_BY_ID(inquiryId)}/assign`,
        data
      )

      return response.data.data.inquiry
    } catch (error) {
      throw handleApiError(error, 'assignInquiry')
    }
  }

  /**
   * Resolve an inquiry
   */
  async resolveInquiry(
    inquiryId: string,
    data: ResolveInquiryRequest
  ): Promise<Inquiry> {
    try {
      const response: AxiosResponse<InquiryResponse> = await apiClient.put(
        `${API_ENDPOINTS.INQUIRY_BY_ID(inquiryId)}/resolve`,
        data
      )

      return response.data.data.inquiry
    } catch (error) {
      throw handleApiError(error, 'resolveInquiry')
    }
  }

  /**
   * Close an inquiry
   */
  async closeInquiry(inquiryId: string, notes?: string): Promise<Inquiry> {
    try {
      const response: AxiosResponse<InquiryResponse> = await apiClient.put(
        `${API_ENDPOINTS.INQUIRY_BY_ID(inquiryId)}/close`,
        { notes }
      )

      return response.data.data.inquiry
    } catch (error) {
      throw handleApiError(error, 'closeInquiry')
    }
  }

  /**
   * Delete an inquiry
   */
  async deleteInquiry(inquiryId: string): Promise<void> {
    try {
      await apiClient.delete(API_ENDPOINTS.INQUIRY_BY_ID(inquiryId))
    } catch (error) {
      throw handleApiError(error, 'deleteInquiry')
    }
  }

  /**
   * Get inquiry statistics
   */
  async getInquiryStats(
    precinctId?: string,
    dateFrom?: string,
    dateTo?: string
  ): Promise<InquiryStats> {
    try {
      const precinctInfo = await authService.getPrecinct()

      const params = {
        precinct_id: precinctId || precinctInfo?.precinct_id,
        date_from: dateFrom,
        date_to: dateTo,
      }

      const response: AxiosResponse<InquiryStatsResponse> = await apiClient.get(
        `${API_ENDPOINTS.INQUIRIES}/stats`,
        { params }
      )

      return response.data.data
    } catch (error) {
      throw handleApiError(error, 'getInquiryStats')
    }
  }

  /**
   * Get inquiry activity log
   */
  async getInquiryActivity(inquiryId: string): Promise<InquiryActivity[]> {
    try {
      const response: AxiosResponse<InquiryActivityResponse> =
        await apiClient.get(
          `${API_ENDPOINTS.INQUIRY_BY_ID(inquiryId)}/activity`
        )

      return response.data.data.activities
    } catch (error) {
      throw handleApiError(error, 'getInquiryActivity')
    }
  }

  /**
   * Get inquiry comments
   */
  async getInquiryComments(inquiryId: string): Promise<InquiryComment[]> {
    try {
      const response: AxiosResponse<InquiryCommentsResponse> =
        await apiClient.get(
          `${API_ENDPOINTS.INQUIRY_BY_ID(inquiryId)}/comments`
        )

      return response.data.data.comments
    } catch (error) {
      throw handleApiError(error, 'getInquiryComments')
    }
  }

  /**
   * Add comment to inquiry
   */
  async addInquiryComment(
    inquiryId: string,
    content: string,
    attachments?: File[]
  ): Promise<InquiryComment> {
    try {
      const formData = new FormData()
      formData.append('content', content)

      if (attachments) {
        attachments.forEach((file, index) => {
          formData.append(`attachment_${index}`, file)
        })
      }

      const response: AxiosResponse<{ data: { comment: InquiryComment } }> =
        await apiClient.post(
          `${API_ENDPOINTS.INQUIRY_BY_ID(inquiryId)}/comments`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          }
        )

      return response.data.data.comment
    } catch (error) {
      throw handleApiError(error, 'addInquiryComment')
    }
  }

  /**
   * Search inquiries
   */
  async searchInquiries(
    query: string,
    filters?: Partial<InquiryListParams>
  ): Promise<InquirySummary[]> {
    try {
      const precinctInfo = await authService.getPrecinct()

      const params = {
        q: query,
        precinct_id: precinctInfo?.precinct_id,
        limit: 50,
        ...filters,
      }

      const response: AxiosResponse<{ data: { inquiries: InquirySummary[] } }> =
        await apiClient.get(`${API_ENDPOINTS.INQUIRIES}/search`, { params })

      return response.data.data.inquiries
    } catch (error) {
      throw handleApiError(error, 'searchInquiries')
    }
  }

  /**
   * Get inquiries by status
   */
  async getInquiriesByStatus(
    status: string,
    limit: number = 10,
    page: number = 1
  ): Promise<InquiryListResponse['data']> {
    try {
      return this.getInquiries({
        status: [status as any],
        limit,
        page,
        sort_by: 'updated_at',
        sort_order: 'desc',
      })
    } catch (error) {
      throw handleApiError(error, 'getInquiriesByStatus')
    }
  }

  /**
   * Get recent inquiries
   */
  async getRecentInquiries(limit: number = 10): Promise<InquirySummary[]> {
    try {
      const result = await this.getInquiries({
        limit,
        page: 1,
        sort_by: 'created_at',
        sort_order: 'desc',
      })

      return result.items
    } catch (error) {
      throw handleApiError(error, 'getRecentInquiries')
    }
  }

  /**
   * Get active inquiries (pending, in_progress)
   */
  async getActiveInquiries(): Promise<InquirySummary[]> {
    try {
      const result = await this.getInquiries({
        status: ['pending', 'in_progress'],
        limit: 100,
        sort_by: 'priority',
        sort_order: 'desc',
      })

      return result.items
    } catch (error) {
      throw handleApiError(error, 'getActiveInquiries')
    }
  }

  /**
   * Validate inquiry data before submission
   */
  validateInquiryData(
    data: CreateInquiryRequest | UpdateInquiryRequest
  ): string[] {
    const errors: string[] = []

    if ('title' in data && data.title) {
      if (!data.title.trim()) {
        errors.push('Title is required')
      }
      if (data.title.length > 200) {
        errors.push('Title must be less than 200 characters')
      }
    }

    if ('description' in data && data.description) {
      if (!data.description.trim()) {
        errors.push('Description is required')
      }
      if (data.description.length > 5000) {
        errors.push('Description must be less than 5000 characters')
      }
    }

    if ('contact_info' in data && data.contact_info) {
      const contact = data.contact_info
      if (!contact.name?.trim()) {
        errors.push('Contact name is required')
      }
      if (!contact.phone?.trim()) {
        errors.push('Contact phone is required')
      }
      if (contact.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(contact.email)) {
        errors.push('Invalid email format')
      }
    }

    return errors
  }
}

// Export singleton instance
export const inquiryService = new InquiryService()
export default inquiryService
