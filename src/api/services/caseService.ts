/**
 * Cases API Service
 *
 * API service for case management operations
 */

import type { AxiosResponse } from 'axios'
import { apiClient } from '../client'
import { API_ENDPOINTS } from '@/config/api'
import { handleApiError } from '@/utils/apiErrorHandling'
import { authService } from './authService'
import {
  Case,
  CaseSummary,
  CaseResponse,
  CaseListResponse,
  CaseMetrics,
  CaseMetricsResponse,
  DistanceMatrixResponse,
  DistanceMatrixRow,
  IncidentReport,
  IncidentReportResponse,
  CaseResponder,
  CaseRespondersResponse,
  CreateCaseRequest,
  UpdateCaseRequest,
  ApproveCaseRequest,
  CloseCaseRequest,
  CaseUploadRequest,
  CaseUploadResponse,
  CaseListParams,
  DashboardCaseStats,
  CaseActivity,
  CaseTimeline,
} from '@/types/api/cases'

class CaseService {
  /**
   * Get case by ID
   */

  private getPrecinctParam(): { precinct_id?: string } {
    try {
      // Example: read precinctId from localStorage (or anywhere else)
      const precinctId = localStorage.getItem('precinctId')
      if (precinctId) {
        return { precinct_id: precinctId }
      }
      return {}
    } catch (error) {
      console.error('getPrecinctParam error:', error)
      return {}
    }
  }

  async getCase(caseId: string): Promise<Case> {
    try {
      const response: AxiosResponse<CaseResponse> = await apiClient.get(
        API_ENDPOINTS.CASE_BY_ID(caseId)
      )
      return response.data.data.case
    } catch (error) {
      throw handleApiError(error, 'getCase')
    }
  }

  async listCases(
    params: {
      startDate?: string
      endDate?: string
      limit?: number
      page?: number
      precinct_id?: string
      [key: string]: any
    } = {}
  ): Promise<any> {
    console.log('listCases CALLED with params:', params)

    try {
      const precinctParam = this.getPrecinctParam()
      console.log('precinctParam:', precinctParam)

      const queryParams = {
        entity: 'cases',
        task: 'trim',
        skip_cache: true,
        req_source: 'admin',
        numberPerPage: params.limit || 10,
        pageNumber: params.page || 1,
        ...precinctParam,
        startDate: params.startDate,
        endDate: params.endDate,
        ...params,
      }

      console.log('Calling listCases with query:', queryParams)

      // Put a log before the request
      console.log('About to call apiClient.get...')
      const response = await apiClient.get('cases', { params: queryParams })

      // If we get here, request succeeded
      console.log('responseeeee', response)
      return response.data
    } catch (error) {
      console.error('listCases error caught:', error)
      throw handleApiError(error, 'listCases')
    }
  }

  /**
   * Get cases list with pagination and filtering
   */
  async getCases(
    params: CaseListParams = {}
  ): Promise<CaseListResponse['data']> {
    try {
      // Get precinct info for filtering
      const precinctInfo = await authService.getPrecinct()

      // Build query parameters
      const queryParams = {
        entity: 'cases',
        task: 'trim',
        skip_cache: true,
        numberPerPage: params.limit || 10,
        pageNumber: params.page || 1,
        precinct_id: params.precinct_id || precinctInfo?.precinct_id,
        ...params,
      }

      const response: AxiosResponse<CaseListResponse> = await apiClient.get(
        API_ENDPOINTS.ENTITIES,
        { params: queryParams }
      )

      return response.data.data
    } catch (error) {
      throw handleApiError(error, 'getCases')
    }
  }

  /**
   * Create a new case
   */
  async createCase(data: CreateCaseRequest): Promise<Case> {
    try {
      const response: AxiosResponse<CaseResponse> = await apiClient.post(
        API_ENDPOINTS.CASES,
        data
      )

      return response.data.data.case
    } catch (error) {
      throw handleApiError(error, 'createCase')
    }
  }

  /**
   * Update an existing case
   */
  async updateCase(caseId: string, data: UpdateCaseRequest): Promise<Case> {
    try {
      const response: AxiosResponse<CaseResponse> = await apiClient.put(
        API_ENDPOINTS.CASE_BY_ID(caseId),
        data
      )

      return response.data.data.case
    } catch (error) {
      throw handleApiError(error, 'updateCase')
    }
  }

  /**
   * Approve a case
   */
  async approveCase(caseId: string, data: ApproveCaseRequest): Promise<Case> {
    try {
      const response: AxiosResponse<CaseResponse> = await apiClient.post(
        API_ENDPOINTS.CASE_APPROVE(caseId),
        data
      )
      return response.data.data.case
    } catch (error) {
      throw handleApiError(error, 'approveCase')
    }
  }

  /**
   * Close a case
   */
  async closeCase(caseId: string, data: CloseCaseRequest): Promise<Case> {
    try {
      const response: AxiosResponse<CaseResponse> = await apiClient.post(
        API_ENDPOINTS.CASE_CLOSE(caseId),
        data
      )

      return response.data.data.case
    } catch (error) {
      throw handleApiError(error, 'closeCase')
    }
  }

  /**
   * Upload multiple cases (bulk operation)
   */
  async uploadCases(
    data: CaseUploadRequest
  ): Promise<CaseUploadResponse['data']> {
    try {
      const response: AxiosResponse<CaseUploadResponse> = await apiClient.post(
        API_ENDPOINTS.CASE_UPLOAD,
        data
      )

      return response.data.data
    } catch (error) {
      throw handleApiError(error, 'uploadCases')
    }
  }

  /**
   * Initiate USSD case (make offline)
   */
  async initiateUssdCase(caseId: string): Promise<Case> {
    try {
      const response: AxiosResponse<CaseResponse> = await apiClient.put(
        API_ENDPOINTS.CASE_MAKE_OFFLINE(caseId)
      )

      return response.data.data.case
    } catch (error) {
      throw handleApiError(error, 'initiateUssdCase')
    }
  }

  /**
   * Get case statistics
   */
  async getDashboardCases(
    precinctId?: string,
    startDate?: string,
    stopDate?: string
  ): Promise<DashboardCaseStats> {
    try {
      const precinctInfo = await authService.getPrecinct()

      const params = {
        precinct_id: precinctId || precinctInfo?.precinct_id,
        startDate,
        stopDate,
        showdata: true,
      }

      const response: AxiosResponse<{ data: DashboardCaseStats }> =
        await apiClient.get(`${API_ENDPOINTS.CASES}/metrics`, { params })

      return response.data.data
    } catch (error) {
      throw handleApiError(error, 'getDashboardCases')
    }
  }

  /**
   * Get case activity log
   */
  async getCaseActivity(caseId: string): Promise<CaseActivity[]> {
    try {
      const response: AxiosResponse<{ data: { activities: CaseActivity[] } }> =
        await apiClient.get(`${API_ENDPOINTS.CASE_BY_ID(caseId)}/activity`)

      return response.data.data.activities
    } catch (error) {
      throw handleApiError(error, 'getCaseActivity')
    }
  }

  /**
   * Get case timeline
   */
  async getCaseTimeline(caseId: string): Promise<CaseTimeline> {
    try {
      const response: AxiosResponse<{ data: CaseTimeline }> =
        await apiClient.get(`${API_ENDPOINTS.CASE_BY_ID(caseId)}/timeline`)

      return response.data.data
    } catch (error) {
      throw handleApiError(error, 'getCaseTimeline')
    }
  }

  /**
   * Search cases
   */
  async searchCases(
    query: string,
    filters?: Partial<CaseListParams>
  ): Promise<CaseSummary[]> {
    try {
      const precinctInfo = await authService.getPrecinct()

      const params = {
        q: query,
        precinct_id: precinctInfo?.precinct_id,
        limit: 50,
        ...filters,
      }

      const response: AxiosResponse<{ data: { cases: CaseSummary[] } }> =
        await apiClient.get(`${API_ENDPOINTS.CASES}/search`, { params })

      return response.data.data.cases
    } catch (error) {
      throw handleApiError(error, 'searchCases')
    }
  }

  /**
   * Get distance matrix from Google Maps API
   */
  async getDistance(
    origin: string,
    destinations: string
  ): Promise<DistanceMatrixRow[]> {
    try {
      const API_KEY = import.meta.env.VITE_GOOGLE_MAP_API || ''

      if (!API_KEY) {
        throw new Error('Google Maps API key not configured')
      }

      const url = `https://maps.googleapis.com/maps/api/distancematrix/json?origins=${origin}&destinations=${destinations}&key=${API_KEY}`

      const response = await fetch(url, { method: 'GET' })
      const result: DistanceMatrixResponse = await response.json()

      if (result.status === 'OK') {
        return result.rows
      }

      const error = new Error('Distance matrix request failed')
      error.name = result.status
      throw error
    } catch (error) {
      throw handleApiError(error, 'getDistance')
    }
  }

  /**
   * Search all cases
   */
  async searchAllCases(searchParam: string, searchValue: string): Promise<any> {
    try {
      const precinctInfo = await authService.getPrecinct()

      const params: Record<string, string> = {
        search_field: searchParam,
        search_term: searchValue,
      }

      if (precinctInfo?.precinct_id) {
        params.precinct_id = precinctInfo.precinct_id
      }

      const response: AxiosResponse = await apiClient.get('cases', {
        params,
        validateStatus: () => true,
      })

      if (response.status !== 200) {
        throw new Error('Failed to fetch cases')
      }

      return response.data
    } catch (error) {
      throw handleApiError(error, 'searchAllCases')
    }
  }

  /**
   * Get incident report by case ID
   */
  async getIncidentReportByID(caseId: string): Promise<IncidentReport> {
    try {
      const response: AxiosResponse<IncidentReportResponse> =
        await apiClient.get('incident-reports', {
          params: { case_id: caseId },
        })

      if (response.status !== 200) {
        throw new Error('Failed to fetch incident report')
      }

      return response.data.data.incident_report
    } catch (error) {
      throw handleApiError(error, 'getIncidentReportByID')
    }
  }

  /**
   * Get case responders
   */
  async getCaseResponders(caseId: string): Promise<CaseResponder[]> {
    try {
      const response: AxiosResponse<CaseRespondersResponse> =
        await apiClient.get(`cases/${caseId}/responders`)

      return response.data.data.responders
    } catch (error) {
      throw handleApiError(error, 'getCaseResponders')
    }
  }

  /**
   * Get case metrics
   */
  async getCasesMetrics(): Promise<CaseMetrics> {
    try {
      const precinctInfo = await authService.getPrecinct()

      const params: Record<string, string> = {}

      if (precinctInfo?.precinct_id) {
        params.precinct_id = precinctInfo.precinct_id
      }

      const response: AxiosResponse<CaseMetricsResponse> = await apiClient.get(
        'cases/metrics',
        {
          params,
          validateStatus: () => true,
        }
      )

      if (response.status !== 200) {
        throw new Error('Failed to fetch case metrics')
      }

      return response.data.data
    } catch (error) {
      throw handleApiError(error, 'getCasesMetrics')
    }
  }

  /**
   * Get cases by status
   */
  async getCasesByStatus(
    status: string,
    limit: number = 10,
    page: number = 1
  ): Promise<CaseListResponse['data']> {
    try {
      return this.getCases({
        status: [status as any],
        limit,
        page,
        sort_by: 'updated_at',
        sort_order: 'desc',
      })
    } catch (error) {
      throw handleApiError(error, 'getCasesByStatus')
    }
  }

  /**
   * Get recent cases
   */
  async getRecentCases(limit: number = 10): Promise<CaseSummary[]> {
    try {
      const result = await this.getCases({
        limit,
        page: 1,
        sort_by: 'created_at',
        sort_order: 'desc',
      })

      return result.items
    } catch (error) {
      throw handleApiError(error, 'getRecentCases')
    }
  }

  /**
   * Get active cases (pending, approved, in_progress)
   */
  async getActiveCases(): Promise<CaseSummary[]> {
    try {
      const result = await this.getCases({
        status: ['pending', 'approved', 'in_progress'],
        limit: 100,
        sort_by: 'priority',
        sort_order: 'desc',
      })

      return result.items
    } catch (error) {
      throw handleApiError(error, 'getActiveCases')
    }
  }

  /**
   * Validate case data before submission
   */
  validateCaseData(data: CreateCaseRequest | UpdateCaseRequest): string[] {
    const errors: string[] = []

    if ('patient' in data && data.patient) {
      if (
        !(data.patient.first_name?.trim() || data.patient.last_name?.trim())
      ) {
        errors.push('Patient name is required')
      }
      if (!data.patient.gender) {
        errors.push('Patient gender is required')
      }
    }

    if ('pickup_location' in data && data.pickup_location) {
      if (!data.pickup_location.address?.trim()) {
        errors.push('Pickup location address is required')
      }
    }

    if ('medical_assessment' in data && data.medical_assessment) {
      if (!data.medical_assessment.chief_complaint?.trim()) {
        errors.push('Chief complaint is required')
      }
      if (!data.medical_assessment.severity_level) {
        errors.push('Severity level is required')
      }
    }

    return errors
  }
}

// Export singleton instance
export const caseService = new CaseService()
export default caseService
