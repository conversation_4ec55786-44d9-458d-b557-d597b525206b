/**
 * Ambulance Services API Service
 *
 * API service for ambulance provider and pricing operations
 */

import type { AxiosResponse } from 'axios'
import { apiClient } from '../client'
import { API_ENDPOINTS } from '@/config/api'
import { handleApiError } from '@/utils/apiErrorHandling'
import { authService } from './authService'
import {
  AmbulanceProvider,
  AmbulanceProviderSummary,
  AmbulanceProviderResponse,
  AmbulanceProviderListResponse,
  AmbulanceRequest,
  AmbulanceRequestsResponse,
  AmbulanceRequestResponse,
  CreateAmbulanceRequestData,
  UpdateAmbulanceRequestData,
  UpdateDropoffLocationData,
  AmbulanceMetrics,
  AmbulanceMetricsResponse,
  AmbulancePricingRequest,
  AmbulancePricingResponse,
  PricingBreakdown,
  ProviderListParams,
  ProviderStats,
  VehicleAvailability,
  VehicleAvailabilityResponse,
  ProviderStatsResponse,
} from '@/types/api/ambulance'

class AmbulanceService {
  /**
   * Get ambulance providers list with pagination and filtering
   */
  async getAmbulanceProviders(
    params: ProviderListParams = {}
  ): Promise<AmbulanceProviderListResponse['data']> {
    try {
      // Get precinct info for filtering
      const precinctInfo = await authService.getPrecinct()

      // Build query parameters
      const queryParams = {
        limit: params.limit || 20,
        precinct_id: params.precinct_id || precinctInfo?.precinct_id,
        ...params,
      }

      const response: AxiosResponse<AmbulanceProviderListResponse> =
        await apiClient.get(API_ENDPOINTS.AMBULANCE_PROVIDERS, {
          params: queryParams,
        })

      return response.data.data
    } catch (error) {
      throw handleApiError(error, 'getAmbulanceProviders')
    }
  }

  /**
   * Get ambulance provider by ID
   */
  async getAmbulanceProvider(providerId: string): Promise<AmbulanceProvider> {
    try {
      const response: AxiosResponse<AmbulanceProviderResponse> =
        await apiClient.get(
          `${API_ENDPOINTS.AMBULANCE_PROVIDERS}/${providerId}`
        )

      return response.data.data.provider
    } catch (error) {
      throw handleApiError(error, 'getAmbulanceProvider')
    }
  }

  /**
   * Get ambulance requests
   */
  async getAmbulanceRequests(
    pageNumber: number = 1,
    numberPerPage: number = 10
  ): Promise<AmbulanceRequestsResponse['data']> {
    try {
      const precinctInfo = await authService.getPrecinct()

      const params: Record<string, string | number> = {
        entity: 'ambulance-requests',
        task: 'all',
        numberPerPage,
        pageNumber,
      }

      if (precinctInfo?.precinct_id) {
        params.precinct_id = precinctInfo.precinct_id
      }

      const response: AxiosResponse<AmbulanceRequestsResponse> =
        await apiClient.get(API_ENDPOINTS.ENTITIES, {
          params,
          validateStatus: () => true,
        })

      if (response.status !== 200) {
        throw new Error('Failed to fetch ambulance requests')
      }

      return response.data.data
    } catch (error) {
      throw handleApiError(error, 'getAmbulanceRequests')
    }
  }

  /**
   * Get ambulance request by ID
   */
  async getAmbulanceRequestByID(
    ambulanceRequestId: string
  ): Promise<AmbulanceRequest> {
    try {
      const response: AxiosResponse<AmbulanceRequestResponse> =
        await apiClient.get('ambulance-requests', {
          params: {
            status: 'get_ambulance_request',
            id: ambulanceRequestId,
          },
        })

      if (response.status !== 200) {
        throw new Error('Failed to fetch ambulance request')
      }

      return response.data.data.ambulance_request
    } catch (error) {
      throw handleApiError(error, 'getAmbulanceRequestByID')
    }
  }

  /**
   * Create ambulance request
   */
  async createAmbulanceRequest(
    requestData: CreateAmbulanceRequestData
  ): Promise<AmbulanceRequest> {
    try {
      const precinctInfo = await authService.getPrecinct()

      const payload = {
        ...requestData,
        precinct_id: precinctInfo?.precinct_id,
      }

      const response: AxiosResponse<AmbulanceRequestResponse> =
        await apiClient.post('ambulance-requests', payload, {
          params: { status: 'create' },
          validateStatus: () => true,
        })

      if (response.status !== 200) {
        throw new Error('Failed to create ambulance request')
      }

      return response.data.data.ambulance_request
    } catch (error) {
      throw handleApiError(error, 'createAmbulanceRequest')
    }
  }

  /**
   * Update ambulance request
   */
  async updateAmbulanceRequest(
    ambulanceRequestId: string,
    requestData: UpdateAmbulanceRequestData
  ): Promise<AmbulanceRequest> {
    try {
      const precinctInfo = await authService.getPrecinct()

      const payload = {
        ...requestData,
        precinct_id: precinctInfo?.precinct_id,
      }

      const response: AxiosResponse<AmbulanceRequestResponse> =
        await apiClient.put('ambulance-requests', payload, {
          params: {
            status: 'update',
            id: ambulanceRequestId,
          },
        })

      return response.data.data.ambulance_request
    } catch (error) {
      throw handleApiError(error, 'updateAmbulanceRequest')
    }
  }

  /**
   * Approve ambulance request
   */
  async approveAmbulanceRequest(
    ambulanceRequestId: string,
    ambulanceProviderId: string
  ): Promise<AmbulanceRequest> {
    try {
      const response: AxiosResponse<AmbulanceRequestResponse> =
        await apiClient.post(
          'ambulance-requests',
          { ambulance_provider_id: ambulanceProviderId },
          {
            params: {
              status: 'approve',
              id: ambulanceRequestId,
            },
          }
        )

      return response.data.data.ambulance_request
    } catch (error) {
      throw handleApiError(error, 'approveAmbulanceRequest')
    }
  }

  /**
   * Update ambulance dropoff location
   */
  async updateAmbulanceDropoffLocation(
    ambulanceRequestId: string,
    caseId: string,
    dropoffLocationData: UpdateDropoffLocationData
  ): Promise<AmbulanceRequest> {
    try {
      const response: AxiosResponse<AmbulanceRequestResponse> =
        await apiClient.put('ambulance-requests', dropoffLocationData, {
          params: {
            status: 'update_dropoff_location',
            id: ambulanceRequestId,
            case_id: caseId,
          },
        })

      return response.data.data.ambulance_request
    } catch (error) {
      throw handleApiError(error, 'updateAmbulanceDropoffLocation')
    }
  }

  /**
   * Update ambulance payment status
   */
  async updateAmbulancePayment(
    ambulanceRequestId: string,
    paymentStatus: 'requires_payment' | 'confirmed'
  ): Promise<AmbulanceRequest> {
    try {
      let status: string
      if (paymentStatus === 'requires_payment') {
        status = 'unconfirm_payment'
      } else if (paymentStatus === 'confirmed') {
        status = 'confirm_payment'
      } else {
        throw new Error('Invalid payment status')
      }

      const response: AxiosResponse<AmbulanceRequestResponse> =
        await apiClient.post('ambulance-requests', null, {
          params: {
            status,
            id: ambulanceRequestId,
          },
        })

      return response.data.data.ambulance_request
    } catch (error) {
      throw handleApiError(error, 'updateAmbulancePayment')
    }
  }

  /**
   * Cancel or restore ambulance request
   */
  async cancelAmbulanceRequest(
    ambulanceRequestId: string,
    requestData: { reason?: string; notes?: string },
    requestStatus: 'open' | 'cancelled'
  ): Promise<AmbulanceRequest> {
    try {
      let status: string
      if (requestStatus === 'open') {
        status = 'cancel'
      } else {
        status = 'restore'
      }

      const response: AxiosResponse<AmbulanceRequestResponse> =
        await apiClient.post('ambulance-requests', requestData, {
          params: {
            status,
            id: ambulanceRequestId,
          },
          validateStatus: () => true,
        })

      if (response.status !== 200) {
        throw new Error('Failed to cancel ambulance request')
      }

      return response.data.data.ambulance_request
    } catch (error) {
      throw handleApiError(error, 'cancelAmbulanceRequest')
    }
  }

  /**
   * Get ambulance metrics
   */
  async getAmbulanceMetrics(): Promise<AmbulanceMetrics> {
    try {
      const precinctInfo = await authService.getPrecinct()

      const params: Record<string, string> = {
        status: 'get_ambulance_metrics',
      }

      if (precinctInfo?.precinct_id) {
        params.precinct_id = precinctInfo.precinct_id
      }

      const response: AxiosResponse<AmbulanceMetricsResponse> =
        await apiClient.get('ambulance-requests', {
          params,
          validateStatus: () => true,
        })

      if (response.status !== 200) {
        throw new Error('Failed to fetch ambulance metrics')
      }

      return response.data.data
    } catch (error) {
      throw handleApiError(error, 'getAmbulanceMetrics')
    }
  }

  /**
   * Get ambulance pricing
   */
  async getAmbulancePricing(
    request: AmbulancePricingRequest
  ): Promise<AmbulancePricingResponse['data']> {
    try {
      // Build query parameters from request
      const queryParams = {
        request_category: request.request_category,
        request_type: request.request_type,
        covid_case: request.covid_case,
        requires_oxygen: request.requires_oxygen,
        distance_km: request.distance_km,
        estimated_duration_minutes: request.estimated_duration_minutes,
        service_level: request.service_level,
      }

      const response: AxiosResponse<AmbulancePricingResponse> =
        await apiClient.get(API_ENDPOINTS.AMBULANCE_PRICING, {
          params: queryParams,
        })

      return response.data.data
    } catch (error) {
      throw handleApiError(error, 'getAmbulancePricing')
    }
  }

  /**
   * Get detailed pricing with location-based calculations
   */
  async getDetailedPricing(
    request: AmbulancePricingRequest
  ): Promise<PricingBreakdown[]> {
    try {
      const response: AxiosResponse<AmbulancePricingResponse> =
        await apiClient.post(
          `${API_ENDPOINTS.AMBULANCE_PRICING}/detailed`,
          request
        )

      return response.data.data.pricing_options
    } catch (error) {
      throw handleApiError(error, 'getDetailedPricing')
    }
  }

  /**
   * Get provider statistics
   */
  async getProviderStats(
    precinctId?: string,
    dateFrom?: string,
    dateTo?: string
  ): Promise<ProviderStats> {
    try {
      const precinctInfo = await authService.getPrecinct()

      const params = {
        precinct_id: precinctId || precinctInfo?.precinct_id,
        date_from: dateFrom,
        date_to: dateTo,
      }

      const response: AxiosResponse<ProviderStatsResponse> =
        await apiClient.get(`${API_ENDPOINTS.AMBULANCE_PROVIDERS}/stats`, {
          params,
        })

      return response.data.data
    } catch (error) {
      throw handleApiError(error, 'getProviderStats')
    }
  }

  /**
   * Get vehicle availability
   */
  async getVehicleAvailability(
    location?: { latitude: number; longitude: number },
    radiusKm: number = 50
  ): Promise<VehicleAvailability[]> {
    try {
      const params = {
        latitude: location?.latitude,
        longitude: location?.longitude,
        radius_km: radiusKm,
      }

      const response: AxiosResponse<VehicleAvailabilityResponse> =
        await apiClient.get(
          `${API_ENDPOINTS.AMBULANCE_PROVIDERS}/availability`,
          { params }
        )

      return response.data.data.availability
    } catch (error) {
      throw handleApiError(error, 'getVehicleAvailability')
    }
  }

  /**
   * Search ambulance providers
   */
  async searchProviders(
    query: string,
    filters?: Partial<ProviderListParams>
  ): Promise<AmbulanceProviderSummary[]> {
    try {
      const precinctInfo = await authService.getPrecinct()

      const params = {
        q: query,
        precinct_id: precinctInfo?.precinct_id,
        limit: 50,
        ...filters,
      }

      const response: AxiosResponse<{
        data: { providers: AmbulanceProviderSummary[] }
      }> = await apiClient.get(`${API_ENDPOINTS.AMBULANCE_PROVIDERS}/search`, {
        params,
      })

      return response.data.data.providers
    } catch (error) {
      throw handleApiError(error, 'searchProviders')
    }
  }

  /**
   * Get active providers
   */
  async getActiveProviders(
    limit: number = 50
  ): Promise<AmbulanceProviderSummary[]> {
    try {
      const result = await this.getAmbulanceProviders({
        status: ['active'],
        has_available_vehicles: true,
        limit,
        sort_by: 'response_time',
        sort_order: 'asc',
      })

      return result.ambulance_providers
    } catch (error) {
      throw handleApiError(error, 'getActiveProviders')
    }
  }

  /**
   * Get providers by service level
   */
  async getProvidersByServiceLevel(
    serviceLevel: string,
    limit: number = 20
  ): Promise<AmbulanceProviderSummary[]> {
    try {
      const result = await this.getAmbulanceProviders({
        service_level: [serviceLevel as any],
        status: ['active'],
        limit,
        sort_by: 'success_rate',
        sort_order: 'desc',
      })

      return result.ambulance_providers
    } catch (error) {
      throw handleApiError(error, 'getProvidersByServiceLevel')
    }
  }

  /**
   * Get nearest providers
   */
  async getNearestProviders(
    location: { latitude: number; longitude: number },
    limit: number = 10
  ): Promise<AmbulanceProviderSummary[]> {
    try {
      const params = {
        latitude: location.latitude,
        longitude: location.longitude,
        limit,
        status: ['active'],
        has_available_vehicles: true,
        sort_by: 'distance',
        sort_order: 'asc',
      }

      const response: AxiosResponse<{
        data: { providers: AmbulanceProviderSummary[] }
      }> = await apiClient.get(`${API_ENDPOINTS.AMBULANCE_PROVIDERS}/nearest`, {
        params,
      })

      return response.data.data.providers
    } catch (error) {
      throw handleApiError(error, 'getNearestProviders')
    }
  }

  /**
   * Get cached pricing data
   */
  async getCachedPricing(
    requestCategory: string,
    isCovidCase: boolean,
    requiresOxygen: boolean
  ): Promise<PricingBreakdown[]> {
    try {
      // Check localStorage for cached pricing
      const cacheKey = `ambulancePricing_${requestCategory}_${isCovidCase}_${requiresOxygen}`
      const cachedData = localStorage.getItem(cacheKey)

      if (cachedData) {
        try {
          const parsed = JSON.parse(cachedData)
          // Return cached data if it's recent (less than 10 minutes old)
          if (parsed.timestamp && Date.now() - parsed.timestamp < 600000) {
            return parsed.data
          }
        } catch (parseError) {
          console.warn('Failed to parse cached pricing data:', parseError)
        }
      }

      // Fetch fresh data from API
      const request: AmbulancePricingRequest = {
        request_category: requestCategory as any,
        request_type: 'ambulance',
        covid_case: isCovidCase,
        requires_oxygen: requiresOxygen,
      }

      const result = await this.getAmbulancePricing(request)

      // Cache the response with timestamp
      const cacheData = {
        data: result.pricing_options,
        timestamp: Date.now(),
      }
      localStorage.setItem(cacheKey, JSON.stringify(cacheData))

      return result.pricing_options
    } catch (error) {
      throw handleApiError(error, 'getCachedPricing')
    }
  }

  /**
   * Clear pricing cache
   */
  clearPricingCache(): void {
    try {
      // Clear pricing-related cache entries
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        if (key.startsWith('ambulancePricing_')) {
          localStorage.removeItem(key)
        }
      })

      console.log('Ambulance pricing cache cleared')
    } catch (error) {
      console.warn('Failed to clear pricing cache:', error)
    }
  }

  /**
   * Validate pricing request
   */
  validatePricingRequest(request: AmbulancePricingRequest): string[] {
    const errors: string[] = []

    if (!request.request_category) {
      errors.push('Request category is required')
    }

    if (!request.request_type) {
      errors.push('Request type is required')
    }

    if (request.covid_case === undefined) {
      errors.push('COVID case status is required')
    }

    if (request.requires_oxygen === undefined) {
      errors.push('Oxygen requirement status is required')
    }

    if (request.distance_km && request.distance_km < 0) {
      errors.push('Distance must be a positive number')
    }

    if (
      request.estimated_duration_minutes &&
      request.estimated_duration_minutes < 0
    ) {
      errors.push('Duration must be a positive number')
    }

    return errors
  }
}

// Export singleton instance
export const ambulanceService = new AmbulanceService()
export default ambulanceService
