/**
 * References API Service
 *
 * API service for reference data operations
 */

import type { AxiosResponse } from 'axios'
import { apiClient } from '../client'
import { API_ENDPOINTS } from '@/config/api'
import { handleApiError } from '@/utils/apiErrorHandling'
import {
  ReferencesResponse,
  ReferenceCategoryResponse,
  ReferenceItem,
  ReferenceCategory,
  ReferenceSearchParams,
  DiagnosisRecommendationRequest,
  DiagnosisRecommendationResponse,
  ReferenceStats,
  IncidentType,
  ProvisionalDiagnosis,
  ProbableCause,
  ActionTaken,
} from '@/types/api/references'

class ReferenceService {
  /**
   * Get all reference data
   */
  async getAllReferences(): Promise<ReferencesResponse['data']> {
    try {
      const response: AxiosResponse<ReferencesResponse> = await apiClient.get(
        `${API_ENDPOINTS.REFERENCES}?task=get_reference`
      )

      return response.data.data
    } catch (error) {
      throw handleApiError(error, 'getAllReferences')
    }
  }

  /**
   * Get references by category
   */
  async getReferencesByCategory<T = ReferenceItem>(
    category: ReferenceCategory
  ): Promise<ReferenceCategoryResponse<T>['data']> {
    try {
      const response: AxiosResponse<ReferenceCategoryResponse<T>> =
        await apiClient.get(API_ENDPOINTS.REFERENCES, {
          params: {
            task: 'get_category',
            category,
          },
        })

      return response.data.data
    } catch (error) {
      throw handleApiError(error, 'getReferencesByCategory')
    }
  }

  /**
   * Get incident types
   */
  async getIncidentTypes(): Promise<IncidentType[]> {
    try {
      const result =
        await this.getReferencesByCategory<IncidentType>('incident_types')
      return result.items
    } catch (error) {
      throw handleApiError(error, 'getIncidentTypes')
    }
  }

  /**
   * Get provisional diagnoses
   */
  async getProvisionalDiagnoses(): Promise<ProvisionalDiagnosis[]> {
    try {
      const response: AxiosResponse<
        ReferenceCategoryResponse<ProvisionalDiagnosis>
      > = await apiClient.get(
        `${API_ENDPOINTS.REFERENCES}?task=get_provisional_diagnisis`
      )

      return response.data.data.items
    } catch (error) {
      throw handleApiError(error, 'getProvisionalDiagnoses')
    }
  }

  /**
   * Get actions taken
   */
  async getActionsTaken(): Promise<ActionTaken[]> {
    try {
      const response: AxiosResponse<ReferenceCategoryResponse<ActionTaken>> =
        await apiClient.get(`${API_ENDPOINTS.REFERENCES}?task=get_action_taken`)

      return response.data.data.items
    } catch (error) {
      throw handleApiError(error, 'getActionsTaken')
    }
  }

  /**
   * Get probable causes for a diagnosis
   */
  async getProbableCauses(
    provisionalDiagnosisCode: string
  ): Promise<ProbableCause[]> {
    try {
      const response: AxiosResponse<ReferenceCategoryResponse<ProbableCause>> =
        await apiClient.get(API_ENDPOINTS.REFERENCES, {
          params: {
            task: 'get_probable_cause',
            provisional_diag: provisionalDiagnosisCode,
          },
        })

      return response.data.data.items
    } catch (error) {
      throw handleApiError(error, 'getProbableCauses')
    }
  }

  /**
   * Get diagnosis recommendations
   */
  async getDiagnosisRecommendations(
    request: DiagnosisRecommendationRequest
  ): Promise<DiagnosisRecommendationResponse['data']> {
    try {
      const response: AxiosResponse<DiagnosisRecommendationResponse> =
        await apiClient.post(API_ENDPOINTS.RECOMMENDATION_DIAGNOSIS, request)

      return response.data.data
    } catch (error) {
      throw handleApiError(error, 'getDiagnosisRecommendations')
    }
  }

  /**
   * Search references
   */
  async searchReferences(
    params: ReferenceSearchParams
  ): Promise<ReferenceItem[]> {
    try {
      const response: AxiosResponse<{ data: { items: ReferenceItem[] } }> =
        await apiClient.get(`${API_ENDPOINTS.REFERENCES}/search`, { params })

      return response.data.data.items
    } catch (error) {
      throw handleApiError(error, 'searchReferences')
    }
  }

  /**
   * Get reference statistics
   */
  async getReferenceStats(): Promise<ReferenceStats> {
    try {
      const response: AxiosResponse<{ data: ReferenceStats }> =
        await apiClient.get(`${API_ENDPOINTS.REFERENCES}/stats`)

      return response.data.data
    } catch (error) {
      throw handleApiError(error, 'getReferenceStats')
    }
  }

  /**
   * Get reference item by ID
   */
  async getReferenceItem(id: string): Promise<ReferenceItem> {
    try {
      const response: AxiosResponse<{ data: { item: ReferenceItem } }> =
        await apiClient.get(`${API_ENDPOINTS.REFERENCES}/${id}`)

      return response.data.data.item
    } catch (error) {
      throw handleApiError(error, 'getReferenceItem')
    }
  }

  /**
   * Get cached references with fallback to API
   */
  async getCachedReferences(): Promise<ReferencesResponse['data']> {
    try {
      // Check localStorage for cached references
      const cachedData = localStorage.getItem('referenceData')
      if (cachedData) {
        try {
          const parsed = JSON.parse(cachedData)
          // Return cached data if it's recent (less than 1 hour old)
          if (parsed.timestamp && Date.now() - parsed.timestamp < 3600000) {
            return parsed.data
          }
        } catch (parseError) {
          console.warn('Failed to parse cached reference data:', parseError)
        }
      }

      // Fetch fresh data from API
      const freshData = await this.getAllReferences()

      // Cache the response with timestamp
      const cacheData = {
        data: freshData,
        timestamp: Date.now(),
      }
      localStorage.setItem('referenceData', JSON.stringify(cacheData))

      return freshData
    } catch (error) {
      throw handleApiError(error, 'getCachedReferences')
    }
  }

  /**
   * Get specific reference category with caching
   */
  async getCachedReferenceCategory<T = ReferenceItem>(
    category: ReferenceCategory
  ): Promise<T[]> {
    try {
      // Check localStorage for cached category data
      const cacheKey = `referenceData_${category}`
      const cachedData = localStorage.getItem(cacheKey)

      if (cachedData) {
        try {
          const parsed = JSON.parse(cachedData)
          // Return cached data if it's recent (less than 30 minutes old)
          if (parsed.timestamp && Date.now() - parsed.timestamp < 1800000) {
            return parsed.data
          }
        } catch (parseError) {
          console.warn(`Failed to parse cached ${category} data:`, parseError)
        }
      }

      // Fetch fresh data from API
      const result = await this.getReferencesByCategory<T>(category)

      // Cache the response with timestamp
      const cacheData = {
        data: result.items,
        timestamp: Date.now(),
      }
      localStorage.setItem(cacheKey, JSON.stringify(cacheData))

      return result.items
    } catch (error) {
      throw handleApiError(error, 'getCachedReferenceCategory')
    }
  }

  /**
   * Clear reference cache
   */
  clearCache(): void {
    try {
      // Clear main reference cache
      localStorage.removeItem('referenceData')

      // Clear category-specific caches
      const categories: ReferenceCategory[] = [
        'incident_types',
        'provisional_diagnosis',
        'probable_causes',
        'actions_taken',
        'severity_levels',
        'case_categories',
        'responder_types',
        'equipment_types',
        'medication_types',
        'facility_types',
      ]

      categories.forEach(category => {
        localStorage.removeItem(`referenceData_${category}`)
      })

      console.log('Reference cache cleared')
    } catch (error) {
      console.warn('Failed to clear reference cache:', error)
    }
  }

  /**
   * Validate reference data structure
   */
  validateReferenceData(data: any): boolean {
    try {
      if (!data || typeof data !== 'object') {
        return false
      }

      // Check for required structure
      const requiredFields = ['references', 'last_updated', 'version']
      const hasRequiredFields = requiredFields.every(field => field in data)

      if (!hasRequiredFields) {
        return false
      }

      // Check references structure
      const references = data.references
      if (!references || typeof references !== 'object') {
        return false
      }

      // Validate at least some categories exist
      const expectedCategories = [
        'incident_types',
        'provisional_diagnosis',
        'actions_taken',
      ]
      const hasCategories = expectedCategories.some(
        category =>
          Array.isArray(references[category]) && references[category].length > 0
      )

      return hasCategories
    } catch (error) {
      console.error('Error validating reference data:', error)
      return false
    }
  }

  /**
   * Get reference item by code
   */
  findReferenceByCode(
    references: ReferencesResponse['data'],
    category: ReferenceCategory,
    code: string
  ): ReferenceItem | null {
    try {
      const categoryData = references.references[category]
      if (!Array.isArray(categoryData)) {
        return null
      }

      return categoryData.find(item => item.code === code) || null
    } catch (error) {
      console.error('Error finding reference by code:', error)
      return null
    }
  }
}

// Export singleton instance
export const referenceService = new ReferenceService()
export default referenceService
