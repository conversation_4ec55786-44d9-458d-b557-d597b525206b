/**
 * API Client
 *
 * Axios-based API client with interceptors, error handling, and authentication
 */

import axios from 'axios'
import type {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  CancelTokenSource,
} from 'axios'
import { fetchAuthSession } from 'aws-amplify/auth'
import { apiConfig } from '@/config/api'
import { useAuthStore } from '@/store/authStore'
import { captureException, addBreadcrumb } from '@/config/sentry'
import { ApiError } from '@/types/api/common'

class ApiClient {
  private client: AxiosInstance
  private cancelTokenSource: CancelTokenSource

  constructor() {
    this.cancelTokenSource = axios.CancelToken.source()

    // Create axios instance
    this.client = axios.create({
      baseURL: apiConfig.baseUrl,
      timeout: apiConfig.timeout,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    this.setupInterceptors()
  }

  private setupInterceptors(): void {
    // Request interceptor for authentication
    this.client.interceptors.request.use(
      async config => {
        try {
          // Add cancel token to all requests
          config.cancelToken = this.cancelTokenSource.token

          // Skip auth for certain endpoints
          if (config.headers?.['skip-auth']) {
            delete config.headers['skip-auth']
            return config
          }

          // Get auth state from Zustand store
          const authState = useAuthStore.getState()

          if (authState.isAuthenticated && authState.token) {
            // Use token from store first
            config.headers.Authorization = `Bearer ${authState.token}`

            // Extract issuer from token
            try {
              const payload = JSON.parse(atob(authState.token.split('.')[1]))
              if (payload.iss) {
                config.headers['X-Cognito-Issuer'] = payload.iss
              }
            } catch (tokenError) {
              console.warn('Failed to extract issuer from token:', tokenError)
            }
          } else {
            // Fallback to AWS Amplify session
            try {
              const session = await fetchAuthSession()
              if (session.tokens?.idToken) {
                const token = session.tokens.idToken.toString()
                config.headers.Authorization = `Bearer ${token}`

                // Extract issuer from token if needed
                const payload = JSON.parse(atob(token.split('.')[1]))
                if (payload.iss) {
                  config.headers['X-Cognito-Issuer'] = payload.iss
                }
              }
            } catch (sessionError) {
              console.warn('Failed to get auth session:', sessionError)
            }
          }

          // Add additional headers for CORS compatibility
          config.headers['Accept'] = 'application/json, text/plain, */*'
          config.headers['Accept-Language'] = 'en-US,en;q=0.9'

          return config
        } catch (error) {
          console.error('Request interceptor error:', error)
          return config
        }
      },
      error => {
        console.error('Request interceptor error:', error)
        return Promise.reject(error)
      }
    )

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        // Add breadcrumb for successful API calls
        addBreadcrumb(
          `API Success: ${response.config.method?.toUpperCase()} ${response.config.url}`,
          'http',
          'info',
          {
            status: response.status,
            url: response.config.url,
          }
        )

        return response
      },
      async error => {
        const originalRequest = error.config

        // Handle cancelled requests
        if (axios.isCancel(error)) {
          console.log('Request cancelled:', error.message)
          return Promise.reject(error)
        }

        // Log error in development
        if (apiConfig.enableLogging) {
          console.error(
            `❌ API Error: ${originalRequest?.method?.toUpperCase()} ${originalRequest?.url}`,
            {
              status: error.response?.status,
              message: error.message,
              data: error.response?.data,
            }
          )
        }

        // Handle authentication errors
        if (error.response?.status === 401) {
          await this.handleAuthError(error)
          return Promise.reject(error)
        }

        // Handle other HTTP errors
        const apiError = this.transformError(error)

        // Report to Sentry (skip for expected errors)
        if (!this.isExpectedError(error)) {
          captureException(error, {
            tags: {
              api_error: true,
              status_code: error.response?.status,
            },
            extra: {
              url: originalRequest?.url,
              method: originalRequest?.method,
              response_data: error.response?.data,
            },
          })
        }

        // Add breadcrumb for API errors
        addBreadcrumb(
          `API Error: ${originalRequest?.method?.toUpperCase()} ${originalRequest?.url}`,
          'http',
          'error',
          {
            status: error.response?.status,
            message: apiError.message,
          }
        )

        return Promise.reject(apiError)
      }
    )
  }

  private async handleAuthError(error: any): Promise<void> {
    try {
      // Clear auth state
      const authStore = useAuthStore.getState()
      authStore.signOut()

      // Add breadcrumb for auth error
      addBreadcrumb(
        'Authentication error - signing out user',
        'auth',
        'warning',
        {
          status: error.response?.status,
          url: error.config?.url,
        }
      )

      console.warn('Authentication error, user signed out')
    } catch (signOutError) {
      console.error('Error during auth error handling:', signOutError)
    }
  }

  private transformError(error: any): ApiError {
    const defaultError: ApiError = {
      status: 'error',
      message: 'An unexpected error occurred',
      code: 500,
    }

    if (!error.response) {
      // Network error
      return {
        ...defaultError,
        message: 'Network error. Please check your connection.',
        code: 0,
      }
    }

    const { status, data } = error.response

    return {
      status: 'error',
      message: data?.message || error.message || defaultError.message,
      code: status || defaultError.code,
      details: data?.details,
      timestamp: new Date().toISOString(),
    }
  }

  private isExpectedError(error: any): boolean {
    // Don't report these errors to Sentry
    const expectedStatuses = [400, 401, 403, 404, 422]
    return expectedStatuses.includes(error.response?.status)
  }

  // Public methods
  public get<T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    return this.client.get(url, config)
  }

  public post<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    return this.client.post(url, data, config)
  }

  public put<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    return this.client.put(url, data, config)
  }

  public delete<T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    return this.client.delete(url, config)
  }

  public patch<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    return this.client.patch(url, data, config)
  }

  // Cancel all pending requests
  public cancelRequests(): void {
    this.cancelTokenSource.cancel('Operation cancelled by user')
    this.cancelTokenSource = axios.CancelToken.source()
  }

  // Check if error is from cancelled request
  public isRequestCancelled(error: any): boolean {
    return axios.isCancel(error)
  }

  // Get the underlying axios instance
  public getInstance(): AxiosInstance {
    return this.client
  }
}

// Create and export singleton instance
export const apiClient = new ApiClient()
export default apiClient
