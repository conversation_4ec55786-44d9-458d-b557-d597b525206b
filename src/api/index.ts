/**
 * API Layer Index
 *
 * Central export point for all API services, hooks, and utilities
 */

// Core API Client
export { apiClient as default } from './client'
export { apiClient } from './client'

// API Services
export { authService } from './services/authService'
export { caseService } from './services/caseService'
export { notificationService } from './services/notificationService'
export { referenceService } from './services/referenceService'
export { inquiryService } from './services/inquiryService'
export { ambulanceService } from './services/ambulanceService'
export { additionalService } from './services/additionalService'

// API Configuration
export {
  apiConfig,
  API_ENDPOINTS,
  QUERY_KEYS,
  MUTATION_KEYS,
} from '@/config/api'

// Error Handling Utilities
export {
  getUserFriendlyErrorMessage,
  handleApiError,
  isExpectedError,
  shouldRetryRequest,
  getRetryDelay,
  formatValidationErrors,
  isAuthError,
  isNetworkError,
  extractErrorDetails,
} from '@/utils/apiErrorHandling'

// Type Exports - Common
export type {
  ApiResponse,
  PaginatedResponse,
  ApiError,
  ApiRequestConfig,
  BaseQueryParams,
  PrecinctInfo,
  UserContext,
  FirebaseTokenRequest,
  EntityStatus,
  PriorityLevel,
  RequestMetadata,
  ResponseMetadata,
  QueryKeyFactory,
  MutationContext,
  CacheTag,
  RequestStatus,
  RetryConfig,
  CacheConfig,
  ApiEnvironmentConfig,
} from '@/types/api/common'

// Type Exports - Authentication
export type {
  Dispatcher,
  DispatcherResponse,
  UpdateDispatcherRequest,
  FirebaseTokenRegistrationRequest,
  FirebaseTokenRegistrationResponse,
  PrecinctResponse,
} from '@/types/api/auth'

// Type Exports - Additional
export type {
  NHISVerificationRequest,
  NHISVerificationResponse,
} from '@/types/api/additional'

// Type Exports - Cases
export type {
  Case,
  CaseSummary,
  CaseResponse,
  CaseListResponse,
  CreateCaseRequest,
  UpdateCaseRequest,
  ApproveCaseRequest,
  CloseCaseRequest,
  CaseUploadRequest,
  CaseUploadResponse,
  CaseListParams,
  DashboardCaseStats,
  CaseActivity,
  CaseTimeline,
  CaseStatus,
  CaseCategory,
  Patient,
  Location,
  MedicalAssessment,
} from '@/types/api/cases'

// Type Exports - Notifications
export type {
  Notification,
  NotificationSummary,
  NotificationResponse,
  NotificationListResponse,
  NotificationListParams,
  MarkNotificationReadRequest,
  BulkMarkReadRequest,
  BulkNotificationResponse,
  NotificationPreferences,
  NotificationPreferencesResponse,
  NotificationType,
  NotificationStatus,
  NotificationPriority,
} from '@/types/api/notifications'

// Type Exports - References
export type {
  ReferenceItem,
  ReferencesResponse,
  ReferenceCategoryResponse,
  ReferenceCategory,
  ReferenceSearchParams,
  DiagnosisRecommendationRequest,
  DiagnosisRecommendationResponse,
  ProbableCauseRequest,
  ReferenceStats,
  IncidentType,
  ProvisionalDiagnosis,
  ProbableCause,
  ActionTaken,
  SeverityLevel,
  CaseCategoryReference as ReferenceCaseCategory,
  ResponderType,
  EquipmentType,
  MedicationType,
  FacilityType,
} from '@/types/api/references'

// Type Exports - Inquiries
export type {
  Inquiry,
  InquirySummary,
  InquiryResponse,
  InquiryListResponse,
  CreateInquiryRequest,
  UpdateInquiryRequest,
  AssignInquiryRequest,
  ResolveInquiryRequest,
  InquiryListParams,
  InquiryStats,
  InquiryStatsResponse,
  InquiryActivity,
  InquiryActivityResponse,
  InquiryComment,
  InquiryCommentsResponse,
  InquiryStatus,
  InquiryType,
  InquirySource,
  ContactInfo,
} from '@/types/api/inquiries'

// Type Exports - Ambulance Services
export type {
  AmbulanceProvider,
  AmbulanceProviderSummary,
  AmbulanceProviderResponse,
  AmbulanceProviderListResponse,
  AmbulancePricingRequest,
  AmbulancePricingResponse,
  PricingBreakdown,
  ProviderListParams,
  ProviderStats,
  ProviderStatsResponse,
  VehicleAvailability,
  VehicleAvailabilityResponse,
  ProviderStatus,
  AmbulanceType,
  ServiceLevel,
  EquipmentCategory,
} from '@/types/api/ambulance'
