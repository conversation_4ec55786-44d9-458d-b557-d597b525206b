import { Routes, Route, Navigate } from 'react-router-dom'
import { lazy, Suspense } from 'react'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { PublicRoute } from '@/components/auth/PublicRoute'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import ROUTES from '@/utils/constants/routes'
import MainWrapper from '@/components/layout/MainWrapper'

// Lazy load components for better performance
const LoginPage = lazy(() => import('@/pages/auth/LoginPage'))
const DispatchLoginPage = lazy(() => import('@/pages/auth/DispatchLoginPage'))
const Dashboard = lazy(() => import('@/pages/Dashboard'))
const CasesPage = lazy(() => import('@/pages/CasesPage'))
const RespondersPage = lazy(() => import('@/pages/RespondersPage'))
const LeaderboardPage = lazy(() => import('@/pages/LeaderboardPage'))
const UnauthorizedPage = lazy(() => import('@/pages/UnauthorizedPage'))
const NotFoundPage = lazy(() => import('@/pages/NotFoundPage'))

// Loading fallback component
const PageLoader = () => (
  <div className="min-h-screen flex items-center justify-center">
    <LoadingSpinner />
  </div>
)

export function AppRoutes() {
  return (
    <Suspense fallback={<PageLoader />}>
      <Routes>
        {/* Public Routes */}
        <Route
          path={ROUTES.home}
          element={
            <PublicRoute allowAuthenticated>
              <LoginPage />
            </PublicRoute>
          }
        />

        <Route
          path={ROUTES.login}
          element={
            <PublicRoute>
              <LoginPage />
            </PublicRoute>
          }
        />

        <Route
          path={ROUTES.dispatchLogin}
          element={
            <PublicRoute>
              <DispatchLoginPage />
            </PublicRoute>
          }
        />

        <Route
          path={ROUTES.dashboard}
          element={
            <ProtectedRoute>
              <MainWrapper>
                <Dashboard />
              </MainWrapper>
            </ProtectedRoute>
          }
        />

        <Route
          path={ROUTES.cases}
          element={
            <ProtectedRoute>
              <MainWrapper>
                <CasesPage />
              </MainWrapper>
            </ProtectedRoute>
          }
        />

        <Route
          path={ROUTES.responders}
          element={
            <ProtectedRoute>
              <MainWrapper>
                <RespondersPage />
              </MainWrapper>
            </ProtectedRoute>
          }
        />

        <Route
          path={ROUTES.leaderboard}
          element={
            <ProtectedRoute>
              <MainWrapper>
                <LeaderboardPage />
              </MainWrapper>
            </ProtectedRoute>
          }
        />

        <Route
          path={ROUTES.dispatchers}
          element={
            <ProtectedRoute>
              <MainWrapper>
                <div>Dispatchers Page</div>
              </MainWrapper>
            </ProtectedRoute>
          }
        />

        <Route
          path={ROUTES.settings}
          element={
            <ProtectedRoute>
              <div className="p-8">
                <h1 className="text-2xl font-bold">Settings</h1>
                <p>Application settings</p>
              </div>
            </ProtectedRoute>
          }
        />

        <Route
          path={ROUTES.profile}
          element={
            <ProtectedRoute>
              <MainWrapper>
                <div>Profile Page</div>
              </MainWrapper>
            </ProtectedRoute>
          }
        />

        {/* Error Routes */}
        <Route path={ROUTES.unauthorized} element={<UnauthorizedPage />} />
        <Route path={ROUTES.notFound} element={<NotFoundPage />} />

        {/* Catch all route - redirect to 404 */}
        <Route path="*" element={<Navigate to={ROUTES.notFound} replace />} />
      </Routes>
    </Suspense>
  )
}
