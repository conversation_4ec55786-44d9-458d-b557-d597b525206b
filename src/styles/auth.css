.logo-mobile {
  display: none;
}

@media screen and (max-width: 769px) {
  .min-h-screen {
    max-height: 100% !important;
  }

  #onboard-cy {
    grid-template-columns: 1fr;
  }

  .landing-left {
    display: none !important;
  }

  .landing-right {
    width: 100%;
    display: flex;
    flex-direction: column !important;
    max-height: 100%;
    align-items: center !important;
    justify-content: center !important;
  }

  .landing-right-login {
    width: 230%;
    margin-left: -2rem;
    display: flex;
    flex-direction: column !important;
    max-height: 100%;
    align-items: center !important;
    justify-content: center !important;
  }

  .logo-mobile {
    display: flex;
    position: relative;
    top: -4rem;
    background: #3d688f !important;
    padding: 1rem 2rem;
    color: #fff;
  }

  .ads {
    position: absolute;
    right: 2rem;
  }

  .dispatch-back {
    margin-left: -7rem;
    top: 3rem;
    position: absolute;
  }

  #dispatch-v-cy {
    font-size: 1rem;
  }

  .forgot-pass {
    width: 230%;
    align-items: center;
    justify-content: center;
  }
}
