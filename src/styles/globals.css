/* Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');

@import 'antd/dist/reset.css';
@import 'tailwindcss';
@import './auth.css';

/* Tailwind v4 Theme Configuration */
@theme {
  /* Font Families */
  --font-family-sans:
    'Poppins', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-family-heading:
    'Manrope', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-family-inter:
    'Inter', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

  /* Custom Color Palette */
  --color-primary: #254769;
  --color-primary-light: #3c6287;
  --color-primary-lighter: #c5dcfa;
  --color-secondary: #f5f9ff;
  --color-secondary-100: #e9f0fb;
  --color-secondary2: #f45b5b;
  --color-era-red: #f6505c;
  --color-era-green: #21b241;
  --color-nhis-green: #0e8428;
  --color-gray-f0: #f0f0f0;
  --color-gray-fa: #fafafa;
  --color-amber: #bda615;
  --color-orange: #ff932f;
  --color-off-white: #f5f5f7;
  --color-off-black: #222222;
  --color-dark-gray-10: #161b23;
  --color-dark-gray-20: #2c2c2c;
  --color-dark-gray-30: #545252;
  --color-dark-gray-40: #605e5e;
  --color-light-gray: #8e8a8a;
  --color-light-gray-10: #f9f9fa;
  --color-lighter-gray: #6b6b6b;
  --color-lighter-gray-2: #696969;
  --color-gray-5: #b7b0b0;
  --color-gray-10: #716565;
  --color-gray-20: #5f5f5f;
  --color-gray-30: #acacac;
  --color-gray-50: #cfcfcf;
  --color-gray-60: #cbd0dc;
  --color-gray-70: #f8f9ff;
  --color-gray-80: #cdd0d5;
  --color-blue-10: #d1e4ff;
  --color-blue-60: #667085;
  --color-dark-blueOne: #344054;
  --color-light-blueOne: #e9f1ff;
}

:root {
  font-family: 'Poppins', system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  font-family: 'Poppins', system-ui, sans-serif;
}

/* Font Hierarchy - Headings use Manrope */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: 'Manrope', system-ui, sans-serif;
  font-weight: 600;
}

/* Utility classes for font families */
.font-heading {
  font-family: 'Manrope', system-ui, sans-serif;
}

.font-body {
  font-family: 'Poppins', system-ui, sans-serif;
}

.font-inter {
  font-family: 'Inter', system-ui, sans-serif;
}

#root {
  width: 100%;
  min-height: 100vh;
}

/* Loading spinner styles */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.text-white {
  color: #fff;
}
