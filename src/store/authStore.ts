import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import {
  signIn,
  signOut,
  confirmSignIn,
  resetPassword,
  confirmResetPassword,
  fetchAuthSession,
  getCurrentUser,
  fetchUserAttributes,
} from 'aws-amplify/auth'
import { setSentryUser, clearSentryUser, addBreadcrumb } from '@/config/sentry'
import { clearAuthStorage, handleAuthError } from '@/utils/authSecurity'

interface User {
  sub: string
  email: string
  challengeName?: string
  attributes: Record<string, any>
}

interface AuthState {
  isAuthenticated: boolean
  isLoading: boolean
  user: User | null
  error: string | null
  token: string | null
}

interface AuthActions {
  signIn: (email: string, password: string) => Promise<void>
  signOut: () => Promise<void>
  setNewPassword: (password: string, userInfo?: any) => Promise<void>
  confirmMFA: (code: string) => Promise<void>
  resetPassword: (email: string) => Promise<void>
  confirmResetPassword: (
    email: string,
    code: string,
    newPassword: string
  ) => Promise<void>
  clearError: () => void
  setLoading: (loading: boolean) => void
}

type AuthStore = AuthState & AuthActions

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // State
      isAuthenticated: false,
      isLoading: false,
      user: null,
      error: null,
      token: null,

      // Actions
      signIn: async (email: string, password: string) => {
        set({ isLoading: true, error: null })
        try {
          const result = await signIn({ username: email, password })

          if (!result.isSignedIn) {
            // Handle challenges
            if (
              result.nextStep.signInStep ===
              'CONFIRM_SIGN_IN_WITH_NEW_PASSWORD_REQUIRED'
            ) {
              set({
                user: {
                  sub: email, // Use email as temporary identifier
                  email,
                  challengeName: 'NEW_PASSWORD_REQUIRED',
                  attributes: {},
                },
                isLoading: false,
              })
              return
            }

            if (
              result.nextStep.signInStep === 'CONFIRM_SIGN_IN_WITH_SMS_CODE' ||
              result.nextStep.signInStep === 'CONFIRM_SIGN_IN_WITH_TOTP_CODE'
            ) {
              set({
                user: {
                  sub: email, // Use email as temporary identifier
                  email,
                  challengeName:
                    result.nextStep.signInStep ===
                    'CONFIRM_SIGN_IN_WITH_SMS_CODE'
                      ? 'SMS_MFA'
                      : 'SOFTWARE_TOKEN_MFA',
                  attributes: {},
                },
                isLoading: false,
              })
              return
            }
          }

          // User is signed in successfully
          const session = await fetchAuthSession()
          const token = session.tokens?.idToken?.toString() || null

          // Get current user info
          const currentUser = await getCurrentUser()
          const userAttributes = await fetchUserAttributes()

          const userData = {
            sub: currentUser.userId,
            email: currentUser.username,
            attributes: userAttributes,
          }

          set({
            isAuthenticated: true,
            user: userData,
            token,
            isLoading: false,
          })

          // Set Sentry user context
          setSentryUser({
            id: userData.sub,
            email: userData.email,
            username: userData.email,
            ...userData.attributes,
          })

          // Add breadcrumb for successful sign in
          addBreadcrumb('User signed in successfully', 'auth', 'info', {
            userId: userData.sub,
            email: userData.email,
          })
        } catch (error: any) {
          const userFriendlyError = handleAuthError(error, 'sign_in', { email })

          set({
            error: userFriendlyError,
            isLoading: false,
          })

          throw error
        }
      },

      signOut: async () => {
        set({ isLoading: true })
        try {
          await signOut()

          // Clear all auth-related storage
          clearAuthStorage()

          set({
            isAuthenticated: false,
            user: null,
            token: null,
            error: null,
            isLoading: false,
          })

          // Clear Sentry user context
          clearSentryUser()

          // Add breadcrumb for sign out
          addBreadcrumb('User signed out', 'auth', 'info')
        } catch (error: any) {
          const userFriendlyError = handleAuthError(error, 'sign_out')

          set({
            error: userFriendlyError,
            isLoading: false,
          })
        }
      },

      setNewPassword: async (password: string, userInfo?: any) => {
        const { user } = get()
        if (!user) throw new Error('No user found')

        set({ isLoading: true, error: null })
        try {
          const result = await confirmSignIn({
            challengeResponse: password,
            options: {
              userAttributes: userInfo,
            },
          })

          if (result.isSignedIn) {
            const session = await fetchAuthSession()
            const token = session.tokens?.idToken?.toString() || null

            // Get current user info
            const currentUser = await getCurrentUser()
            const userAttributes = await fetchUserAttributes()

            set({
              isAuthenticated: true,
              user: {
                sub: currentUser.userId,
                email: currentUser.username,
                attributes: userAttributes,
                challengeName: undefined,
              },
              token,
              isLoading: false,
            })
          } else {
            throw new Error('Password update did not complete sign in')
          }
        } catch (error: any) {
          set({
            error: error.message || 'Password update failed',
            isLoading: false,
          })
          throw error
        }
      },

      confirmMFA: async (code: string) => {
        const { user } = get()
        if (!user) throw new Error('No user found')

        set({ isLoading: true, error: null })
        try {
          const result = await confirmSignIn({
            challengeResponse: code,
          })

          if (result.isSignedIn) {
            const session = await fetchAuthSession()
            const token = session.tokens?.idToken?.toString() || null

            // Get current user info
            const currentUser = await getCurrentUser()
            const userAttributes = await fetchUserAttributes()

            set({
              isAuthenticated: true,
              user: {
                sub: currentUser.userId,
                email: currentUser.username,
                attributes: userAttributes,
              },
              token,
              isLoading: false,
            })
          } else {
            throw new Error('MFA confirmation did not complete sign in')
          }
        } catch (error: any) {
          set({
            error: error.message || 'MFA confirmation failed',
            isLoading: false,
          })
          throw error
        }
      },

      resetPassword: async (email: string) => {
        set({ isLoading: true, error: null })
        try {
          await resetPassword({ username: email })
          set({ isLoading: false })
        } catch (error: any) {
          set({
            error: error.message || 'Password reset failed',
            isLoading: false,
          })
          throw error
        }
      },

      confirmResetPassword: async (
        email: string,
        code: string,
        newPassword: string
      ) => {
        set({ isLoading: true, error: null })
        try {
          await confirmResetPassword({
            username: email,
            confirmationCode: code,
            newPassword,
          })
          set({ isLoading: false })
        } catch (error: any) {
          set({
            error: error.message || 'Password confirmation failed',
            isLoading: false,
          })
          throw error
        }
      },

      clearError: () => set({ error: null }),
      setLoading: (loading: boolean) => set({ isLoading: loading }),
    }),
    {
      name: 'auth-storage',
      partialize: state => ({
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        // Don't persist tokens - let AWS Amplify handle token management
        // token: state.token,
      }),
      // Add storage options for better security
      storage: {
        getItem: name => {
          const value = localStorage.getItem(name)
          return value ? JSON.parse(value) : null
        },
        setItem: (name, value) => {
          localStorage.setItem(name, JSON.stringify(value))
        },
        removeItem: name => {
          localStorage.removeItem(name)
        },
      },
    }
  )
)
