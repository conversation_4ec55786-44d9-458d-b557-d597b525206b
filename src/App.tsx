import { Suspense, useEffect } from 'react'
import { UIProvider } from '@/providers/UIProvider'
import { AuthProvider } from '@/providers/AuthProvider'
import { DispatcherProvider } from '@/providers/DispatcherProvider'
import { ErrorBoundary } from '@/components/ui/ErrorBoundary'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { AppRoutes } from '@/routes'
import {
  initializeFirebase,
  requestNotificationPermission,
} from '@/utils/firebaseUtils'
import { useAuthStore } from '@/store/authStore'

function App() {
  const { user, isAuthenticated } = useAuthStore()

  useEffect(() => {
    if (!isAuthenticated || !user?.sub) return

    // 1. Ensure location permission
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        () => {},
        err => {
          if (err.code === err.PERMISSION_DENIED) {
            // Optionally show a UI prompt/banner to enable location
            console.warn('Location permission denied')
          }
        }
      )
    }

    // 2. Initialize Firebase
    initializeFirebase()

    // 3. Ensure notification permission and register token
    const setupNotificationsPermission = async () => {
      const hasPermission = await requestNotificationPermission()
      if (!hasPermission) {
        console.warn('Notification permission not granted')
        return
      }
    }
    setupNotificationsPermission()
  }, [isAuthenticated, user?.sub])

  return (
    <UIProvider>
      <ErrorBoundary>
        <AuthProvider>
          <DispatcherProvider>
            <Suspense fallback={<LoadingSpinner />}>
              <AppRoutes />
            </Suspense>
          </DispatcherProvider>
        </AuthProvider>
      </ErrorBoundary>
    </UIProvider>
  )
}

export default App
