import { X, Clock, CalendarDays, MapPin, Download } from 'lucide-react'

interface CaseDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  caseData: any
  onExport: () => void
}

const CaseDetailsModal: React.FC<CaseDetailsModalProps> = ({
  isOpen,
  onClose,
  caseData,
  onExport,
}) => {
  if (!isOpen || !caseData) return null

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) onClose()
  }

  return (
    <div
      className="fixed inset-0 flex items-center justify-center bg-black/50 z-50"
      onClick={handleBackdropClick}
    >
      <div className="bg-white rounded-lg shadow-lg w-[600px] max-h-[90vh] overflow-y-auto p-4 relative">
        {/* Header */}
        <div className="flex items-center justify-between pb-2 mb-4 border-b border-gray-300">
          <h2 className="text-base font-semibold text-gray-800">
            Case Details
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-800"
          >
            <X size={18} />
          </button>
        </div>

        <h2 className="text-lg font-bold mb-2 text-primary">
          {caseData.caseId}
        </h2>

        <div className="flex items-center justify-between w-full text-xs text-gray-600 mb-4 border-b border-gray-200 pb-2">
          <div className="flex items-center gap-1">
            <Clock size={14} className="text-green-600" />
            <span className="text-green-600 font-semibold">00:00:00</span>
          </div>
          <span className="text-gray-300">|</span>
          <div className="flex items-center gap-1">
            <CalendarDays size={14} className="text-gray-500" />
            <span>
              10:30am 1-09-2021{' '}
              <span className="text-gray-400">(1 hour ago)</span>
            </span>
          </div>
          <span className="text-gray-300">|</span>
          <div className="flex items-center gap-1">
            <MapPin size={14} className="text-gray-500" />
            <span>{caseData.location}</span>
          </div>
          <span className="text-gray-300">|</span>
          <span className="bg-yellow-200 text-yellow-900 px-1.5 py-0.5 rounded text-[10px] font-medium">
            {caseData.severity}
          </span>
        </div>

        {/* Case Details */}
        <p className="font-semibold text-xs text-gray-800 mb-2">Case Details</p>
        <div className="grid grid-cols-2 text-xs border border-gray-300 rounded-md overflow-hidden mb-4">
          <div className="p-2 border-b border-r border-gray-300">
            <p className="text-gray-600">Description</p>
            <p className="font-semibold text-gray-900">
              {caseData.description}
            </p>
          </div>
          <div className="p-2 border-b border-gray-300">
            <p className="text-gray-600">Status</p>
            <p className="font-semibold text-gray-900">{caseData.status}</p>
          </div>
          <div className="p-2 border-b border-r border-gray-300">
            <p className="text-gray-600">Request Time</p>
            <p className="font-semibold text-gray-900">
              {caseData.requestTime}
            </p>
          </div>
          <div className="p-2 border-b border-gray-300">
            <p className="text-gray-600">Dispatch Time</p>
            <p className="font-semibold text-gray-900">
              {caseData.dispatchTime}
            </p>
          </div>
          <div className="p-2 border-b border-r border-gray-300">
            <p className="text-gray-600">Location</p>
            <p className="font-semibold text-gray-900">{caseData.location}</p>
          </div>
          <div className="p-2 border-b border-gray-300">
            <p className="text-gray-600">Severity Time</p>
            <p className="font-semibold text-gray-900">
              {caseData.severityTime}
            </p>
          </div>
          <div className="p-2 border-r border-gray-300">
            <p className="text-gray-600">Case Type</p>
            <p className="font-semibold text-gray-900">{caseData.caseType}</p>
          </div>
          <div className="p-2">
            <p className="text-gray-600">Payment Status</p>
            <p className="font-semibold text-gray-900">
              {caseData.paymentStatus}
            </p>
          </div>
        </div>

        {/* Callers Details */}
        <p className="font-semibold text-xs text-gray-800 mb-2">
          Callers Details
        </p>
        <div className="grid grid-cols-2 text-xs border border-gray-300 rounded-md overflow-hidden mb-4">
          <div className="p-2 border-b border-r border-gray-300">
            <p className="text-gray-600">Callers Name</p>
            <p className="font-semibold text-gray-900">{caseData.callerName}</p>
          </div>
          <div className="p-2 border-b border-gray-300">
            <p className="text-gray-600">Callers Phone Number</p>
            <p className="font-semibold text-gray-900">
              {caseData.callerPhone}
            </p>
          </div>
          <div className="p-2 border-b border-r border-gray-300">
            <p className="text-gray-600">Insurance Status</p>
            <p className="font-semibold text-gray-900">
              {caseData.insuranceStatus}
            </p>
          </div>
          <div className="p-2 border-b border-gray-300">
            <p className="text-gray-600">Call Duration</p>
            <p className="font-semibold text-gray-900">
              {caseData.callDuration}
            </p>
          </div>
        </div>

        {/* Responder Details */}
        <p className="font-semibold text-xs text-gray-800 mb-2">
          Responder Details
        </p>
        <div className="grid grid-cols-2 text-xs border border-gray-300 rounded-md overflow-hidden divide-x divide-gray-300">
          <div className="p-2">
            <p className="text-gray-600">Responder Name</p>
            <p className="font-semibold">{caseData.responderName}</p>
          </div>
          <div className="p-2">
            <p className="text-gray-600">Responder Phone Number</p>
            <p className="font-semibold">{caseData.responderPhone}</p>
          </div>
        </div>

        <div className="flex justify-between items-center mt-4">
          <button
            onClick={() => {
              onExport()
              onClose()
            }}
            className="flex items-center gap-1 px-2.5 py-1 border border-gray-400 bg-gray-100 !text-gray-700 text-xs rounded hover:bg-gray-200 transition"
          >
            <Download size={12} className="text-gray-700" />
            Export
          </button>
          <div className="flex gap-2">
            <button className="px-2.5 py-1 border border-red-500 bg-red-50 !text-red-600 text-xs rounded hover:bg-red-100 transition">
              Close Case
            </button>
            <button className="px-2.5 py-1 border border-blue-500 bg-blue-50 !text-blue-600 text-xs rounded hover:bg-blue-100 transition">
              Update Case
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CaseDetailsModal
