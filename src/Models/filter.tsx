import { useState } from 'react'
import { X } from 'lucide-react'

interface FilterSidebarProps {
  isOpen: boolean
  onClose: () => void
}

const FilterModal: React.FC<FilterSidebarProps> = ({ isOpen, onClose }) => {
  const [openSection, setOpenSection] = useState<string | null>('caseStatus')

  const toggleSection = (section: string) => {
    setOpenSection(openSection === section ? null : section)
  }

  if (!isOpen) return null

  return (
    <div className="absolute top-full right-0 mt-2 z-50 w-64 max-w-[90vw] bg-white rounded-xl shadow-lg border border-gray-200">
      <div className="flex items-center justify-between px-3 py-2 border-b border-gray-200">
        <h2 className="text-sm font-semibold text-gray-800">Filter Options</h2>
        <button onClick={onClose} className="text-gray-500 hover:text-gray-800">
          <X size={16} />
        </button>
      </div>

      <div className="max-h-80 overflow-y-auto p-3 space-y-4 text-sm">
        <div>
          <button
            onClick={() => toggleSection('caseStatus')}
            className="w-full flex justify-between items-center font-semibold"
          >
            Case Status
            <span>{openSection === 'caseStatus' ? '-' : '+'}</span>
          </button>
          {openSection === 'caseStatus' && (
            <div className="mt-2 space-y-1 pl-2">
              {[
                'Open',
                'In Progress',
                'Closed',
                'Pending',
                'Cancelled',
                'Follow-Up Required',
              ].map(status => (
                <label key={status} className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    className="h-4 w-4 border border-gray-400 bg-white rounded text-blue-600 focus:ring-blue-500"
                  />
                  {status}
                </label>
              ))}
            </div>
          )}
        </div>

        <div>
          <button
            onClick={() => toggleSection('severity')}
            className="w-full flex justify-between items-center font-semibold text-gray-700"
          >
            Severity Level
            <span>{openSection === 'severity' ? '-' : '+'}</span>
          </button>
          {openSection === 'severity' && (
            <div className="mt-2 space-y-1 pl-2">
              {['High', 'Medium', 'Low'].map(level => (
                <label key={level} className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    className="h-4 w-4 border border-gray-400 bg-white rounded text-blue-600 focus:ring-blue-500"
                  />
                  {level}
                </label>
              ))}
            </div>
          )}
        </div>

        <div>
          <button
            onClick={() => toggleSection('emergency')}
            className="w-full flex justify-between items-center font-semibold text-gray-700"
          >
            Emergency Category
            <span>{openSection === 'emergency' ? '-' : '+'}</span>
          </button>
          {openSection === 'emergency' && (
            <div className="mt-2 space-y-1 pl-2">
              {['Medical Emergency', 'Non-Emergency'].map(cat => (
                <label key={cat} className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    className="h-4 w-4 border border-gray-400 bg-white rounded text-blue-600 focus:ring-blue-500"
                  />
                  {cat}
                </label>
              ))}
            </div>
          )}
        </div>

        <div>
          <button
            onClick={() => toggleSection('responseTime')}
            className="w-full flex justify-between items-center font-semibold text-gray-700"
          >
            Response Time
            <span>{openSection === 'responseTime' ? '-' : '+'}</span>
          </button>
          {openSection === 'responseTime' && (
            <div className="mt-2 space-y-1 pl-2">
              <label className="flex items-center gap-2">
                <input
                  type="radio"
                  name="responseTime"
                  className="h-4 w-4 border border-gray-400 bg-white rounded-full text-blue-600 focus:ring-blue-500"
                />
                Less than 15 minutes
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="radio"
                  name="responseTime"
                  className="h-4 w-4 border border-gray-400 bg-white rounded-full text-blue-600 focus:ring-blue-500"
                />
                More than 15 minutes
              </label>
            </div>
          )}
        </div>

        <div>
          <p className="font-semibold text-gray-700">Date</p>
          <div className="mt-2 flex gap-2">
            <input
              type="date"
              className="border rounded px-2 py-1 w-1/2 text-xs"
              placeholder="Start"
            />
            <input
              type="date"
              className="border rounded px-2 py-1 w-1/2 text-xs"
              placeholder="End"
            />
          </div>
        </div>

        <div>
          <p className="font-semibold text-gray-700">Responder</p>
          <select className="mt-2 w-full border rounded px-2 py-1 text-xs">
            <option>Search by name, team, ID...</option>
          </select>
        </div>

        <div>
          <p className="font-semibold text-gray-700">Location</p>
          <select className="mt-2 w-full border rounded px-2 py-1 text-xs">
            <option>Select location</option>
          </select>
        </div>
      </div>

      <div className="flex justify-between border-t border-gray-200 p-2">
        <button
          onClick={onClose}
          className="px-3 py-1 border border-gray-300 bg-gray-50 text-gray-700 text-sm rounded"
        >
          Cancel
        </button>
        <button className="px-3 py-1 border border-blue-600 bg-blue-600 !text-white text-sm rounded">
          Apply (#)
        </button>
      </div>
    </div>
  )
}

export default FilterModal
