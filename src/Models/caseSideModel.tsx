import { useState } from 'react'
import { X, Clock, CalendarDays, MapPin, Download } from 'lucide-react'
import CaseReportModal from '../Models/exportModal'

interface CaseSideModalProps {
  caseData: any
  onClose: () => void
  onExport: (caseData: any) => void
}

const mockCase = {
  caseId: 'C20230707-0008',
  description: 'Multiple sclerosis',
  status: 'In progress',
  requestTime: '10:30am 1-09-2021',
  dispatchTime: '00:30:14',
  location: 'Ikeja, Lagos',
  severity: 'Medium',
  caseType: 'Emergency',
  paymentStatus: 'Paid',
  callerName: '<PERSON><PERSON><PERSON>',
  callerPhone: '+234908766353',
  insuranceStatus: 'Registered',
  callDuration: '5 Minutes (10:30-10:35am)',
  resources: [
    {
      name: 'Bright Navy',
      phone: '+234808736353',
      type: 'First Responder',
      responseTime: '00:05:14',
    },
    {
      name: '<PERSON>',
      phone: '+234808736353',
      type: 'Ambulance Driver',
      responseTime: '00:30:14',
    },
  ],
}

const CaseSideModal: React.FC<CaseSideModalProps> = ({
  caseData,
  onClose,
  onExport,
}) => {
  // const [isExportCaseOpen, setIsExportCaseOpen] = useState(false)
  const [isReportModalOpen, setIsReportModalOpen] = useState(false)

  if (!caseData) return null

  return (
    <aside
      // onClick={() => setIsExportCaseOpen(false)}
      className="fixed right-0 top-0 z-[60] w-[380px] max-w-[90vw] h-screen bg-white shadow-2xl border-l border-gray-200 flex flex-col text-xs"
    >
      {/* Header */}
      <div className="flex items-center justify-between px-3 py-2 border-b border-gray-300">
        <h2 className="text-sm font-semibold text-right text-gray-800">
          Case Details
        </h2>
        <button onClick={onClose} className="text-gray-500 hover:text-gray-800">
          <X size={16} />
        </button>
      </div>

      <div className="flex-1 overflow-y-auto p-3">
        <h3 className="text-sm font-bold text-primary mb-1">
          {caseData.caseId}
        </h3>
        <div className="flex items-center justify-between w-full text-[11px] text-gray-600 mb-3 border-gray-200 pb-1">
          <div className="flex items-center gap-1">
            <Clock size={12} className="text-green-600" />
            <span className="text-green-600 font-semibold text-right">
              00:00:00
            </span>
          </div>
          <span className="text-gray-300">|</span>
          <div className="flex items-center gap-1">
            <CalendarDays size={12} className="text-gray-500" />
            <span>
              10:30am 1-09-2021 <span className="text-gray-400">(1h ago)</span>
            </span>
          </div>
          <span className="text-gray-300">|</span>
          <div className="flex items-center gap-1">
            <MapPin size={12} className="text-gray-500" />
            <span>{caseData.location}</span>
          </div>
          <span className="text-gray-300">|</span>
          <span className="bg-yellow-200 text-yellow-900 px-1 py-0.5 rounded text-[10px] font-medium">
            {caseData.severity}
          </span>
        </div>

        {/* Details */}
        <div className="grid grid-cols-1 gap-1">
          <p className="text-[11px] font-semibold text-gray-700">
            Case Details
          </p>
          <div className="flex justify-between pb-1">
            <p className="text-gray-500">Description</p>
            <p className="font-semibold text-right">{caseData.description}</p>
          </div>
          <div className="flex justify-between pb-1">
            <p className="text-gray-500">Status</p>
            <p className="font-semibold text-right">{caseData.status}</p>
          </div>
          <div className="flex justify-between pb-1">
            <p className="text-gray-500">Case Type</p>
            <p className="font-semibold text-right">{caseData.caseType}</p>
          </div>
          <div className="flex justify-between pb-1">
            <p className="text-gray-500">Payment Status</p>
            <p className="font-semibold text-right">{caseData.paymentStatus}</p>
          </div>

          {/* Callers */}
          <p className="text-[11px] font-semibold text-gray-700 mt-1">
            Caller Details
          </p>
          <div className="flex justify-between pb-1">
            <p className="text-gray-500">Name</p>
            <p className="font-semibold text-right">{caseData.callersName}</p>
          </div>
          <div className="flex justify-between pb-1">
            <p className="text-gray-500">Phone</p>
            <p className="font-semibold text-right">
              {caseData.callersPhoneNumber}
            </p>
          </div>
          <div className="flex justify-between pb-1">
            <p className="text-gray-500">Insurance</p>
            <p className="font-semibold text-right">
              {caseData.insuranceStatus}
            </p>
          </div>
          <div className="flex justify-between pb-1">
            <p className="text-gray-500">Duration</p>
            <p className="font-semibold text-right">{caseData.callDuration}</p>
          </div>

          {/* Resource */}
          <p className="text-[11px] font-semibold text-gray-700 mt-1">
            Resource Details
          </p>
          <div className="flex justify-between pb-1">
            <p className="text-gray-500">Responder</p>
            <p className="font-semibold text-right">{caseData.responderName}</p>
          </div>
          <div className="flex justify-between pb-1">
            <p className="text-gray-500">Phone</p>
            <p className="font-semibold text-right">
              {caseData.responderPhoneNumber}
            </p>
          </div>
          <div className="flex justify-between pb-1">
            <p className="text-gray-500">Type</p>
            <p className="font-semibold text-right">{caseData.responderType}</p>
          </div>

          {/* Summary */}
          <p className="text-[11px] font-semibold text-gray-700 mt-1">
            Action Taken Summary
          </p>
          <div className="bg-gray-100 p-4">
            <p className="text-gray-500">Dispatch Assessment</p>
            <p className="font-semibold text-right">
              {caseData.dispatchAssessment}
            </p>
          </div>
        </div>
      </div>

      {/* Footer (fixed at bottom) */}
      <div className="flex justify-between items-center border-t border-gray-300 p-2">
        <button
          onClick={() => {
            onExport(mockCase) // pass the case up
            onClose()
          }}
          className="flex items-center gap-1 px-2 py-1 border border-gray-400 bg-gray-100 !text-gray-700 text-xs rounded"
        >
          <Download size={12} />
          Export
        </button>
        <div className="flex gap-2">
          <button className="px-2 py-1 border border-red-500 bg-red-50 !text-red-600 text-xs rounded">
            Close
          </button>
          <button className="px-2 py-1 border border-blue-500 bg-blue-50 !text-blue-600 text-xs rounded">
            Update
          </button>
        </div>
      </div>
      <CaseReportModal
        isOpen={isReportModalOpen}
        onClose={() => setIsReportModalOpen(false)}
        caseData={mockCase}
      />
    </aside>
  )
}

export default CaseSideModal
