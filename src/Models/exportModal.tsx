import { ERAColourLogo } from '../assets/icons/ERAColourLogo'

interface CaseReportModalProps {
  isOpen: boolean
  onClose: () => void
  caseData: any
}

const CaseReportModal: React.FC<CaseReportModalProps> = ({
  isOpen,
  onClose,
  caseData,
}) => {
  if (!isOpen) return null

  return (
    <div
      onClick={onClose}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/40"
    >
      <div className="bg-white rounded-xl shadow-lg w-full max-w-2xl max-h-[85vh] overflow-y-auto px-2">
        <div className="flex justify-between items-center px-3 py-2">
          <h2 className="text-1xl font-bold">Case Report</h2>
          <button
            onClick={onClose}
            className="text-gray-600 hover:text-gray-900"
          >
            <ERAColourLogo />
          </button>
        </div>

        <div className="p-2 space-y-5 text-sm">
          <div>
            <h3 className="font-semibold mb-2">
              Case Details ({caseData.caseId})
            </h3>
            <div className="grid grid-cols-2 border border-gray-300 rounded-md overflow-hidden">
              <div className="p-2 border-b border-r border-gray-300">
                <p className="text-gray-600">Description</p>
                <p className="font-semibold text-gray-900">
                  {caseData.description}
                </p>
              </div>
              <div className="p-2 border-b border-gray-300">
                <p className="text-gray-600">Status</p>
                <p className="font-semibold text-gray-900">{caseData.status}</p>
              </div>
              <div className="p-2 border-b border-r border-gray-300">
                <p className="text-gray-600">Request Time</p>
                <p className="font-semibold text-gray-900">
                  {caseData.requestTime}
                </p>
              </div>
              <div className="p-2 border-b border-gray-300">
                <p className="text-gray-600">Dispatch Time</p>
                <p className="font-semibold text-gray-900">
                  {caseData.dispatchTime}
                </p>
              </div>
              <div className="p-2 border-b border-r border-gray-300">
                <p className="text-gray-600">Location</p>
                <p className="font-semibold text-gray-900">
                  {caseData.location}
                </p>
              </div>
              <div className="p-2 border-b border-gray-300">
                <p className="text-gray-600">Severity Level</p>
                <p className="font-semibold text-gray-900">
                  {caseData.severity}
                </p>
              </div>
              <div className="p-2 border-r border-gray-300">
                <p className="text-gray-600">Case Type</p>
                <p className="font-semibold text-gray-900">
                  {caseData.caseType}
                </p>
              </div>
              <div className="p-2">
                <p className="text-gray-600">Payment Status</p>
                <p className="font-semibold text-gray-900">
                  {caseData.paymentStatus}
                </p>
              </div>
            </div>
          </div>

          {/* Caller Details */}
          <div>
            <h3 className="font-semibold mb-2">Caller’s Details</h3>
            <div className="grid grid-cols-2 border border-gray-300 rounded-md overflow-hidden">
              <div className="p-2 border-b border-r border-gray-300">
                <p className="text-gray-600">Caller’s Name</p>
                <p className="font-semibold text-gray-900">
                  {caseData.callerName}
                </p>
              </div>
              <div className="p-2 border-b border-gray-300">
                <p className="text-gray-600">Caller’s Phone</p>
                <p className="font-semibold text-gray-900">
                  {caseData.callerPhone}
                </p>
              </div>
              <div className="p-2 border-r border-gray-300">
                <p className="text-gray-600">Insurance Status</p>
                <span className="px-2 py-1 text-xs font-semibold text-green-700 bg-green-100 rounded">
                  {caseData.insuranceStatus}
                </span>
              </div>
              <div className="p-2">
                <p className="text-gray-600">Call Duration</p>
                <p className="font-semibold text-gray-900">
                  {caseData.callDuration}
                </p>
              </div>
            </div>
          </div>

          {/* Resource Details */}
          <div>
            <h3 className="font-semibold mb-2">Resource Details</h3>
            <div className="space-y-3">
              {caseData.resources.map((res: any, idx: number) => (
                <div
                  key={idx}
                  className="grid grid-cols-2 border border-gray-300 rounded-md overflow-hidden"
                >
                  <div className="p-2 border-b border-r border-gray-300">
                    <p className="text-gray-600">Responder Name</p>
                    <p className="font-semibold text-gray-900">{res.name}</p>
                  </div>
                  <div className="p-2 border-b border-gray-300">
                    <p className="text-gray-600">Responder Phone</p>
                    <p className="font-semibold text-gray-900">{res.phone}</p>
                  </div>
                  <div className="p-2 border-r border-gray-300">
                    <p className="text-gray-600">Responder Type</p>
                    <p className="font-semibold text-gray-900">{res.type}</p>
                  </div>
                  <div className="p-2">
                    <p className="text-gray-600">Response Time</p>
                    <p className="font-semibold text-gray-900">
                      {res.responseTime}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CaseReportModal
