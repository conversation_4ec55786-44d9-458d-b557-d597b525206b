import { X } from 'lucide-react'

interface SortModalProps {
  isOpen: boolean
  onClose: () => void
}

const SortModal: React.FC<SortModalProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null

  return (
    <div className="absolute top-full right-0 mt-2 z-50 w-64 max-w-[90vw] bg-white rounded-xl shadow-lg border border-gray-200">
      <div className="flex items-center justify-between px-3 py-2 border-b border-gray-200">
        <h2 className="text-sm font-semibold text-gray-800">Sort</h2>
        <button onClick={onClose} className="text-gray-500 hover:text-gray-800">
          <X size={16} />
        </button>
      </div>

      <div className="p-3 space-y-2 text-sm">
        {[
          'Case ID (Ascending / Descending)',
          'Severity Level (Low → High)',
          'Severity Level (High → Low)',
          'Request Time (Oldest → Newest)',
          'Request Time (Newest → Oldest)',
          'Location (A–Z)',
          'Location (Z–A)',
        ].map(option => (
          <label key={option} className="flex items-center gap-2">
            <input
              type="radio"
              name="sortOption"
              className="h-4 w-4 border border-gray-400 bg-white rounded-full text-blue-600 focus:ring-blue-500"
            />
            {option}
          </label>
        ))}
      </div>

      <div className="flex justify-between border-t border-gray-200 p-2">
        <button
          onClick={onClose}
          className="px-3 py-1 border border-gray-300 bg-gray-50 text-gray-700 text-sm rounded"
        >
          Cancel
        </button>
        <button className="px-3 py-1 border border-blue-600 bg-blue-600 !text-white text-sm rounded">
          Apply
        </button>
      </div>
    </div>
  )
}

export default SortModal
