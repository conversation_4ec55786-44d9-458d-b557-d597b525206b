import { getToken, onMessage, deleteToken } from 'firebase/messaging'
import toast from 'react-hot-toast'
import {
  getFirebaseMessaging,
  VAPID_KEY,
  getNotificationPermission,
  requestNotificationPermission,
  isPushNotificationSupported,
  isMessagingAvailable,
} from '@/config/firebase'
import { captureException, addBreadcrumb } from '@/config/sentry'

/**
 * Firebase Cloud Messaging Service for ERA Dispatch Application
 *
 * This service handles FCM token registration, foreground notifications,
 * and integration with the existing toast notification system.
 */

export interface NotificationPayload {
  title: string
  body: string
  icon?: string
  data?: Record<string, unknown>
  tag?: string
  url?: string
}

export interface FCMToken {
  token: string
  timestamp: number
  userId?: string
}

class NotificationService {
  private currentToken: string | null = null
  private isInitialized = false
  private unsubscribeFromMessages: (() => void) | null = null

  /**
   * Initialize the notification service
   */
  async initialize(): Promise<void> {
    try {
      if (this.isInitialized) {
        console.log('Notification service already initialized')
        return
      }

      // Check if push notifications are supported
      if (!isPushNotificationSupported()) {
        console.warn('Push notifications are not supported in this browser')
        return
      }

      // Check if Firebase messaging is available
      if (!isMessagingAvailable()) {
        console.warn('Firebase messaging is not available')
        return
      }

      // Register service worker
      await this.registerServiceWorker()

      // Set up foreground message listener
      this.setupForegroundMessageListener()

      this.isInitialized = true
      console.log('✅ Notification service initialized successfully')

      addBreadcrumb('Notification service initialized', 'notification', 'info')
    } catch (error) {
      console.error('❌ Failed to initialize notification service:', error)
      captureException(error as Error, {
        context: 'notification_service_initialization',
      })
    }
  }

  /**
   * Register service worker for background notifications
   */
  private async registerServiceWorker(): Promise<void> {
    try {
      if ('serviceWorker' in navigator) {
        const registration = await navigator.serviceWorker.register(
          '/firebase-messaging-sw.js',
          { scope: '/' }
        )
        console.log('Service worker registered successfully:', registration)

        addBreadcrumb('Service worker registered', 'notification', 'info', {
          scope: registration.scope,
        })
      }
    } catch (error) {
      console.error('Service worker registration failed:', error)
      captureException(error as Error, {
        context: 'service_worker_registration',
      })
    }
  }

  /**
   * Set up listener for foreground messages
   */
  private setupForegroundMessageListener(): void {
    try {
      const messaging = getFirebaseMessaging()
      if (!messaging) return

      this.unsubscribeFromMessages = onMessage(messaging, payload => {
        console.log('Received foreground message:', payload)

        addBreadcrumb(
          'Foreground notification received',
          'notification',
          'info',
          {
            title: payload.notification?.title,
            hasData: !!payload.data,
          }
        )

        // Display as toast notification when app is in foreground
        this.showForegroundNotification({
          title: payload.notification?.title || 'ERA Dispatch Notification',
          body: payload.notification?.body || 'You have a new notification',
          icon: payload.notification?.icon,
          data: payload.data,
          tag: payload.data?.tag,
          url: payload.data?.url,
        })
      })
    } catch (error) {
      console.error('Error setting up foreground message listener:', error)
      captureException(error as Error, {
        context: 'foreground_message_listener_setup',
      })
    }
  }

  /**
   * Show foreground notification as toast
   */
  private showForegroundNotification(notification: NotificationPayload): void {
    try {
      // Show a simple toast notification for foreground messages
      toast(`${notification.title}: ${notification.body}`, {
        duration: 6000,
        position: 'top-right',
        icon: '🔔',
        style: {
          background: '#fff',
          color: '#333',
          border: '1px solid #e5e7eb',
          borderRadius: '8px',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
        },
      })

      // If there's a URL, we could show an action button
      if (notification.url) {
        setTimeout(() => {
          const shouldNavigate = window.confirm(
            `${notification.title}\n\n${notification.body}\n\nWould you like to view this notification?`
          )
          if (shouldNavigate && notification.url) {
            window.location.href = notification.url
          }
        }, 1000)
      }
    } catch (error) {
      console.error('Error showing foreground notification:', error)
      captureException(error as Error, {
        context: 'show_foreground_notification',
        notification: {
          title: notification.title,
          body: notification.body,
          hasIcon: !!notification.icon,
          hasUrl: !!notification.url,
        },
      })
    }
  }

  /**
   * Request notification permission and get FCM token
   */
  async requestPermissionAndGetToken(userId?: string): Promise<string | null> {
    try {
      // Check if messaging is available
      if (!isMessagingAvailable()) {
        console.warn('Firebase messaging is not available')
        return null
      }

      // Request notification permission
      const permission = await requestNotificationPermission()

      if (permission !== 'granted') {
        console.warn('Notification permission not granted:', permission)
        addBreadcrumb(
          'Notification permission denied',
          'notification',
          'warning',
          {
            permission,
          }
        )
        return null
      }

      // Get FCM token
      const messaging = getFirebaseMessaging()
      if (!messaging) return null

      if (!VAPID_KEY) {
        console.error('VAPID key is not configured')
        return null
      }

      const token = await getToken(messaging, {
        vapidKey: VAPID_KEY,
      })

      if (token) {
        console.log('FCM token obtained:', token.substring(0, 20) + '...')
        this.currentToken = token

        addBreadcrumb('FCM token obtained', 'notification', 'info', {
          userId,
          tokenPrefix: token.substring(0, 20),
        })

        // Store token with user info if provided
        this.storeToken(token, userId)

        return token
      } else {
        console.warn('No FCM token available')
        return null
      }
    } catch (error) {
      console.error('Error getting FCM token:', error)
      captureException(error as Error, {
        context: 'fcm_token_request',
        userId,
      })
      return null
    }
  }

  /**
   * Store FCM token locally
   */
  private storeToken(token: string, userId?: string): void {
    try {
      const tokenData: FCMToken = {
        token,
        timestamp: Date.now(),
        userId,
      }

      localStorage.setItem('fcm_token', JSON.stringify(tokenData))
      console.log('FCM token stored locally')
    } catch (error) {
      console.error('Error storing FCM token:', error)
      captureException(error as Error, {
        context: 'fcm_token_storage',
      })
    }
  }

  /**
   * Get stored FCM token
   */
  getStoredToken(): FCMToken | null {
    try {
      const storedData = localStorage.getItem('fcm_token')
      if (storedData) {
        return JSON.parse(storedData)
      }
      return null
    } catch (error) {
      console.error('Error retrieving stored FCM token:', error)
      return null
    }
  }

  /**
   * Delete FCM token
   */
  async deleteToken(): Promise<void> {
    try {
      const messaging = getFirebaseMessaging()
      if (!messaging || !this.currentToken) return

      await deleteToken(messaging)
      this.currentToken = null
      localStorage.removeItem('fcm_token')

      addBreadcrumb('FCM token deleted', 'notification', 'info')
      console.log('FCM token deleted successfully')
    } catch (error) {
      console.error('Error deleting FCM token:', error)
      captureException(error as Error, {
        context: 'fcm_token_deletion',
      })
    }
  }

  /**
   * Get current notification permission status
   */
  getPermissionStatus(): NotificationPermission {
    return getNotificationPermission()
  }

  /**
   * Check if notifications are supported and enabled
   */
  isNotificationEnabled(): boolean {
    return (
      isPushNotificationSupported() &&
      isMessagingAvailable() &&
      this.getPermissionStatus() === 'granted'
    )
  }

  /**
   * Cleanup service
   */
  cleanup(): void {
    if (this.unsubscribeFromMessages) {
      this.unsubscribeFromMessages()
      this.unsubscribeFromMessages = null
    }
    this.isInitialized = false
    console.log('Notification service cleaned up')
  }
}

// Export singleton instance
export const notificationService = new NotificationService()
export default notificationService
