/**
 * Authentication API Types
 *
 * TypeScript interfaces for authentication-related API endpoints
 */

import { ApiResponse } from './common'

// Dispatcher Data
export interface Dispatcher {
  dispatcher_id: string
  email: string
  first_name: string
  last_name: string
  phone_number?: string
  precinct_id: string
  precinct_name: string
  profile_image_url?: string
  registration_datetime?: number
  createdAt?: string
  date_of_birth?: number
  country?: string
  city?: string
  address_line_1?: string
  // Legacy fields for compatibility
  id?: string
  user_id?: string
  name?: string
  phone?: string
  role?: 'dispatcher' | 'admin' | 'supervisor'
  status?: 'active' | 'inactive' | 'suspended'
  permissions?: string[]
  created_at?: string
  updated_at?: string
  last_login?: string
  profile_image?: string
  emergency_contact?: {
    name: string
    phone: string
    relationship: string
  }
}

// Precinct Information
export interface Precinct {
  precinct_id: string
  precinct_name: string
  address?: string
  phone?: string
  email?: string
  coordinates?: {
    latitude: number
    longitude: number
  }
  coverage_area?: string
  status: 'active' | 'inactive'
  created_at: string
  updated_at: string
}

// Update Dispatcher Request
export interface UpdateDispatcherRequest {
  name?: string
  phone?: string
  profile_image?: string
  emergency_contact?: {
    name: string
    phone: string
    relationship: string
  }
  preferences?: {
    notifications: boolean
    email_alerts: boolean
    sms_alerts: boolean
    theme: 'light' | 'dark' | 'auto'
    language: string
  }
}

// Dispatcher Response
export interface DispatcherResponse
  extends ApiResponse<{
    dispatcher: Dispatcher
  }> {}

// Precinct Response
export interface PrecinctResponse
  extends ApiResponse<{
    precinct: Precinct
  }> {}

// Firebase Token Registration
export interface FirebaseTokenRegistrationRequest {
  entity: 'dispatchers' | 'responders'
  token: string
  id: string
  device_info?: {
    platform: string
    version: string
    model?: string
  }
}

export interface FirebaseTokenRegistrationResponse
  extends ApiResponse<{
    registered: boolean
    token_id: string
  }> {}

// User Session Info
export interface UserSession {
  user_id: string
  dispatcher: Dispatcher
  precinct: Precinct
  permissions: string[]
  session_id: string
  expires_at: string
  last_activity: string
}

// Auth Context for API calls
export interface AuthContext {
  user_id: string
  token: string
  issuer: string
  dispatcher?: Dispatcher
  precinct?: Precinct
}

// Dispatcher Preferences
export interface DispatcherPreferences {
  notifications: {
    push_enabled: boolean
    email_enabled: boolean
    sms_enabled: boolean
    sound_enabled: boolean
    vibration_enabled: boolean
  }
  display: {
    theme: 'light' | 'dark' | 'auto'
    language: string
    timezone: string
    date_format: string
    time_format: '12h' | '24h'
  }
  dashboard: {
    default_view: 'cases' | 'map' | 'notifications'
    auto_refresh: boolean
    refresh_interval: number
    show_closed_cases: boolean
  }
  alerts: {
    high_priority_sound: boolean
    critical_popup: boolean
    case_assignment_alert: boolean
    system_maintenance_alert: boolean
  }
}
