/**
 * Users API Types
 *
 * TypeScript interfaces for Users API endpoints
 */

import { ApiResponse } from './common'

// User
export interface User {
  id: string
  username: string
  email: string
  first_name: string
  last_name: string
  role: string
  status: 'active' | 'inactive' | 'suspended'
  precinct_id: string
  precinct_name: string
  permissions: string[]
  last_login?: string
  created_at: string
  updated_at: string
}

export interface UsersResponse
  extends ApiResponse<{
    users: User[]
  }> {}

export interface UsersLookupResponse
  extends ApiResponse<{
    users: Array<{
      id: string
      name: string
      email: string
      role: string
    }>
  }> {}
