/**
 * Responders API Types
 *
 * TypeScript interfaces for Responders API endpoints
 */

import { ApiResponse, PaginatedResponse, PriorityLevel } from './common'

// Responder Notification
export interface NotifyResponderRequest {
  caseId: string
  responderType: string
  message?: string
  urgency?: PriorityLevel
}

export interface NotifyResponderResponse
  extends ApiResponse<{
    notification_sent: boolean
    responders_notified: number
    notification_id: string
  }> {}

// Responder
export interface Responder {
  responder_id: string
  first_name: string
  last_name: string
  user_name?: string
  username?: string
  profile_image_url?: string
  email?: string
  phone_number: string
  date_of_birth?: string | number
  registration_datetime?: string | number

  level_of_training?: string
  responder_type: string
  status?: string
  employee_type?: string
  device_token?: string

  country?: string
  state?: string
  city?: string
  address_line_1: string
  address_line_2?: string

  location_latitude?: number
  location_longitude?: number
  geohash?: string
  geohash_key?: string

  // Additional fields that might be in the API response
  precinct_id?: string
  responding_to?: string | null
}

export type ResponderType = {
  location_longitude: number
  profile_image_url: string
  status: string
  registration_datetime: number
  responder_type: string
  email: string
  country: string
  state: string
  last_name: string
  address_line_1: string
  precinct_id: string
  first_name: string
  location_latitude: number
  phone_number: string
  responder_id: string
  employee_type: string
  responding_to: string | null
}

// Raw API Response structure
export interface RespondersApiResponse {
  currentPage: number
  pages: {
    totalNumOfPages: number
    totalRecords: number
    numberPerPage: number
  }
  data: {
    count: number
    data: ResponderType[]
    total_count: number
    last_key: {
      responder_id: string
    }
    type: string
  }
  status: string
}

export interface RespondersResponse extends PaginatedResponse<Responder> {}

// Transformed response structure (what the service returns)
export interface DashboardRespondersResponse {
  data: {
    items: ResponderType[]
    pagination: {
      currentPage: number
      totalPages: number
      totalItems: number
      itemsPerPage: number
      hasNextPage: boolean
      hasPreviousPage: boolean
    }
  }
}
