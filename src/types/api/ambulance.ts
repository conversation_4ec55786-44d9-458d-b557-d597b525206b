/**
 * Ambulance Services API Types
 *
 * TypeScript interfaces for ambulance provider and pricing API endpoints
 */

import { ApiResponse, PaginatedResponse, PriorityLevel } from './common'

// Ambulance Provider Status
export type ProviderStatus = 'active' | 'inactive' | 'suspended' | 'maintenance'

// Ambulance Type
export type AmbulanceType =
  | 'basic'
  | 'advanced'
  | 'critical_care'
  | 'neonatal'
  | 'psychiatric'
  | 'bariatric'
  | 'air_ambulance'

// Service Level
export type ServiceLevel = 'bls' | 'als' | 'ccr' | 'specialty'

// Equipment Category
export type EquipmentCategory =
  | 'basic_life_support'
  | 'advanced_life_support'
  | 'cardiac_monitoring'
  | 'respiratory_support'
  | 'medication_administration'
  | 'trauma_care'
  | 'specialty_equipment'

// Ambulance Provider
export interface AmbulanceProvider {
  id: string
  name: string
  company_name: string
  license_number: string
  status: ProviderStatus

  // Contact Information
  contact_info: {
    phone: string
    email: string
    emergency_phone: string
    address: string
    city: string
    state: string
    postal_code: string
    country: string
  }

  // Service Information
  service_areas: Array<{
    precinct_id: string
    precinct_name: string
    coverage_radius_km: number
    response_time_minutes: number
  }>

  // Fleet Information
  fleet: Array<{
    vehicle_id: string
    vehicle_number: string
    type: AmbulanceType
    service_level: ServiceLevel
    capacity: number
    equipment: EquipmentCategory[]
    status: 'available' | 'busy' | 'maintenance' | 'out_of_service'
    location?: {
      latitude: number
      longitude: number
      address: string
      last_updated: string
    }
  }>

  // Certifications and Compliance
  certifications: Array<{
    type: string
    number: string
    issued_by: string
    issued_date: string
    expiry_date: string
    status: 'valid' | 'expired' | 'pending'
  }>

  // Performance Metrics
  performance: {
    average_response_time: number
    success_rate: number
    customer_satisfaction: number
    total_transports: number
    on_time_percentage: number
    cancellation_rate: number
  }

  // Pricing Information
  base_rates: {
    basic_transport: number
    emergency_transport: number
    critical_care_transport: number
    mileage_rate: number
    waiting_time_rate: number
  }

  // Operational Information
  operating_hours: {
    monday: { start: string; end: string; is_24_hours: boolean }
    tuesday: { start: string; end: string; is_24_hours: boolean }
    wednesday: { start: string; end: string; is_24_hours: boolean }
    thursday: { start: string; end: string; is_24_hours: boolean }
    friday: { start: string; end: string; is_24_hours: boolean }
    saturday: { start: string; end: string; is_24_hours: boolean }
    sunday: { start: string; end: string; is_24_hours: boolean }
  }

  // Metadata
  precinct_id: string
  precinct_name: string
  created_at: string
  updated_at: string
  last_active: string
}

// Ambulance Provider Summary (for list views) - matches actual API response
export interface AmbulanceProviderSummary {
  notification_settings: {
    cases_accepted: { in_app: boolean; email: boolean }
    ambulance_arrived: { in_app: boolean; email: boolean }
    cases_declined: { in_app: boolean; email: boolean }
    cases_assigned: { in_app: boolean; email: boolean }
    cases_completed: { in_app: boolean; email: boolean }
    cases_opened: { in_app: boolean; email: boolean }
  }
  emergency_after_care: boolean
  location_longitude: number
  number_of_vehicles: number
  registration_datetime: number
  emergency_ready: boolean
  createdAt: string
  contact_email: string
  email: string
  contact_city: string
  name: string
  contact_phone_number: string
  state: string
  contact_last_name: string
  contact_first_name: string
  precinct_id: string
  address_line_1: string
  updatedAt: string
  location_latitude: number
  ambulance_provider_id: string
  phone_number: string
  is_setup_complete: boolean
  contact_role: string
  // For backward compatibility
  id?: string
  company_name?: string
  average_response_time?: number
  success_rate?: number
}

// Ambulance Request
export interface AmbulanceRequest {
  id: string
  case_id?: string
  request_type: 'emergency' | 'non_emergency' | 'transfer'
  status: 'open' | 'approved' | 'in_progress' | 'completed' | 'cancelled'
  priority: PriorityLevel
  patient_info: {
    name: string
    age: number
    gender: 'M' | 'F' | 'Other'
    condition: string
    medical_history?: string[]
  }
  pickup_location: {
    address: string
    latitude?: number
    longitude?: number
    contact_person: string
    contact_phone: string
  }
  dropoff_location?: {
    address: string
    latitude?: number
    longitude?: number
    facility_name?: string
  }
  ambulance_provider_id?: string
  ambulance_provider_name?: string
  estimated_cost?: number
  actual_cost?: number
  payment_status: 'pending' | 'requires_payment' | 'confirmed' | 'failed'
  requested_by: string
  approved_by?: string
  cancelled_by?: string
  cancellation_reason?: string
  notes?: string
  precinct_id: string
  precinct_name: string
  created_at: string
  updated_at: string
  approved_at?: string
  completed_at?: string
  cancelled_at?: string
}

export interface AmbulanceRequestsResponse
  extends PaginatedResponse<AmbulanceRequest> {}

export interface AmbulanceRequestResponse
  extends ApiResponse<{
    ambulance_request: AmbulanceRequest
  }> {}

// Ambulance Request Operations
export interface CreateAmbulanceRequestData {
  request_type: 'emergency' | 'non_emergency' | 'transfer'
  priority: PriorityLevel
  patient_info: {
    name: string
    age: number
    gender: 'M' | 'F' | 'Other'
    condition: string
    medical_history?: string[]
  }
  pickup_location: {
    address: string
    latitude?: number
    longitude?: number
    contact_person: string
    contact_phone: string
  }
  dropoff_location?: {
    address: string
    latitude?: number
    longitude?: number
    facility_name?: string
  }
  notes?: string
}

export interface UpdateAmbulanceRequestData
  extends Partial<CreateAmbulanceRequestData> {}

export interface UpdateDropoffLocationData {
  address: string
  latitude?: number
  longitude?: number
  facility_name?: string
  estimated_arrival?: string
}

// Metrics
export interface AmbulanceMetrics {
  total_requests: number
  pending_requests: number
  approved_requests: number
  completed_requests: number
  cancelled_requests: number
  average_response_time: number
  success_rate: number
  total_cost: number
  by_priority: Record<PriorityLevel, number>
  by_status: Record<string, number>
  monthly_trend: Array<{
    month: string
    count: number
    success_rate: number
  }>
}

// Ambulance Pricing Request
export interface AmbulancePricingRequest {
  request_category:
    | 'emergency'
    | 'non_emergency'
    | 'critical_care'
    | 'transport'
  request_type: 'ambulance'
  covid_case: boolean
  requires_oxygen: boolean
  distance_km?: number
  estimated_duration_minutes?: number
  service_level?: ServiceLevel
  special_requirements?: string[]
  pickup_location?: {
    latitude: number
    longitude: number
    address: string
  }
  destination_location?: {
    latitude: number
    longitude: number
    address: string
  }
  patient_info?: {
    age: number
    weight_kg?: number
    medical_conditions: string[]
    mobility_assistance_required: boolean
  }
}

// Pricing Breakdown
export interface PricingBreakdown {
  base_cost: number
  distance_cost: number
  time_cost: number
  equipment_cost: number
  special_requirements_cost: number
  covid_surcharge: number
  oxygen_surcharge: number
  total_cost: number
  currency: string

  // Cost Components Detail
  components: Array<{
    type: 'base' | 'distance' | 'time' | 'equipment' | 'surcharge' | 'special'
    description: string
    quantity: number
    unit_cost: number
    total_cost: number
  }>

  // Provider Information
  provider_id: string
  provider_name: string
  estimated_response_time: number
  service_level: ServiceLevel
  vehicle_type: AmbulanceType

  // Validity
  valid_until: string
  quote_id: string
}

// Ambulance Pricing Response
export interface AmbulancePricingResponse
  extends ApiResponse<{
    pricing_options: PricingBreakdown[]
    request_id: string
    processed_at: string
    expires_at: string
  }> {}

// Provider Filters
export interface ProviderFilters {
  status?: ProviderStatus[]
  service_level?: ServiceLevel[]
  ambulance_type?: AmbulanceType[]
  precinct_id?: string
  service_area?: string
  has_available_vehicles?: boolean
  min_success_rate?: number
  max_response_time?: number
  certification_type?: string
  search_query?: string
}

// Provider List Parameters
export interface ProviderListParams extends ProviderFilters {
  page?: number
  limit?: number
  sort_by?:
    | 'name'
    | 'response_time'
    | 'success_rate'
    | 'base_rate'
    | 'updated_at'
  sort_order?: 'asc' | 'desc'
  include_inactive?: boolean
}

// Provider Statistics
export interface ProviderStats {
  total_providers: number
  active_providers: number
  inactive_providers: number
  total_vehicles: number
  available_vehicles: number
  busy_vehicles: number

  // By Type
  by_service_level: Record<ServiceLevel, number>
  by_ambulance_type: Record<AmbulanceType, number>

  // Performance Averages
  average_response_time: number
  average_success_rate: number
  average_satisfaction: number

  // Coverage Statistics
  coverage_by_precinct: Array<{
    precinct_id: string
    precinct_name: string
    provider_count: number
    vehicle_count: number
    average_response_time: number
  }>

  // Utilization
  utilization_rate: number
  peak_hours: Array<{
    hour: number
    utilization_percentage: number
  }>
}

// Vehicle Availability
export interface VehicleAvailability {
  provider_id: string
  provider_name: string
  vehicles: Array<{
    vehicle_id: string
    vehicle_number: string
    type: AmbulanceType
    service_level: ServiceLevel
    status: 'available' | 'busy' | 'maintenance' | 'out_of_service'
    estimated_arrival_time?: number
    current_location?: {
      latitude: number
      longitude: number
      address: string
    }
    equipment_available: EquipmentCategory[]
    crew_info?: {
      driver_name: string
      medic_name: string
      certifications: string[]
    }
  }>
  last_updated: string
}

// API Response Types
export interface AmbulanceProviderResponse
  extends ApiResponse<{
    provider: AmbulanceProvider
  }> {}

// Raw API Response structure for ambulance providers
export interface AmbulanceProviderListResponse {
  data: {
    ambulance_providers: AmbulanceProviderSummary[]
    last_key: any
  }
  status: string
}

export interface ProviderStatsResponse extends ApiResponse<ProviderStats> {}

export interface AmbulanceMetricsResponse
  extends ApiResponse<AmbulanceMetrics> {}

export interface VehicleAvailabilityResponse
  extends ApiResponse<{
    availability: VehicleAvailability[]
    search_radius_km: number
    search_location: {
      latitude: number
      longitude: number
      address: string
    }
  }> {}
