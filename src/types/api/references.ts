/**
 * References API Types
 *
 * TypeScript interfaces for reference data API endpoints
 */

import { ApiResponse } from './common'

// Reference Data Categories
export type ReferenceCategory =
  | 'incident_types'
  | 'provisional_diagnosis'
  | 'probable_causes'
  | 'actions_taken'
  | 'severity_levels'
  | 'case_categories'
  | 'responder_types'
  | 'equipment_types'
  | 'medication_types'
  | 'facility_types'

// Base Reference Item
export interface ReferenceItem {
  id: string
  code: string
  name: string
  description?: string
  category: ReferenceCategory
  active: boolean
  sort_order: number
  metadata?: Record<string, any>
  created_at: string
  updated_at: string
}

// Incident Type Reference
export interface IncidentType extends ReferenceItem {
  category: 'incident_types'
  severity_default: 'low' | 'medium' | 'high' | 'critical'
  requires_ambulance: boolean
  estimated_duration_minutes: number
  equipment_required: string[]
  protocols: string[]
}

// Provisional Diagnosis Reference
export interface ProvisionalDiagnosis extends ReferenceItem {
  category: 'provisional_diagnosis'
  icd_code?: string
  severity_indicators: string[]
  common_symptoms: string[]
  risk_factors: string[]
  contraindications: string[]
  related_diagnoses: string[]
}

// Probable Cause Reference
export interface ProbableCause extends ReferenceItem {
  category: 'probable_causes'
  provisional_diagnosis_codes: string[]
  likelihood_score: number
  investigation_steps: string[]
  prevention_measures: string[]
}

// Action Taken Reference
export interface ActionTaken extends ReferenceItem {
  category: 'actions_taken'
  applicable_diagnoses: string[]
  required_equipment: string[]
  required_personnel: string[]
  estimated_time_minutes: number
  success_indicators: string[]
  complications: string[]
}

// Severity Level Reference
export interface SeverityLevel extends ReferenceItem {
  category: 'severity_levels'
  level: 'low' | 'medium' | 'high' | 'critical'
  response_time_target_minutes: number
  escalation_criteria: string[]
  resource_requirements: {
    personnel_count: number
    equipment_types: string[]
    vehicle_type: string
  }
}

// Case Category Reference
export interface CaseCategoryReference extends ReferenceItem {
  category: 'case_categories'
  priority_default: 'low' | 'medium' | 'high' | 'critical'
  billing_category: string
  insurance_coverage: boolean
  documentation_requirements: string[]
}

// Responder Type Reference
export interface ResponderType extends ReferenceItem {
  category: 'responder_types'
  qualifications_required: string[]
  certifications_required: string[]
  equipment_authorized: string[]
  procedures_authorized: string[]
  supervision_required: boolean
}

// Equipment Type Reference
export interface EquipmentType extends ReferenceItem {
  category: 'equipment_types'
  equipment_class: 'basic' | 'advanced' | 'specialized'
  maintenance_schedule: string
  calibration_required: boolean
  training_required: string[]
  cost_per_use?: number
}

// Medication Type Reference
export interface MedicationType extends ReferenceItem {
  category: 'medication_types'
  generic_name: string
  brand_names: string[]
  dosage_forms: string[]
  contraindications: string[]
  side_effects: string[]
  drug_interactions: string[]
  storage_requirements: string
}

// Facility Type Reference
export interface FacilityType extends ReferenceItem {
  category: 'facility_types'
  services_provided: string[]
  specializations: string[]
  capacity_indicators: {
    beds: number
    icu_beds: number
    emergency_capacity: number
  }
  equipment_available: string[]
  certifications: string[]
}

// Reference Collection Response
export interface ReferencesResponse
  extends ApiResponse<{
    references: {
      incident_types: IncidentType[]
      provisional_diagnosis: ProvisionalDiagnosis[]
      probable_causes: ProbableCause[]
      actions_taken: ActionTaken[]
      severity_levels: SeverityLevel[]
      case_categories: CaseCategoryReference[]
      responder_types: ResponderType[]
      equipment_types: EquipmentType[]
      medication_types: MedicationType[]
      facility_types: FacilityType[]
    }
    last_updated: string
    version: string
  }> {}

// Single Category Response
export interface ReferenceCategoryResponse<T = ReferenceItem>
  extends ApiResponse<{
    items: T[]
    category: ReferenceCategory
    count: number
    last_updated: string
  }> {}

// Diagnosis Recommendation Request
export interface DiagnosisRecommendationRequest {
  provisional_diagnosis: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  symptoms?: string[]
  patient_age?: number
  patient_gender?: 'M' | 'F' | 'Other'
  medical_history?: string[]
  current_medications?: string[]
}

// Diagnosis Recommendation Response
export interface DiagnosisRecommendationResponse
  extends ApiResponse<{
    recommendations: Array<{
      diagnosis_code: string
      diagnosis_name: string
      confidence_score: number
      reasoning: string
      recommended_actions: string[]
      urgency_level: 'low' | 'medium' | 'high' | 'critical'
      estimated_treatment_time: number
      required_resources: {
        personnel: string[]
        equipment: string[]
        medications: string[]
      }
      contraindications: string[]
      follow_up_required: boolean
    }>
    metadata: {
      algorithm_version: string
      processed_at: string
      confidence_threshold: number
    }
  }> {}

// Probable Cause Request
export interface ProbableCauseRequest {
  provisional_diagnosis_code: string
  additional_symptoms?: string[]
  risk_factors?: string[]
  environmental_factors?: string[]
}

// Reference Search Parameters
export interface ReferenceSearchParams {
  category?: ReferenceCategory
  query?: string
  active_only?: boolean
  sort_by?: 'name' | 'code' | 'sort_order' | 'updated_at'
  sort_order?: 'asc' | 'desc'
  limit?: number
  offset?: number
}

// Reference Statistics
export interface ReferenceStats {
  total_items: number
  by_category: Record<ReferenceCategory, number>
  active_items: number
  inactive_items: number
  last_updated: string
  most_used: Array<{
    category: ReferenceCategory
    code: string
    name: string
    usage_count: number
  }>
  recently_updated: Array<{
    category: ReferenceCategory
    code: string
    name: string
    updated_at: string
  }>
}

// Reference Usage Tracking
export interface ReferenceUsage {
  reference_id: string
  reference_code: string
  category: ReferenceCategory
  used_by: string
  used_in_context: 'case_creation' | 'case_update' | 'diagnosis' | 'treatment'
  used_at: string
  case_id?: string
  metadata?: Record<string, any>
}

// Reference Validation
export interface ReferenceValidation {
  is_valid: boolean
  errors: string[]
  warnings: string[]
  suggestions: Array<{
    field: string
    current_value: any
    suggested_value: any
    reason: string
  }>
}

// Reference Update Request
export interface ReferenceUpdateRequest {
  name?: string
  description?: string
  active?: boolean
  sort_order?: number
  metadata?: Record<string, any>
}

// Bulk Reference Operation
export interface BulkReferenceOperation {
  operation: 'create' | 'update' | 'delete' | 'activate' | 'deactivate'
  items: Array<{
    id?: string
    code?: string
    data?: any
  }>
}

// Bulk Operation Response
export interface BulkReferenceResponse
  extends ApiResponse<{
    processed_count: number
    success_count: number
    failed_count: number
    results: Array<{
      id?: string
      code?: string
      status: 'success' | 'failed'
      error?: string
      data?: any
    }>
  }> {}
