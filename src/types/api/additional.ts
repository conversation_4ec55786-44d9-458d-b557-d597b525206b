/**
 * Additional API Types
 *
 * TypeScript interfaces for additional API endpoints
 */

import { ApiResponse } from './common'

// NHIS Verification
export interface NHISVerificationRequest {
  phone: string
}

export interface NHISEnrollee {
  id: string
  phone: string
  name: string
  gender: 'M' | 'F'
  date_of_birth: string
  enrollment_date: string
  status: 'active' | 'inactive' | 'suspended'
  plan_type: string
  provider: string
  coverage_details?: {
    benefits: string[]
    limitations: string[]
    copay_amount?: number
  }
}

export interface NHISVerificationResponse
  extends ApiResponse<{
    enrollee: NHISEnrollee | null
    verified: boolean
    message: string
  }> {}
