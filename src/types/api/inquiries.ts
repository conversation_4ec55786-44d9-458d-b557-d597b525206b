/**
 * Inquiries API Types
 *
 * TypeScript interfaces for inquiry-related API endpoints
 */

import { ApiResponse, PaginatedResponse, PriorityLevel } from './common'

// Inquiry Status
export type InquiryStatus =
  | 'pending'
  | 'in_progress'
  | 'resolved'
  | 'closed'
  | 'cancelled'

// Inquiry Type
export type InquiryType =
  | 'general'
  | 'medical'
  | 'emergency'
  | 'complaint'
  | 'feedback'
  | 'information_request'
  | 'follow_up'

// Inquiry Source
export type InquirySource =
  | 'phone'
  | 'web'
  | 'mobile_app'
  | 'email'
  | 'walk_in'
  | 'referral'

// Contact Information
export interface ContactInfo {
  name: string
  phone: string
  email?: string
  address?: string
  preferred_contact_method: 'phone' | 'email' | 'sms'
  alternative_phone?: string
  emergency_contact?: {
    name: string
    phone: string
    relationship: string
  }
}

// Inquiry Entity
export interface Inquiry {
  id: string
  inquiry_number: string
  title: string
  description: string
  type: InquiryType
  status: InquiryStatus
  priority: PriorityLevel
  source: InquirySource

  // Contact Information
  contact_info: ContactInfo

  // Assignment Information
  assigned_to?: string
  assigned_to_name?: string
  assigned_by?: string
  assigned_at?: string

  // Resolution Information
  resolution?: string
  resolution_notes?: string
  resolved_by?: string
  resolved_at?: string

  // Timestamps
  created_at: string
  updated_at: string
  closed_at?: string

  // Additional Information
  category?: string
  subcategory?: string
  tags: string[]
  attachments: Array<{
    id: string
    filename: string
    url: string
    size: number
    type: string
    uploaded_at: string
  }>

  // Related Information
  related_case_id?: string
  related_inquiry_ids: string[]

  // Metadata
  precinct_id: string
  precinct_name: string
  dispatcher_id: string
  dispatcher_name: string

  // Follow-up Information
  follow_up_required: boolean
  follow_up_date?: string
  follow_up_notes?: string

  // Satisfaction
  satisfaction_rating?: number
  satisfaction_feedback?: string
}

// Inquiry Summary (for list views)
export interface InquirySummary {
  id: string
  inquiry_number: string
  title: string
  type: InquiryType
  status: InquiryStatus
  priority: PriorityLevel
  contact_name: string
  contact_phone: string
  assigned_to_name?: string
  created_at: string
  updated_at: string
  precinct_name: string
}

// Create Inquiry Request
export interface CreateInquiryRequest {
  title: string
  description: string
  type: InquiryType
  priority: PriorityLevel
  source: InquirySource
  contact_info: ContactInfo
  category?: string
  subcategory?: string
  tags?: string[]
  related_case_id?: string
  follow_up_required?: boolean
  follow_up_date?: string
  attachments?: Array<{
    filename: string
    content: string // base64 encoded
    type: string
  }>
}

// Update Inquiry Request
export interface UpdateInquiryRequest {
  title?: string
  description?: string
  type?: InquiryType
  priority?: PriorityLevel
  status?: InquiryStatus
  contact_info?: Partial<ContactInfo>
  category?: string
  subcategory?: string
  tags?: string[]
  resolution?: string
  resolution_notes?: string
  follow_up_required?: boolean
  follow_up_date?: string
  follow_up_notes?: string
}

// Assign Inquiry Request
export interface AssignInquiryRequest {
  assigned_to: string
  notes?: string
}

// Resolve Inquiry Request
export interface ResolveInquiryRequest {
  resolution: string
  resolution_notes?: string
  satisfaction_rating?: number
  satisfaction_feedback?: string
  follow_up_required?: boolean
  follow_up_date?: string
}

// Inquiry Filters
export interface InquiryFilters {
  status?: InquiryStatus[]
  type?: InquiryType[]
  priority?: PriorityLevel[]
  source?: InquirySource[]
  assigned_to?: string
  dispatcher_id?: string
  precinct_id?: string
  date_from?: string
  date_to?: string
  category?: string
  subcategory?: string
  tags?: string[]
  contact_name?: string
  inquiry_number?: string
  search_query?: string
}

// Inquiry List Parameters
export interface InquiryListParams extends InquiryFilters {
  page?: number
  limit?: number
  sort_by?: 'created_at' | 'updated_at' | 'priority' | 'status'
  sort_order?: 'asc' | 'desc'
  include_closed?: boolean
}

// Inquiry Statistics
export interface InquiryStats {
  total_inquiries: number
  pending_inquiries: number
  in_progress_inquiries: number
  resolved_inquiries: number
  closed_inquiries: number
  cancelled_inquiries: number

  // By Type
  by_type: Record<InquiryType, number>

  // By Priority
  by_priority: Record<PriorityLevel, number>

  // By Source
  by_source: Record<InquirySource, number>

  // Performance Metrics
  average_resolution_time: number
  average_response_time: number
  satisfaction_average: number

  // Trends
  monthly_trend: Array<{
    month: string
    count: number
    resolved_count: number
    satisfaction_average: number
  }>

  // Top Categories
  top_categories: Array<{
    category: string
    count: number
    percentage: number
  }>
}

// Inquiry Activity
export interface InquiryActivity {
  id: string
  inquiry_id: string
  activity_type:
    | 'created'
    | 'updated'
    | 'assigned'
    | 'resolved'
    | 'closed'
    | 'commented'
  description: string
  performed_by: string
  performed_by_role: 'dispatcher' | 'admin' | 'system'
  timestamp: string
  metadata?: Record<string, any>
  changes?: Record<
    string,
    {
      old_value: any
      new_value: any
    }
  >
}

// Inquiry Comment
export interface InquiryComment {
  id: string
  inquiry_id: string
  content: string
  author_id: string
  author_name: string
  author_role: 'dispatcher' | 'admin' | 'system'
  created_at: string
  updated_at?: string
  attachments?: Array<{
    id: string
    filename: string
    url: string
    size: number
    type: string
  }>
}

// API Response Types
export interface InquiryResponse
  extends ApiResponse<{
    inquiry: Inquiry
  }> {}

export interface InquiryListResponse
  extends PaginatedResponse<InquirySummary> {}

export interface InquiryStatsResponse extends ApiResponse<InquiryStats> {}

export interface InquiryActivityResponse
  extends ApiResponse<{
    activities: InquiryActivity[]
  }> {}

export interface InquiryCommentsResponse
  extends ApiResponse<{
    comments: InquiryComment[]
  }> {}

// Inquiry Template
export interface InquiryTemplate {
  id: string
  name: string
  title_template: string
  description_template: string
  type: InquiryType
  priority: PriorityLevel
  category?: string
  subcategory?: string
  tags: string[]
  active: boolean
  created_at: string
  updated_at: string
}

// Inquiry Export
export interface InquiryExportRequest {
  filters?: InquiryFilters
  format: 'csv' | 'excel' | 'pdf'
  include_comments?: boolean
  include_attachments?: boolean
  date_range?: {
    start: string
    end: string
  }
}

export interface InquiryExportResponse
  extends ApiResponse<{
    export_id: string
    download_url: string
    expires_at: string
    file_size: number
    record_count: number
  }> {}
