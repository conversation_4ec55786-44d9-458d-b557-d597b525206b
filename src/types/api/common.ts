/**
 * Common API Types and Interfaces
 *
 * Shared types used across all API endpoints
 */

// Base API Response Structure
export interface ApiResponse<T = any> {
  status: 'success' | 'error'
  message: string
  data: T
  code?: number
}

// Paginated Response
export interface PaginatedResponse<T> {
  status: 'success' | 'error'
  message: string
  data: {
    items: T[]
    pagination: {
      currentPage: number
      totalPages: number
      totalItems: number
      itemsPerPage: number
      hasNextPage: boolean
      hasPreviousPage: boolean
    }
  }
}

// Error Response
export interface ApiError {
  status: 'error'
  message: string
  code: number
  details?: Record<string, any>
  timestamp?: string
}

// Request Configuration
export interface ApiRequestConfig {
  timeout?: number
  retries?: number
  cache?: boolean
  skipAuth?: boolean
  skipErrorHandling?: boolean
}

// Query Parameters
export interface BaseQueryParams {
  limit?: number
  offset?: number
  page?: number
  sort?: string
  order?: 'asc' | 'desc'
}

// Precinct Information
export interface PrecinctInfo {
  precinct_id: string
  precinct_name: string
}

// User Context
export interface UserContext {
  user_id: string
  precinct?: PrecinctInfo
  token: string
  issuer: string
}

// Firebase Token Registration
export interface FirebaseTokenRequest {
  entity: 'dispatchers' | 'responders'
  token: string
  id: string
}

// Common Entity Status
export type EntityStatus = 'active' | 'inactive' | 'pending' | 'archived'

// Common Priority Levels
export type PriorityLevel = 'low' | 'medium' | 'high' | 'critical'

// Common Request/Response Metadata
export interface RequestMetadata {
  requestId: string
  timestamp: string
  version: string
}

export interface ResponseMetadata extends RequestMetadata {
  processingTime: number
  cached?: boolean
}

// Query Key Factory Types
export interface QueryKeyFactory {
  all: readonly string[]
  lists: () => readonly string[]
  list: (filters?: Record<string, any>) => readonly string[]
  details: () => readonly string[]
  detail: (id: string) => readonly string[]
}

// Mutation Context for Optimistic Updates
export interface MutationContext<T = any> {
  previousData?: T
  optimisticData?: T
  rollback?: () => void
}

// Cache Tags for Invalidation
export type CacheTag =
  | 'cases'
  | 'notifications'
  | 'inquiries'
  | 'responders'
  | 'references'
  | 'dispatchers'
  | 'ambulance-providers'
  | 'ambulance-pricing'

// Request Status for UI
export interface RequestStatus {
  isLoading: boolean
  isError: boolean
  isSuccess: boolean
  error?: ApiError | null
}

// Retry Configuration
export interface RetryConfig {
  attempts: number
  delay: number
  backoff: 'linear' | 'exponential'
  retryCondition?: (error: any) => boolean
}

// Cache Configuration
export interface CacheConfig {
  staleTime: number
  gcTime: number
  refetchOnWindowFocus: boolean
  refetchOnReconnect: boolean
}

// Environment Configuration
export interface ApiEnvironmentConfig {
  baseUrl: string
  timeout: number
  retries: RetryConfig
  cache: CacheConfig
  enableLogging: boolean
}
