/**
 * Cases API Types
 *
 * TypeScript interfaces for case-related API endpoints
 */

import { ApiResponse, PaginatedResponse, PriorityLevel } from './common'

// Case Status
export type CaseStatus =
  | 'pending'
  | 'approved'
  | 'in_progress'
  | 'completed'
  | 'closed'
  | 'cancelled'

// Case Category
export type CaseCategory =
  | 'emergency'
  | 'non_emergency'
  | 'transport'
  | 'consultation'

// Patient Information
export interface Patient {
  allergies: string
  condition: string
  gender: string
  pre_existing_condition: string
  age_group: string
  last_name: string
  medication: string
  first_name: string
}

interface ResponderRecommendation {
  number_of_responders: number
  responder_type: string
}

interface HospitalAssigned {
  status: string
}

interface AdditionalInformation {
  secondary_questions: any[] // Could be more specific
}

// Location Information
export interface Location {
  address: string
  coordinates?: {
    latitude: number
    longitude: number
  }
  landmark?: string
  access_instructions?: string
}

// Medical Assessment
export interface MedicalAssessment {
  chief_complaint: string
  symptoms: string[]
  vital_signs?: {
    blood_pressure?: string
    heart_rate?: number
    temperature?: number
    respiratory_rate?: number
    oxygen_saturation?: number
  }
  provisional_diagnosis?: string
  probable_cause?: string
  severity_level: PriorityLevel
  requires_oxygen: boolean
  is_covid_case: boolean
  additional_notes?: string
}

// Case Data
export interface Case {
  case_id: string
  address_line_1: string
  case_status: string
  caller_phone_number: string
  provisional_diagnosis: string
  severity: string
  case_created_time: string // ISO date string
  case_open_time: string // ISO date string
  incident_description: string
  caller_first_name?: string
  caller_last_name?: string
  probable_cause?: string
  source?: string
  approved_by_dispatcher?: boolean
  partner_name?: string | null
  location_longitude?: number
  payment_method?: string
  payment_status?: string
  payment_option?: string
  requires_oxygen?: string
  third_party_check?: boolean
  number_of_patients?: number
  user_can_talk?: boolean
  responded_to?: boolean
  api_user_id?: string
  action_taken?: string[]
  is_patient_caller?: boolean
  inquiry_creation_time?: number // Unix timestamp
  precinct_id?: string
  approved_by_dispatcher_at?: number // Unix timestamp
  location_latitude?: number
  open_dispatcher_id?: string
  nature_of_inquiry?: string
  additional_information?: AdditionalInformation
  health_insurance_coverage?: boolean
  responders?: any[] // Could be more specific
  response_type?: string
  inquiry_id?: string
  patients?: Patient[]
  ambulance_accepted?: boolean
  require_ambulance?: boolean
  partner_case?: boolean
  case_open_time_pk?: string
  responder_recommendation?: ResponderRecommendation[]
  notify_emergency_contacts?: boolean
  responder_recommendation_count?: number
  covid_case?: boolean
  hospital_assigned?: HospitalAssigned
  address_line_2?: string
  images?: any[] // Could be more specific
}

// Create Case Request
export interface CreateCaseRequest {
  category: CaseCategory
  priority: PriorityLevel
  patient: Omit<Patient, 'id'>
  pickup_location: Location
  destination_location?: Location
  medical_assessment: MedicalAssessment
  estimated_cost?: number
  reference_number?: string
  external_case_id?: string
  notes?: string
}

// Update Case Request
export interface UpdateCaseRequest {
  category?: CaseCategory
  priority?: PriorityLevel
  patient?: Partial<Patient>
  pickup_location?: Location
  destination_location?: Location
  medical_assessment?: Partial<MedicalAssessment>
  estimated_cost?: number
  notes?: string
}

// Approve Case Request
export interface ApproveCaseRequest {
  ambulance_provider_id: string
  responder_id?: string
  estimated_arrival_time?: string
  special_instructions?: string
  approved_cost?: number
}

// Close Case Request
export interface CloseCaseRequest {
  dispatcher_id: string
  closure_reason: string
  closure_notes?: string
  outcome: 'successful' | 'unsuccessful' | 'cancelled'
  actual_cost?: number
  actual_duration?: number
  patient_satisfaction?: number
  final_diagnosis?: string
  treatment_provided?: string
  recommendations?: string
}

// Case Upload Request (for bulk operations)
export interface CaseUploadRequest {
  cases: CreateCaseRequest[]
  batch_id?: string
  validation_mode?: 'strict' | 'lenient'
}

// Case Search/Filter Parameters
export interface CaseFilters {
  status?: CaseStatus[]
  category?: CaseCategory[]
  priority?: PriorityLevel[]
  dispatcher_id?: string
  responder_id?: string
  ambulance_provider_id?: string
  precinct_id?: string
  date_from?: string
  date_to?: string
  patient_name?: string
  case_number?: string
  search_query?: string
}

// Case List Parameters
export interface CaseListParams extends CaseFilters {
  page?: number
  limit?: number
  sort_by?: 'created_at' | 'updated_at' | 'priority' | 'status'
  sort_order?: 'asc' | 'desc'
  include_closed?: boolean
}

// Case Summary (for list views)
export interface CaseSummary {
  id: string
  case_number: string
  status: CaseStatus
  category: CaseCategory
  priority: PriorityLevel
  patient_name: string
  patient_age: number
  pickup_address: string
  dispatcher_name: string
  created_at: string
  updated_at: string
  estimated_cost?: number
}

// API Response Types
export interface CaseResponse
  extends ApiResponse<{
    case: Case
  }> {}

export interface CaseListResponse extends PaginatedResponse<CaseSummary> {}

export interface CaseUploadResponse
  extends ApiResponse<{
    uploaded_count: number
    failed_count: number
    batch_id: string
    errors?: Array<{
      index: number
      errors: string[]
    }>
  }> {}

interface CasesCount {
  total: number
  open: number
  'in-progress': number
  cancelled: number
  closed: number
}

interface CasesResult {
  open: Case[]
  'in-progress': Case[]
  cancelled: Case[]
  closed: Case[]
}

// Case Statistics
export interface DashboardCaseStats {
  cases_count: CasesCount
  result: CasesResult
}

// Case Activity Log
export interface CaseActivity {
  id: string
  case_id: string
  activity_type:
    | 'created'
    | 'updated'
    | 'approved'
    | 'assigned'
    | 'completed'
    | 'closed'
    | 'cancelled'
  description: string
  performed_by: string
  performed_by_role: 'dispatcher' | 'responder' | 'admin' | 'system'
  timestamp: string
  metadata?: Record<string, any>
  changes?: Record<
    string,
    {
      old_value: any
      new_value: any
    }
  >
}

// Case Timeline
export interface CaseTimeline {
  case_id: string
  activities: CaseActivity[]
  milestones: Array<{
    type:
      | 'created'
      | 'approved'
      | 'dispatched'
      | 'arrived'
      | 'completed'
      | 'closed'
    timestamp: string
    duration_from_previous?: number
  }>
  total_duration?: number
  sla_compliance: boolean
  sla_target_minutes: number
}

// Google Distance Matrix API
export interface DistanceMatrixRequest {
  origin: string
  destinations: string
}

export interface DistanceMatrixElement {
  distance: {
    text: string
    value: number
  }
  duration: {
    text: string
    value: number
  }
  status: string
}

export interface DistanceMatrixRow {
  elements: DistanceMatrixElement[]
}

export interface DistanceMatrixResponse {
  destination_addresses: string[]
  origin_addresses: string[]
  rows: DistanceMatrixRow[]
  status: string
}

// Case Search
export interface CaseSearchRequest {
  searchParam: string
  searchValue: string
  precinct_id?: string
}

// Incident Report
export interface IncidentReport {
  id: string
  case_id: string
  report_type: string
  description: string
  severity: PriorityLevel
  location: string
  reporter_name: string
  reporter_contact: string
  incident_datetime: string
  status: 'pending' | 'investigating' | 'resolved' | 'closed'
  assigned_to?: string
  resolution?: string
  attachments?: Array<{
    id: string
    filename: string
    url: string
    type: string
  }>
  created_at: string
  updated_at: string
}

export interface IncidentReportResponse
  extends ApiResponse<{
    incident_report: IncidentReport
  }> {}

// Case Responders
export interface CaseResponder {
  id: string
  case_id: string
  responder_id: string
  responder_name: string
  responder_type: string
  contact_number: string
  status: 'assigned' | 'en_route' | 'on_scene' | 'completed' | 'unavailable'
  assigned_at: string
  arrived_at?: string
  completed_at?: string
  location?: {
    latitude: number
    longitude: number
    address: string
  }
  equipment: string[]
  notes?: string
}

export interface CaseRespondersResponse
  extends ApiResponse<{
    responders: CaseResponder[]
  }> {}

export interface CaseMetrics {
  total_cases: number
  pending_cases: number
  approved_cases: number
  completed_cases: number
  closed_cases: number
  average_resolution_time: number
  success_rate: number
  by_priority: Record<PriorityLevel, number>
  by_category: Record<string, number>
  monthly_trend: Array<{
    month: string
    count: number
    success_rate: number
  }>
}

export interface CaseMetricsResponse extends ApiResponse<CaseMetrics> {}
