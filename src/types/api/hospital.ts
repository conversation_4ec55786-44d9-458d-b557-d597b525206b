/**
 * Hospital API Types
 *
 * TypeScript interfaces for Hospital API endpoints
 */

import { ApiResponse, PriorityLevel } from './common'

// Hospital - matches actual API response
export interface Hospital {
  capacity: number
  hospital_id: string
  ownership_type: string
  hospital_name: string
  location_longitude: number
  registration_datetime: string
  setup_completed: boolean
  geohash_key: string
  country: string
  state: string
  precinct_id: string
  address_line_1: string
  location_latitude: number
  geohash: string
  precinct_name: string
  registration_datetime_pk: string
  // For backward compatibility
  id?: string
  name?: string
  severity?: string
  key?: number
}

export interface HospitalsResponse extends ApiResponse<Hospital[]> {}

// Hospital Transfer
export interface HospitalTransferRequest {
  hospitalId: string
  caseId: string
  report: string
  notes: string
  transfer_reason?: string
  patient_condition?: string
  estimated_arrival?: string
}

export interface HospitalTransferResponse
  extends ApiResponse<{
    transfer_id: string
    hospital_name: string
    estimated_arrival: string
  }> {}

// Hospital Case Assignment
export interface HospitalCaseAssignmentRequest {
  case_id: string
  hospital_id: string
  priority?: PriorityLevel
  notes?: string
}

export interface HospitalCaseAssignmentResponse
  extends ApiResponse<{
    assignment_id: string
    hospital_name: string
    case_number: string
  }> {}

// Hospital Case
export interface HospitalCase {
  id: string
  case_id: string
  hospital_id: string
  hospital_name: string
  status: 'new' | 'assigned' | 'in_progress' | 'completed' | 'cancelled'
  priority: PriorityLevel
  patient_name: string
  patient_age: number
  admission_type: string
  department: string
  assigned_doctor?: string
  estimated_discharge?: string
  notes?: string
  created_at: string
  updated_at: string
}

export interface HospitalCasesResponse
  extends ApiResponse<{
    cases: HospitalCase[]
    total_count: number
  }> {}
