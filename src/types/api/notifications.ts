/**
 * Notifications API Types
 *
 * TypeScript interfaces for notification-related API endpoints
 */

import { ApiResponse, PaginatedResponse, PriorityLevel } from './common'

// Notification Types
export type NotificationType =
  | 'case_assigned'
  | 'case_approved'
  | 'case_completed'
  | 'case_cancelled'
  | 'system_alert'
  | 'maintenance'
  | 'emergency_broadcast'
  | 'reminder'
  | 'update'

// Notification Status
export type NotificationStatus = 'unread' | 'read' | 'archived'

// Notification Priority
export type NotificationPriority = PriorityLevel

// Notification Entity
export interface Notification {
  id: string
  title: string
  message: string
  type: NotificationType
  status: NotificationStatus
  priority: NotificationPriority

  // Recipient Information
  recipient_id: string
  recipient_type: 'dispatcher' | 'responder' | 'admin'

  // Sender Information
  sender_id?: string
  sender_name?: string
  sender_type?: 'system' | 'dispatcher' | 'responder' | 'admin'

  // Related Entity Information
  related_entity_type?: 'case' | 'inquiry' | 'responder' | 'system'
  related_entity_id?: string

  // Metadata
  data?: Record<string, any>
  action_url?: string
  action_label?: string

  // Timestamps
  created_at: string
  read_at?: string
  expires_at?: string

  // Delivery Information
  delivery_channels: ('push' | 'email' | 'sms' | 'in_app')[]
  delivered_at?: string
  delivery_status?: 'pending' | 'delivered' | 'failed'

  // Precinct Information
  precinct_id?: string
  precinct_name?: string
}

// Notification Summary (for list views)
export interface NotificationSummary {
  id: string
  title: string
  message: string
  type: NotificationType
  status: NotificationStatus
  priority: NotificationPriority
  created_at: string
  read_at?: string
  related_entity_type?: string
  related_entity_id?: string
  action_url?: string
}

// Notification Filters
export interface NotificationFilters {
  status?: NotificationStatus[]
  type?: NotificationType[]
  priority?: NotificationPriority[]
  recipient_id?: string
  precinct_id?: string
  date_from?: string
  date_to?: string
  related_entity_type?: string
  related_entity_id?: string
}

// Notification List Parameters
export interface NotificationListParams extends NotificationFilters {
  page?: number
  limit?: number
  sort_by?: 'created_at' | 'priority' | 'status'
  sort_order?: 'asc' | 'desc'
  include_read?: boolean
}

// Mark Notification as Read Request
export interface MarkNotificationReadRequest {
  notification_id: string
  entity: 'dispatcher' | 'responder'
}

// Bulk Mark as Read Request
export interface BulkMarkReadRequest {
  notification_ids: string[]
  entity: 'dispatcher' | 'responder'
}

// Notification Preferences
export interface NotificationPreferences {
  user_id: string
  entity_type: 'dispatcher' | 'responder'

  // Channel Preferences
  push_enabled: boolean
  email_enabled: boolean
  sms_enabled: boolean
  in_app_enabled: boolean

  // Type Preferences
  type_preferences: Record<
    NotificationType,
    {
      enabled: boolean
      channels: ('push' | 'email' | 'sms' | 'in_app')[]
      priority_override?: NotificationPriority
    }
  >

  // Timing Preferences
  quiet_hours?: {
    enabled: boolean
    start: string // HH:mm format
    end: string // HH:mm format
    timezone: string
    days: (
      | 'monday'
      | 'tuesday'
      | 'wednesday'
      | 'thursday'
      | 'friday'
      | 'saturday'
      | 'sunday'
    )[]
  }

  // Frequency Preferences
  digest_enabled: boolean
  digest_frequency: 'hourly' | 'daily' | 'weekly'
  max_notifications_per_hour: number

  updated_at: string
}

// API Response Types
export interface NotificationResponse
  extends ApiResponse<{
    notification: Notification
  }> {}

export interface NotificationListResponse
  extends PaginatedResponse<NotificationSummary> {}

export interface FirebaseTokenResponse
  extends ApiResponse<{
    registered: boolean
    token_id: string
    expires_at?: string
  }> {}

export interface NotificationPreferencesResponse
  extends ApiResponse<{
    preferences: NotificationPreferences
  }> {}

// Bulk Operations Response
export interface BulkNotificationResponse
  extends ApiResponse<{
    processed_count: number
    success_count: number
    failed_count: number
    errors?: Array<{
      notification_id: string
      error: string
    }>
  }> {}

// Real-time Notification Event
export interface NotificationEvent {
  type: 'new_notification' | 'notification_read' | 'notification_updated'
  notification: Notification
  timestamp: string
  user_id: string
}

// Notification Template
export interface NotificationTemplate {
  id: string
  name: string
  type: NotificationType
  title_template: string
  message_template: string
  default_priority: NotificationPriority
  default_channels: ('push' | 'email' | 'sms' | 'in_app')[]
  variables: string[]
  active: boolean
  created_at: string
  updated_at: string
}
