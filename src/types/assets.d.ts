/**
 * Asset type declarations for Vite
 * This file provides TypeScript support for importing various asset types
 */

declare module '*.svg' {
  const content: string
  export default content
}

declare module '*.png' {
  const content: string
  export default content
}

declare module '*.jpg' {
  const content: string
  export default content
}

declare module '*.jpeg' {
  const content: string
  export default content
}

declare module '*.gif' {
  const content: string
  export default content
}

declare module '*.webp' {
  const content: string
  export default content
}

declare module '*.ico' {
  const content: string
  export default content
}

declare module '*.bmp' {
  const content: string
  export default content
}

// For importing assets with explicit queries
declare module '*.svg?url' {
  const content: string
  export default content
}

declare module '*.png?url' {
  const content: string
  export default content
}

declare module '*.jpg?url' {
  const content: string
  export default content
}

// For importing SVGs as React components (if using vite-plugin-svgr)
declare module '*.svg?react' {
  import { FC, SVGProps } from 'react'
  const content: FC<SVGProps<SVGElement>>
  export default content
}

// For importing assets as raw strings
declare module '*.svg?raw' {
  const content: string
  export default content
}
