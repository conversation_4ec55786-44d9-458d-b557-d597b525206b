export interface User {
  id: string
  email: string
  name: string
  role: 'dispatcher' | 'admin' | 'responder'
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface AuthUser {
  sub: string
  email: string
  attributes: Record<string, any>
  challengeName?: 'NEW_PASSWORD_REQUIRED' | 'SMS_MFA' | 'SOFTWARE_TOKEN_MFA'
}

export interface AuthState {
  user: AuthUser | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  token: string | null
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface SignUpData extends LoginCredentials {
  name: string
  confirmPassword: string
}

export interface AuthError {
  code: string
  message: string
  userFriendlyMessage: string
}

export interface AuthSession {
  accessToken: string
  idToken: string
  refreshToken: string
  expiresAt: number
}

export interface AuthValidation {
  isValid: boolean
  shouldRefresh: boolean
  error?: string
}
