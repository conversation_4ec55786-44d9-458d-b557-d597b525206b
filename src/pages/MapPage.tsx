import React, { useState, useEffect, useCallback } from 'react'
import { Wrapper, Status } from '@googlemaps/react-wrapper'
import { TextInput, But<PERSON>, Loader, Alert } from '@mantine/core'
import { Search, MapPin, AlertCircle } from 'lucide-react'
import {
  mockCases,
  mockResponders,
  mockHospitals,
} from '@/utils/mockData/mapData'
import type {
  MockCase,
  MockResponder,
  MockHospital,
} from '@/utils/mockData/mapData'
import { handleApiError } from '@/utils/apiErrorHandling'
import {
  createCaseMarker,
  createResponderMarker,
  createHospitalMarker,
  generateCaseInfoWindow,
  generateResponderInfoWindow,
  generateHospitalInfoWindow,
} from '@/components/map/MapMarkers'
import { MapLegend } from '@/components/map/MapLegend'

// Map component interfaces
interface MapComponentProps {
  center: google.maps.LatLngLiteral
  zoom: number
  cases: MockCase[]
  responders: MockResponder[]
  hospitals: MockHospital[]
  onMarkerClick: (type: 'case' | 'responder' | 'hospital', data: any) => void
}

interface GeolocationState {
  loading: boolean
  error: string | null
  position: GeolocationPosition | null
}

interface SearchState {
  query: string
  loading: boolean
  error: string | null
}

// Default map center (Lagos, Nigeria)
const DEFAULT_CENTER: google.maps.LatLngLiteral = {
  lat: 6.5244,
  lng: 3.3792,
}

const DEFAULT_ZOOM = 12

// Map component that renders the actual Google Map
const MapComponent: React.FC<MapComponentProps> = ({
  center,
  zoom,
  cases,
  responders,
  hospitals,
  onMarkerClick,
}) => {
  const [map, setMap] = useState<google.maps.Map | null>(null)
  const [markers, setMarkers] = useState<google.maps.Marker[]>([])
  const [infoWindow, setInfoWindow] = useState<google.maps.InfoWindow | null>(
    null
  )

  const mapRef = useCallback(
    (node: HTMLDivElement | null) => {
      if (node !== null && !map) {
        const newMap = new google.maps.Map(node, {
          center,
          zoom,
          mapTypeControl: true,
          streetViewControl: true,
          fullscreenControl: true,
          zoomControl: true,
          styles: [
            {
              featureType: 'poi',
              elementType: 'labels',
              stylers: [{ visibility: 'off' }],
            },
          ],
        })
        setMap(newMap)
        setInfoWindow(new google.maps.InfoWindow())
      }
    },
    [center, zoom, map]
  )

  // Clear existing markers
  const clearMarkers = useCallback(() => {
    markers.forEach(marker => marker.setMap(null))
    setMarkers([])
  }, [markers])

  // Create markers for different entity types
  useEffect(() => {
    if (!map || !infoWindow) return

    clearMarkers()
    const newMarkers: google.maps.Marker[] = []

    // Create case markers with custom icons and info windows
    cases.forEach(caseItem => {
      const marker = createCaseMarker(caseItem, map, selectedCase => {
        infoWindow.setContent(generateCaseInfoWindow(selectedCase))
        infoWindow.open(map, marker)
        onMarkerClick('case', selectedCase)
      })
      newMarkers.push(marker)
    })

    // Create responder markers with custom icons and info windows
    responders.forEach(responder => {
      const marker = createResponderMarker(
        responder,
        map,
        selectedResponder => {
          infoWindow.setContent(generateResponderInfoWindow(selectedResponder))
          infoWindow.open(map, marker)
          onMarkerClick('responder', selectedResponder)
        }
      )
      newMarkers.push(marker)
    })

    // Create hospital markers with custom icons and info windows
    hospitals.forEach(hospital => {
      const marker = createHospitalMarker(hospital, map, selectedHospital => {
        infoWindow.setContent(generateHospitalInfoWindow(selectedHospital))
        infoWindow.open(map, marker)
        onMarkerClick('hospital', selectedHospital)
      })
      newMarkers.push(marker)
    })

    setMarkers(newMarkers)
  }, [
    map,
    infoWindow,
    cases,
    responders,
    hospitals,
    onMarkerClick,
    clearMarkers,
  ])

  // Update map center when center prop changes
  useEffect(() => {
    if (map) {
      map.setCenter(center)
    }
  }, [map, center])

  return <div ref={mapRef} className="w-full h-full" />
}

// Loading component for map initialization
const MapLoadingComponent: React.FC = () => (
  <div className="w-full h-full flex items-center justify-center bg-gray-100">
    <div className="text-center">
      <Loader size="lg" className="mb-4" />
      <p className="text-gray-600">Loading map...</p>
    </div>
  </div>
)

// Error component for map loading failures
const MapErrorComponent: React.FC<{ error: Error }> = ({ error }) => (
  <div className="w-full h-full flex items-center justify-center bg-gray-100">
    <Alert
      icon={<AlertCircle size={16} />}
      title="Map Loading Error"
      color="red"
      className="max-w-md"
    >
      {error.message ||
        'Failed to load Google Maps. Please check your internet connection and try again.'}
    </Alert>
  </div>
)

// Render function for Google Maps Wrapper
const render = (status: Status): React.ReactElement => {
  if (status === Status.LOADING) return <MapLoadingComponent />
  if (status === Status.FAILURE)
    return <MapErrorComponent error={new Error('Failed to load Google Maps')} />
  return <></>
}

// Main Map Page Component
const MapPage: React.FC = () => {
  const [mapCenter, setMapCenter] =
    useState<google.maps.LatLngLiteral>(DEFAULT_CENTER)
  const [geolocation, setGeolocation] = useState<GeolocationState>({
    loading: true,
    error: null,
    position: null,
  })
  const [search, setSearch] = useState<SearchState>({
    query: '',
    loading: false,
    error: null,
  })

  // Get user's current location on component mount
  useEffect(() => {
    if (!navigator.geolocation) {
      setGeolocation({
        loading: false,
        error: 'Geolocation is not supported by this browser',
        position: null,
      })
      return
    }

    navigator.geolocation.getCurrentPosition(
      position => {
        const userLocation = {
          lat: position.coords.latitude,
          lng: position.coords.longitude,
        }
        setMapCenter(userLocation)
        setGeolocation({
          loading: false,
          error: null,
          position,
        })
      },
      error => {
        let errorMessage = 'Failed to get your location'
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Location access denied. Using default location.'
            break
          case error.POSITION_UNAVAILABLE:
            errorMessage =
              'Location information unavailable. Using default location.'
            break
          case error.TIMEOUT:
            errorMessage = 'Location request timed out. Using default location.'
            break
        }

        setGeolocation({
          loading: false,
          error: errorMessage,
          position: null,
        })
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000, // 5 minutes
      }
    )
  }, [])

  // Handle marker clicks
  const handleMarkerClick = useCallback(
    (type: 'case' | 'responder' | 'hospital', data: any) => {
      // TODO: Implement modal or detail view for clicked markers
      console.log(`Clicked ${type}:`, data)
    },
    []
  )

  // Handle address search
  const handleSearch = useCallback(async () => {
    if (!search.query.trim()) return

    setSearch(prev => ({ ...prev, loading: true, error: null }))

    try {
      // Check if Google Maps API is loaded
      if (typeof google === 'undefined' || !google?.maps) {
        throw new Error(
          'Google Maps API is not loaded yet. Please try again in a moment.'
        )
      }

      const geocoder = new google.maps.Geocoder()

      geocoder.geocode({ address: search.query }, (results, status) => {
        if (status === 'OK' && results?.[0]) {
          const location = results[0].geometry.location
          setMapCenter({
            lat: location.lat(),
            lng: location.lng(),
          })
          setSearch(prev => ({ ...prev, loading: false }))
        } else {
          setSearch(prev => ({
            ...prev,
            loading: false,
            error: 'Address not found. Please try a different search term.',
          }))
        }
      })
    } catch (error) {
      const apiError = handleApiError(error, 'Address search')
      setSearch(prev => ({
        ...prev,
        loading: false,
        error: apiError.message,
      }))
    }
  }, [search.query])

  // Handle search input key press
  const handleSearchKeyPress = useCallback(
    (event: React.KeyboardEvent) => {
      if (event.key === 'Enter') {
        handleSearch()
      }
    },
    [handleSearch]
  )

  const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY

  if (!apiKey) {
    return (
      <div className="p-6">
        <Alert
          icon={<AlertCircle size={16} />}
          title="Configuration Error"
          color="red"
        >
          Google Maps API key is not configured. Please add
          VITE_GOOGLE_MAPS_API_KEY to your environment variables.
        </Alert>
      </div>
    )
  }

  return (
    <div className="absolute inset-0 flex flex-col">
      {/* Header with search */}
      <div className="p-4 bg-white border-b border-gray-200 flex-shrink-0 z-10">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <h1 className="text-2xl font-heading font-semibold text-gray-900">
            Map
          </h1>

          {/* Search bar */}
          <div className="flex items-center space-x-2 w-full md:max-w-md">
            <TextInput
              placeholder="Search for an address..."
              value={search.query}
              onChange={event =>
                setSearch(prev => ({
                  ...prev,
                  query: event.currentTarget.value,
                }))
              }
              onKeyPress={handleSearchKeyPress}
              leftSection={<Search size={16} />}
              className="flex-1"
              error={search.error}
            />
            <Button
              onClick={handleSearch}
              loading={search.loading}
              disabled={!search.query.trim()}
              leftSection={<MapPin size={16} />}
              className="shrink-0"
            >
              <span className="hidden sm:inline">Search</span>
              <span className="sm:hidden">Go</span>
            </Button>
          </div>
        </div>

        {/* Geolocation status */}
        {geolocation.error && (
          <Alert
            icon={<AlertCircle size={16} />}
            title="Location Notice"
            color="yellow"
            className="mt-4"
          >
            {geolocation.error}
          </Alert>
        )}
      </div>

      {/* Map container */}
      <div className="flex-1 relative">
        <Wrapper apiKey={apiKey} render={render}>
          <MapComponent
            center={mapCenter}
            zoom={DEFAULT_ZOOM}
            cases={mockCases}
            responders={mockResponders}
            hospitals={mockHospitals}
            onMarkerClick={handleMarkerClick}
          />
        </Wrapper>

        {/* Map Legend */}
        <MapLegend className="hidden md:block" />
      </div>
    </div>
  )
}

export default MapPage
