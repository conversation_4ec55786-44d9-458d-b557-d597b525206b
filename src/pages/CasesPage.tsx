import { useState, useMemo, useEffect } from 'react'
import Table from '@/components/tableProps'
import classNames from 'classnames'
import {
  CalendarDays,
  Download,
  Search,
  Filter,
  ArrowDownUp,
} from 'lucide-react'
import Pagination from '@/components/Pagination'
import CaseDetailsModal from '../Models/caseModel'
import CaseSideModal from '../Models/caseSideModel'
import FilterModal from '../Models/filter'
import SortButton from '../Models/sort'
import CaseReportModal from '../Models/exportModal'
import StatusBadge from '@/components/badge/StatusBadge'
import caseService from '../api/services/caseService'

const columns = [
  { key: 'caseId', label: 'Case ID' },
  { key: 'description', label: 'Description' },
  { key: 'contact', label: 'Contact Details' },
  { key: 'location', label: 'Location' },
  {
    key: 'insurance',
    label: 'Insurance Status',
    render: (value: string) => <StatusBadge type="insurance" value={value} />,
  },
  { key: 'requestTime', label: 'Request Time' },
  {
    key: 'severity',
    label: 'Severity Level',
    render: (value: string) => <StatusBadge type="severity" value={value} />,
  },
]

const rows = [
  {
    caseId: 'C20230707–0008',
    description: 'Severe burns',
    contact: 'Adenike Ajagbe',
    location: 'Ikeja, Lagos',
    insurance: 'Registered',
    requestTime: '10:30am (1 hour ago)',
    severity: 'High',
    status: 'In Progress',
    caseType: 'Emergency',
    paymentStatus: '',
    dispatchAssessment:
      'Advised caller to access patient"s consciousness, breathing and ability to communicate',
  },
  {
    caseId: 'C20244701–0004',
    description: 'Accident',
    contact: 'Chioma Jacob',
    location: 'Yaba, Lagos',
    insurance: 'Not Registered',
    requestTime: '8:30am (3 hour ago)',
    severity: 'Low',
    status: 'In Progress',
    caseType: 'Emergency',
    paymentStatus: 'Paid',
    dispatchAssessment:
      'Advised caller to access patient"s consciousness, breathing and ability to communicate',
  },
  {
    caseId: 'C11244701–0024',
    description: 'Stroke',
    contact: 'Joy Chike',
    location: 'Yaba, Lagos',
    insurance: 'Not Registered',
    requestTime: '4:00am (7 hour ago)',
    severity: 'Medium',
    status: 'In Progress',
    caseType: 'Emergency',
    paymentStatus: 'Paid',
    dispatchAssessment:
      'Advised caller to access patient"s consciousness, breathing and ability to communicate',
  },
]

const mockCase = {
  caseId: 'C20230707-0008',
  description: 'Multiple sclerosis',
  status: 'In progress',
  requestTime: '10:30am 1-09-2021',
  dispatchTime: '00:30:14',
  location: 'Ikeja, Lagos',
  severity: 'Medium',
  caseType: 'Emergency',
  paymentStatus: 'Paid',
  callerName: 'Ajayi Babatunde',
  callerPhone: '+234908766353',
  insuranceStatus: 'Registered',
  callDuration: '5 Minutes (10:30-10:35am)',
  resources: [
    {
      name: 'Bright Navy',
      phone: '+234808736353',
      type: 'First Responder',
      responseTime: '00:05:14',
    },
    {
      name: 'Anthony Abanga',
      phone: '+234808736353',
      type: 'Ambulance Driver',
      responseTime: '00:30:14',
    },
  ],
}

const CaseOverviewPage = () => {
  const [currentPage, setCurrentPage] = useState(1)
  const [selectedCase, setSelectedCase] = useState<any | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [sideCase, setSideCase] = useState<any | null>(null)
  const [isFilterOpen, setIsFilterOpen] = useState(false)
  const [isSortOpen, setIsSortOpen] = useState(false)
  const [isExportCaseOpen, setIsExportCaseOpen] = useState(false)
  const [isReportModalOpen, setIsReportModalOpen] = useState(false)
  const [reportCase, setReportCase] = useState<any | null>(null)

  const rowsPerPage = 10
  const handleRowClick = (row: any) => {
    setSideCase(null)
    setSelectedCase(row)
  }

  const filteredRows = useMemo(() => {
    if (!searchQuery) return rows

    return rows.filter(row => {
      const query = searchQuery.toLowerCase()
      console.log('query', query)
      return (
        row.description?.toLowerCase().includes(query) ||
        row.contact?.toLowerCase().includes(query) ||
        row.location?.toLowerCase().includes(query) ||
        row.severity?.toLowerCase().includes(query) ||
        row.status?.toLowerCase().includes(query)
      )
    })
  }, [searchQuery, rows])

  const handleCaseIdClick = (row: any) => {
    console.log('Side modal triggered for:', row.caseId)
    setSelectedCase(null)
    setSideCase(row)
  }
  useEffect(() => {
    const fetchCases = async () => {
      try {
        console.log('About to call getCases with params:', {
          limit: rowsPerPage,
          page: currentPage,
        })

        const data = await caseService.getCases({
          limit: rowsPerPage,
          page: currentPage,
        })
        console.log('Fetched cases:', data)
      } catch (error) {
        console.error('Failed to fetch cases (outer catch):', error)
      }
    }

    fetchCases()
  }, [currentPage, rowsPerPage])

  return (
    <div className="p-6">
      <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
        <div className="flex space-x-4 flex-grow gap-3">
          {[
            { label: 'Open Cases', active: true },
            { label: 'Active Cases', active: false },
            { label: 'Closed Cases', active: false },
          ].map(({ label, active }) => (
            <button
              key={label}
              className={classNames(
                'flex items-center gap-1 px-2 py-1 rounded-md text-sm font-medium border',
                active
                  ? 'bg-blue-100 text-blue-900 border-blue-300'
                  : 'bg-white text-gray-700 border-gray-300'
              )}
            >
              {label}
              <span className="inline-flex items-center justify-center w-5 h-5 text-xs font-semibold text-white bg-gray-800 rounded-full">
                1
              </span>
            </button>
          ))}
        </div>

        <div className="flex items-center gap-3">
          <button className="flex items-center gap-1 px-2 py-1 border rounded text-sm text-gray-700 bg-white border-gray-400">
            <CalendarDays size={16} />
            Today
          </button>
          <button
            onClick={() => {
              setReportCase(mockCase)
              setIsReportModalOpen(true)
            }}
            className="flex items-center gap-1 px-2 py-1 border rounded text-sm text-gray-700 bg-white border-gray-400"
          >
            <Download size={16} />
            Export
          </button>
          <CaseReportModal
            isOpen={isReportModalOpen}
            onClose={() => setIsReportModalOpen(false)}
            caseData={reportCase}
          />
          <CaseReportModal
            isOpen={isExportCaseOpen}
            onClose={() => setIsExportCaseOpen(false)}
            caseData={mockCase}
          />
        </div>
      </div>

      <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
        <div className="relative flex-1 md:w-64">
          <span className="absolute left-3 top-2.5 text-gray-400">
            <Search size={20} />
          </span>
          <input
            type="text"
            placeholder="Search Cases"
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            className="pl-9 pr-4 py-2 border border-gray-400 rounded-full text-sm w-full bg-gray-100"
          />
        </div>

        <div className="flex items-center gap-3">
          <div className="relative">
            <button
              onClick={() => setIsFilterOpen(prev => !prev)}
              className="flex items-center gap-1 px-2 py-1 bg-white border rounded text-sm text-gray-700 border-gray-400"
            >
              <Filter size={16} />
              Filter
            </button>

            <FilterModal
              isOpen={isFilterOpen}
              onClose={() => setIsFilterOpen(false)}
            />
          </div>

          <div className="relative">
            <button
              onClick={() => setIsSortOpen(prev => !prev)}
              className="flex items-center gap-1 px-2 py-1 bg-white border rounded text-sm text-gray-700 border-gray-400"
            >
              <ArrowDownUp size={16} />
              Sort
            </button>

            <SortButton
              isOpen={isSortOpen}
              onClose={() => setIsSortOpen(false)}
            />
          </div>
        </div>
      </div>

      <Table
        columns={columns}
        data={filteredRows}
        onRowClick={handleRowClick}
        onCaseIdClick={handleCaseIdClick}
      />
      <Pagination
        currentPage={currentPage}
        totalPages={Math.ceil(rows.length / rowsPerPage)}
        onPageChange={setCurrentPage}
      />
      {selectedCase && (
        <CaseDetailsModal
          isOpen={true}
          onClose={() => setSelectedCase(null)}
          caseData={selectedCase}
          onExport={() => {
            setSelectedCase(null)
            setIsExportCaseOpen(true)
          }}
        />
      )}

      {sideCase && (
        <CaseSideModal
          caseData={sideCase}
          onClose={() => setSideCase(null)}
          onExport={caseData => {
            setReportCase(caseData)
            setIsReportModalOpen(true)
          }}
        />
      )}
    </div>
  )
}

export default CaseOverviewPage
