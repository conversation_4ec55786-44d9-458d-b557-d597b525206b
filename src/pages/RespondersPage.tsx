import { useState, useMemo } from 'react'
import { Search, User } from 'lucide-react'
import { Loader, Button, Text } from '@mantine/core'
import Table from '@/components/tableProps'
import Pagination from '@/components/Pagination'
import { useAllResponders } from '@/hooks/api/useResponder'
import { Responder } from '@/types/api/responders'
import {
  SortDropdown,
  FilterDropdown,
  ExportDropdown,
  StatusTabs,
  getResponderTableColumns,
} from '@/components/responders'

export default function RespondersPage() {
  const [currentPage, setCurrentPage] = useState(1)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState<string>('')
  const [selectedType, setSelectedType] = useState<string>('')
  const [sortValue, setSortValue] = useState('name-asc')
  const [filterStatuses, setFilterStatuses] = useState<string[]>([])
  const [filterTypes, setFilterTypes] = useState<string[]>([])
  const rowsPerPage = 100

  // Get table columns
  const columns = getResponderTableColumns()

  // Fetch Responders
  const {
    data: respondersData,
    isLoading: isLoadingResponders,
    error: respondersError,
    refetch: refetchResponders,
  } = useAllResponders(currentPage, rowsPerPage)

  // Filter and search responders
  const filteredResponders = useMemo(() => {
    if (!respondersData?.items || !Array.isArray(respondersData.items)) {
      return []
    }

    let filtered = [...respondersData.items] // Create a copy to avoid mutations

    // Search filter
    if (searchTerm && Array.isArray(filtered)) {
      const searchLower = searchTerm.toLowerCase()
      filtered = filtered.filter(
        (responder: Responder) =>
          responder.first_name?.toLowerCase().includes(searchLower) ||
          responder.last_name?.toLowerCase().includes(searchLower) ||
          responder.user_name?.toLowerCase().includes(searchLower) ||
          responder.email?.toLowerCase().includes(searchLower) ||
          responder.phone_number?.includes(searchTerm) ||
          responder.responder_type?.toLowerCase().includes(searchLower) ||
          responder.city?.toLowerCase().includes(searchLower) ||
          responder.state?.toLowerCase().includes(searchLower)
      )
    }

    // Status filter
    if (selectedStatus && Array.isArray(filtered)) {
      filtered = filtered.filter(
        (responder: Responder) =>
          responder.status?.toLowerCase() === selectedStatus.toLowerCase()
      )
    }

    // Type filter
    if (selectedType && Array.isArray(filtered)) {
      filtered = filtered.filter(
        (responder: Responder) =>
          responder.responder_type?.toLowerCase() === selectedType.toLowerCase()
      )
    }

    return Array.isArray(filtered) ? filtered : []
  }, [respondersData?.items, searchTerm, selectedStatus, selectedType])

  // Calculate total pages for pagination
  const totalPages = respondersData?.pagination?.totalPages || 0
  // For server-side pagination, use the data as-is (no client-side slicing)
  const paginatedResponders = Array.isArray(filteredResponders)
    ? filteredResponders
    : []

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    // Scroll to top when page changes
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  // Handle search
  const handleSearch = (value: string) => {
    setSearchTerm(value)
    setCurrentPage(1) // Reset to first page when searching
  }

  // Handle status filter
  const handleStatusFilter = (status: string) => {
    setSelectedStatus(status)
    setCurrentPage(1) // Reset to first page when filtering
  }

  // Handle export
  const handleExport = (type: 'pdf' | 'csv') => {
    // TODO: Implement export functionality
    console.log(`Export responders as ${type}`)
  }

  // Handle filter clear
  const handleClearFilters = () => {
    setFilterStatuses([])
    setFilterTypes([])
  }

  // Error handling for API errors
  if (respondersError) {
    return (
      <div className="min-h-screen bg-[#f9f9fb] p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <Text fw={500} c="red" size="sm">
                Error loading responders
              </Text>
              <div className="mt-2">
                <Text size="sm" c="red">
                  {respondersError.message ||
                    'Server error occurred. Please try again later.'}
                </Text>
                <Button
                  variant="subtle"
                  size="xs"
                  c="red"
                  onClick={() => refetchResponders()}
                  className="mt-2"
                >
                  Try again
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-[#f9f9fb] p-6">
      {/* Header with status tabs */}
      <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
        <StatusTabs
          selectedStatus={selectedStatus}
          onStatusChange={handleStatusFilter}
          respondersData={respondersData}
        />
        <ExportDropdown onExport={handleExport} />
      </div>

      {/* Search + Filters */}
      <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
        <div className="relative flex-1 md:w-80">
          <span className="absolute left-3 top-2.5 text-gray-400">
            <Search size={16} />
          </span>
          <input
            type="text"
            value={searchTerm}
            onChange={e => handleSearch(e.target.value)}
            placeholder="Search by name, email, phone..."
            className="pl-9 pr-4 py-2 border border-gray-400 rounded-full text-sm w-full bg-gray-100 focus:bg-white focus:border-blue-500 transition-colors"
          />
        </div>
        <div className="flex items-center gap-3">
          <FilterDropdown
            filterStatuses={filterStatuses}
            filterTypes={filterTypes}
            onStatusChange={setFilterStatuses}
            onTypeChange={setFilterTypes}
            onClearFilters={handleClearFilters}
          />
          <SortDropdown sortValue={sortValue} onSortChange={setSortValue} />
        </div>
      </div>

      {/* Results Summary */}
      <div className="mb-4">
        <Text size="sm" c="dimmed">
          Showing {filteredResponders.length} of{' '}
          {respondersData?.pagination?.totalItems || 0} responders
          {searchTerm && ` matching "${searchTerm}"`}
          {selectedStatus && ` with status "${selectedStatus}"`}
          {selectedType && ` of type "${selectedType}"`}
        </Text>
      </div>

      {/* Table */}
      {isLoadingResponders ? (
        <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
          <div className="flex flex-col items-center gap-4">
            <Loader size="lg" />
            <Text c="dimmed">Loading responders...</Text>
          </div>
        </div>
      ) : filteredResponders.length === 0 ? (
        <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
          <div className="text-gray-400 mb-4">
            <User size={48} className="mx-auto" />
          </div>
          <Text size="lg" fw={500} c="dark" mb="sm">
            No responders found
          </Text>
          <Text c="dimmed" mb="lg">
            {searchTerm || selectedStatus || selectedType
              ? 'Try adjusting your search or filters'
              : 'No responders are currently registered'}
          </Text>
          {(searchTerm || selectedStatus || selectedType) && (
            <Button
              onClick={() => {
                setSearchTerm('')
                setSelectedStatus('')
                setSelectedType('')
                setCurrentPage(1)
              }}
            >
              Clear all filters
            </Button>
          )}
        </div>
      ) : (
        <>
          <Table columns={columns} data={paginatedResponders} />
        </>
      )}
      {/* Pagination */}
      {totalPages > 1 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
        />
      )}
    </div>
  )
}
