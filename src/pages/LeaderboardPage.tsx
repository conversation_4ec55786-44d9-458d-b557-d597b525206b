import React, { useState } from 'react'
import Table from '@/components/tableProps'
import { PerformanceDetailsModal } from '@/components/leaderboard'
import { Search, Filter, Settings, CalendarDays, Download } from 'lucide-react'
import { Text } from '@mantine/core'
import Pagination from '@/components/Pagination'

const columns = [
  { key: 'rank', label: 'Rank' },
  { key: 'name', label: 'Name' },
  { key: 'totalCalls', label: 'Total Call Received' },
  { key: 'casesConverted', label: 'Cases Converted' },
  { key: 'avgDispatchTime', label: 'Average Dispatch Time' },
  { key: 'idleTime', label: 'Idle Time' },
  { key: 'conversionEfficiency', label: 'Conversion Efficiency' },
  { key: 'satisfactionRating', label: 'Satisfaction Rating' },
]

const rows = [
  {
    rank: 1,
    name: '<PERSON><PERSON> Ajagbe',
    totalCalls: 128,
    casesConverted: 102,
    avgDispatchTime: '4m 32s',
    idleTime: '12m 15s',
    conversionEfficiency: '79.7%',
    satisfactionRating: '4.8/5',
  },
  {
    rank: 2,
    name: '<PERSON>',
    totalCalls: 115,
    casesConverted: 89,
    avgDispatchTime: '5m 10s',
    idleTime: '14m 02s',
    conversionEfficiency: '77.4%',
    satisfactionRating: '4.6/5',
  },
  {
    rank: 3,
    name: 'Mary Johnson',
    totalCalls: 110,
    casesConverted: 85,
    avgDispatchTime: '4m 50s',
    idleTime: '13m 30s',
    conversionEfficiency: '76.5%',
    satisfactionRating: '4.7/5',
  },
  // ...add more rows as needed
]

const LeaderboardPage: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1)
  const [selectedDispatcher, setSelectedDispatcher] = useState<any>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const rowsPerPage = 10

  const handleRowClick = (row: any) => {
    setSelectedDispatcher(row)
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setSelectedDispatcher(null)
  }

  return (
    <div>
      <div className="flex flex-wrap items-center justify-between gap-4 mb-3">
        <Text size="md" fw={700} c="dark">
          Teams Summary
        </Text>
        <div className="flex items-center gap-3">
          <button className="flex items-center gap-1 px-2 py-1 border rounded text-sm text-gray-700 bg-white border-gray-400">
            <CalendarDays size={16} />
            Today
          </button>
          <button className="flex items-center gap-1 px-2 py-1 border rounded text-sm text-gray-700 bg-white border-gray-400">
            <Download size={16} />
            Export
          </button>
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[
          { title: 'Total Calls', value: 12 },
          { title: 'Average Dispatch Time', value: '10:00' },
          { title: 'Total Conversion Rate', value: '0%' },
          { title: 'Satisfaction Average', value: 5 },
        ].map(({ title, value }) => (
          <div
            key={title}
            className="bg-white p-6 rounded-lg shadow-sm border border-gray-200"
          >
            <Text
              size="sm"
              fw={600}
              c="dark"
              className="whitespace-nowrap overflow-hidden text-ellipsis"
            >
              {title}
            </Text>
            <Text size="3xl" fw={700} c="dark" mt={8}>
              {value}
            </Text>
          </div>
        ))}
      </div>

      <div className="flex w-full mt-2 overflow-hidden">
        <div className="flex-[2] min-w-0 bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
            <div className="relative flex-1 md:w-64">
              <span className="absolute left-3 top-2.5 text-gray-400">
                <Search size={20} />
              </span>
              <input
                type="text"
                placeholder="Search Cases"
                className="pl-9 pr-4 py-2 border border-gray-400 rounded-full text-sm w-full bg-gray-100"
              />
            </div>
            <div className="flex gap-2">
              <button className="flex items-center gap-1 px-2 py-1 text-sm bg-white border border-gray-300 rounded">
                <Filter size={16} /> Filter
              </button>
              <button className="flex items-center gap-1 px-2 py-1 text-sm bg-white border border-gray-300 rounded">
                <Settings size={16} /> Sort
              </button>
            </div>
          </div>

          <div className="overflow-auto">
            <Table columns={columns} data={rows} onRowClick={handleRowClick} />
          </div>

          <div className="mt-6">
            <Pagination
              currentPage={currentPage}
              totalPages={Math.ceil(rows.length / rowsPerPage)}
              onPageChange={setCurrentPage}
            />
          </div>
        </div>
      </div>

      {/* Performance Details Modal */}
      {selectedDispatcher && (
        <PerformanceDetailsModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          dispatcherData={{
            name: selectedDispatcher.name,
            role: 'Dispatcher',
            totalCalls: selectedDispatcher.totalCalls,
            avgCallDuration: selectedDispatcher.avgCallDuration,
            casesConverted: selectedDispatcher.casesConverted,
            conversionEfficiency: parseFloat(
              selectedDispatcher.conversionEfficiency
            ),
            avgDispatchTime: selectedDispatcher.avgDispatchTime,
            idleTime: selectedDispatcher.idleTime,
            satisfactionRating: parseFloat(
              selectedDispatcher.satisfactionRating
            ),
            lastActiveTimestamp: '15 Apr 2025 8:00 am',
          }}
        />
      )}
    </div>
  )
}

export default LeaderboardPage
