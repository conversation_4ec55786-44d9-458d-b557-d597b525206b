import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { <PERSON>R<PERSON>, Shield, User, Briefcase } from 'lucide-react'
import { LoginDesign } from '../../components/auth/LoginDesign'

const LoginPage: React.FC = () => {
  const adminUrl =
    import.meta.env.VITE_STAGE === 'prod'
      ? 'https://admin.emergencyresponseafrica.com'
      : 'http://internal-dashboard-staging.s3-website-eu-west-1.amazonaws.com'

  return (
    <div className="min-h-screen grid grid-cols-2" id="onboard-cy">
      <LoginDesign />
      <section className="my-32 w-7/12 mx-auto landing-right px-4">
        <div className="flex items-center logo-mobile">
          <Shield className="w-8 h-8 text-primary" />
          <h2 className="pl-2 font-semibold">
            ERA Dispatch &amp; Control Center
          </h2>
        </div>
        <h2 className="font-bold text-2xl mb-3" id="login-cy">
          Login
        </h2>
        <p className="font-light">Select the type of account</p>
        <section className="my-16">
          <Link
            to="/auth/dispatch-login"
            className="flex items-center justify-between bg-secondary border border-primary rounded-md py-3 px-8"
            id="click-dispatch-cy"
          >
            <div className="flex items-center">
              <User className="w-8 h-8 text-primary mr-3" />
              <div className="col-span-3">
                <h5 className="font-semibold" id="dispatch-cy">
                  Dispatcher
                </h5>
                <p>User account to manage incoming emergencies and resources</p>
              </div>
            </div>
            <ArrowRight />
          </Link>

          <a
            href={adminUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="my-5 flex items-center justify-between py-3 px-8 border relative rounded hover:bg-secondary hover:border-primary"
          >
            <div className="flex items-center">
              <Briefcase className="w-8 h-8 text-primary mr-3" />
              <div className="col-span-3">
                <h5 className="font-semibold" id="admin-cy">
                  Admin
                </h5>
                <p>Manage all other accounts and partners.</p>
              </div>
            </div>
            <ArrowRight />
          </a>
        </section>
      </section>
    </div>
  )
}

export default LoginPage
