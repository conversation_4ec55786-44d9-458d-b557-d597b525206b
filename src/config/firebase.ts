import { initializeApp } from 'firebase/app'
import { getMessaging, isSupported } from 'firebase/messaging'
import { captureException } from '@/config/sentry'

/**
 * Firebase Configuration for ERA Dispatch Application
 *
 * This module provides Firebase configuration with messaging support
 * for push notifications functionality.
 */

// Firebase configuration object
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
}

// VAPID key for push notifications
export const VAPID_KEY = import.meta.env.VITE_FIREBASE_VAPID_KEY

// Initialize Firebase app
let firebaseApp: ReturnType<typeof initializeApp> | null = null
let messaging: ReturnType<typeof getMessaging> | null = null

/**
 * Initialize Firebase app and messaging
 */
export const initializeFirebase = async (): Promise<void> => {
  try {
    // Check if all required config values are present
    const requiredKeys = [
      'VITE_FIREBASE_API_KEY',
      'VITE_FIREBASE_AUTH_DOMAIN',
      'VITE_FIREBASE_PROJECT_ID',
      'VITE_FIREBASE_STORAGE_BUCKET',
      'VITE_FIREBASE_MESSAGING_SENDER_ID',
      'VITE_FIREBASE_APP_ID',
    ]

    const missingKeys = requiredKeys.filter(key => !import.meta.env[key])

    if (missingKeys.length > 0) {
      console.warn(
        'Firebase configuration incomplete. Missing keys:',
        missingKeys
      )
      return
    }

    // Initialize Firebase app
    firebaseApp = initializeApp(firebaseConfig)
    console.log('✅ Firebase app initialized successfully')

    // Check if messaging is supported in this environment
    const messagingSupported = await isSupported()

    if (messagingSupported) {
      messaging = getMessaging(firebaseApp)
      console.log('✅ Firebase messaging initialized successfully')
    } else {
      console.warn('⚠️ Firebase messaging is not supported in this environment')
    }
  } catch (error) {
    console.error('❌ Failed to initialize Firebase:', error)
    captureException(error as Error, {
      context: 'firebase_initialization',
      firebaseConfig: {
        ...firebaseConfig,
        apiKey: firebaseConfig.apiKey ? '[REDACTED]' : 'missing',
      },
    })
  }
}

/**
 * Get Firebase messaging instance
 */
export const getFirebaseMessaging = () => {
  if (!messaging) {
    console.warn(
      'Firebase messaging not initialized. Call initializeFirebase() first.'
    )
    return null
  }
  return messaging
}

/**
 * Get Firebase app instance
 */
export const getFirebaseApp = () => {
  if (!firebaseApp) {
    console.warn(
      'Firebase app not initialized. Call initializeFirebase() first.'
    )
    return null
  }
  return firebaseApp
}

/**
 * Check if Firebase messaging is available
 */
export const isMessagingAvailable = (): boolean => {
  return messaging !== null
}

/**
 * Check if the current environment supports push notifications
 */
export const isPushNotificationSupported = (): boolean => {
  return (
    'serviceWorker' in navigator &&
    'PushManager' in window &&
    'Notification' in window
  )
}

/**
 * Get current notification permission status
 */
export const getNotificationPermission = (): NotificationPermission => {
  if (!('Notification' in window)) {
    return 'denied'
  }
  return Notification.permission
}

/**
 * Request notification permission from user
 */
export const requestNotificationPermission =
  async (): Promise<NotificationPermission> => {
    try {
      if (!('Notification' in window)) {
        console.warn('This browser does not support notifications')
        return 'denied'
      }

      if (Notification.permission === 'granted') {
        return 'granted'
      }

      if (Notification.permission === 'denied') {
        console.warn('Notification permission was previously denied')
        return 'denied'
      }

      // Request permission
      const permission = await Notification.requestPermission()
      console.log('Notification permission result:', permission)

      return permission
    } catch (error) {
      console.error('Error requesting notification permission:', error)
      captureException(error as Error, {
        context: 'notification_permission_request',
      })
      return 'denied'
    }
  }

export default firebaseApp
