/**
 * API Configuration
 *
 * Environment-based configuration for API client
 */

// import { ApiEnvironmentConfig } from '@/types/api/common'

// Get current environment
const environment = import.meta.env.VITE_APP_ENVIRONMENT || 'development'

// Export current configuration
export const apiConfig = {
  baseUrl: `${import.meta.env.VITE_API_BASE_URL}/${environment}/v1`,
  timeout: 30000,
  retries: {
    attempts: 3,
    delay: 1000,
    backoff: 'exponential',
    retryCondition: (error: { response: { status: number } }) => {
      // Retry on network errors and 5xx status codes
      return (
        !error.response ||
        (error.response.status >= 500 && error.response.status < 600)
      )
    },
  },
  cache: {
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
  },
  enableLogging: true,
}

// API Endpoints
export const API_ENDPOINTS = {
  // Authentication & User Management
  DISPATCHERS: '/dispatchers',
  DISPATCHER_BY_ID: (id: string) => `/dispatchers/${id}`,

  // Cases
  CASES: '/cases',
  CASE_BY_ID: (id: string) => `/cases/${id}`,
  CASE_APPROVE: (id: string) => `/cases/${id}/approve`,
  CASE_CLOSE: (id: string) => `/cases/${id}/close`,
  CASE_UPLOAD: '/cases/upload',
  CASE_MAKE_OFFLINE: (id: string) => `/cases/${id}/makeoffline`,
  ENTITIES: '/entities',

  // Notifications
  NOTIFICATIONS: '/notifications',
  NOTIFICATION_BY_ID: (id: string) => `/notifications/${id}`,
  NOTIFICATIONS_REGISTER: '/notifications/register',

  // Inquiries
  INQUIRIES: '/inquiry',
  INQUIRY_BY_ID: (id: string) => `/inquiry/${id}`,

  // References
  REFERENCES: '/references',
  RECOMMENDATION_DIAGNOSIS: '/recommendation/diagnosis',

  // Ambulance Services
  AMBULANCE_PROVIDERS: '/ambulance-providers',
  AMBULANCE_PRICING: '/ambulance-requests/pricing',

  // External APIs
  NHIS_VERIFY: (phone: string) =>
    `http://digicomme.com:8000/api/prz/enrollees/${phone}/verify`,
} as const

// Query Keys Factory
export const createQueryKeys = (entity: string) => ({
  all: [entity] as const,
  lists: () => [entity, 'list'] as const,
  list: (filters?: Record<string, any>) => [entity, 'list', filters] as const,
  details: () => [entity, 'detail'] as const,
  detail: (id: string) => [entity, 'detail', id] as const,
  infinite: (filters?: Record<string, any>) =>
    [entity, 'infinite', filters] as const,
})

// Common Query Keys
export const QUERY_KEYS = {
  dispatchers: createQueryKeys('dispatchers'),
  cases: createQueryKeys('cases'),
  notifications: createQueryKeys('notifications'),
  inquiries: createQueryKeys('inquiries'),
  references: createQueryKeys('references'),
  ambulanceProviders: createQueryKeys('ambulance-providers'),
  ambulancePricing: createQueryKeys('ambulance-pricing'),
  precinct: createQueryKeys('precinct'),
} as const

// Mutation Keys
export const MUTATION_KEYS = {
  // Dispatcher mutations
  UPDATE_DISPATCHER: 'updateDispatcher',

  // Case mutations
  CREATE_CASE: 'createCase',
  UPDATE_CASE: 'updateCase',
  APPROVE_CASE: 'approveCase',
  CLOSE_CASE: 'closeCase',
  UPLOAD_CASE: 'uploadCase',
  INITIATE_USSD_CASE: 'initiateUssdCase',

  // Notification mutations
  MARK_NOTIFICATION_READ: 'markNotificationRead',
  REGISTER_FIREBASE_TOKEN: 'registerFirebaseToken',

  // Responder mutations
  RESPONDERS: 'responders',
  NOTIFY_RESPONDER: 'notifyResponder',

  // Inquiry mutations
  CREATE_INQUIRY: 'createInquiry',
  UPDATE_INQUIRY: 'updateInquiry',

  // External API mutations
  VERIFY_NHIS: 'verifyNhis',
} as const

export default apiConfig
