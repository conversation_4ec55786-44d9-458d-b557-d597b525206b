import * as Sentry from '@sentry/react'

/**
 * Simplified Sentry Configuration for ERA Dispatch Application
 *
 * This module provides basic Sentry configuration for error tracking
 * across all environments using a single DSN.
 */

/* eslint-disable @typescript-eslint/no-explicit-any */

/**
 * Initialize Sentry with basic configuration
 */
export const initializeSentry = (): void => {
  const dsn = import.meta.env.VITE_SENTRY_DSN

  // Don't initialize if DSN is not provided
  if (!dsn) {
    console.warn('Sentry DSN not provided - error tracking disabled')
    return
  }

  const isDevelopment = import.meta.env.DEV
  const environment = import.meta.env.VITE_APP_ENVIRONMENT || 'development'

  Sentry.init({
    dsn,
    environment,
    debug: isDevelopment,

    // Release information
    release: `era-dispatch-app@${import.meta.env.VITE_APP_VERSION || '2.0.0'}`,

    // Basic performance monitoring
    tracesSampleRate: isDevelopment ? 1.0 : 0.1,

    // Session Replay
    replaysSessionSampleRate: isDevelopment ? 1.0 : 0.1,
    replaysOnErrorSampleRate: 1.0,

    // Logs
    enableLogs: true,

    // React integration
    integrations: [
      Sentry.browserTracingIntegration(),
      Sentry.replayIntegration(),
    ],

    // Filter out non-actionable errors
    ignoreErrors: [
      // Browser extension errors
      'Non-Error promise rejection captured',
      'ResizeObserver loop limit exceeded',
      'Script error.',

      // Network errors that are not actionable
      'NetworkError',
      'Failed to fetch',
      'Load failed',

      // AWS Amplify known issues
      'The user is not authenticated',
      'No current user',
    ],

    // Default tags for all events
    initialScope: {
      tags: {
        'app.name': 'ERA Dispatch App',
        'app.version': import.meta.env.VITE_APP_VERSION || '2.0.0',
        'app.environment': environment,
      },
    },

    // Send default PII (personally identifiable information) For example, automatic IP address collection on events
    sendDefaultPii: false,
  })

  console.log(`✅ Sentry initialized for ${environment} environment`)
}

/**
 * Set user context for Sentry
 */
export const setSentryUser = (user: {
  id: string
  email?: string
  username?: string
  [key: string]: any
}): void => {
  Sentry.setUser({
    id: user.id,
    email: user.email,
    username: user.username || user.email,
  })
}

/**
 * Clear user context (on logout)
 */
export const clearSentryUser = (): void => {
  Sentry.setUser(null)
}

/**
 * Capture exception with context
 */
export const captureException = (
  error: Error,
  context?: Record<string, any>
): void => {
  Sentry.withScope((scope: Sentry.Scope) => {
    if (context) {
      Object.entries(context).forEach(([key, value]) => {
        scope.setTag(key, String(value))
      })
      scope.setContext('error_context', context)
    }

    Sentry.captureException(error)
  })
}

/**
 * Add breadcrumb for tracking user actions
 */
export const addBreadcrumb = (
  message: string,
  category: string = 'custom',
  level: Sentry.SeverityLevel = 'info',
  data?: Record<string, any>
): void => {
  Sentry.addBreadcrumb({
    message,
    category,
    level,
    data,
    timestamp: Date.now() / 1000,
  })
}

// Export Sentry for direct usage when needed
export { Sentry }
