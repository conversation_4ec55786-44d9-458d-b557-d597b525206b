import { createTheme } from '@mantine/core'
import type { MantineColorsTuple } from '@mantine/core'

// Define custom colors that match our Tailwind palette
const primary: MantineColorsTuple = [
  '#C5DCFA', // primary-lighter
  '#A8C8F5',
  '#8BB4F0',
  '#6EA0EB',
  '#518CE6',
  '#3C6287', // primary-light
  '#254769', // primary
  '#1E3A54',
  '#172D3F',
  '#10202A',
]

const secondary: MantineColorsTuple = [
  '#F5F9FF', // secondary
  '#E9F0FB', // secondary-100
  '#344054',
  '#D1DEF3',
  '#C5D5EF',
  '#B9CCEB',
  '#ADC3E7',
  '#A1BAE3',
  '#95B1DF',
  '#89A8DB',
]

const success: MantineColorsTuple = [
  '#E8F5EA',
  '#D1EBD5',
  '#BAE1C0',
  '#A3D7AB',
  '#8CCD96',
  '#75C381',
  '#5EB96C',
  '#47AF57',
  '#30A542',
  '#21B241', // successGreen
]

const grey: MantineColorsTuple = [
  '#E8F5EA',
  '#2A2525', // grey-100
  '#2A2525', // grey-200
  '#2A2525', // grey-300
  '#2A2525', // grey-400
  '#2A2525', // grey-500
  '#2A2525', // grey-600
  '#101112', // grey-700
  '#2A2525', // grey-800
  '#2A2525', // grey-900
  '#30A542',
]

export const mantineTheme = createTheme({
  /** Put your mantine theme override here */
  fontFamily: 'Poppins, system-ui, sans-serif',
  headings: {
    fontFamily: 'Manrope, system-ui, sans-serif',
    fontWeight: '600',
  },

  colors: {
    primary,
    secondary,
    success,
    grey,
  },

  primaryColor: 'primary',
  primaryShade: 6, // This corresponds to our main primary color #254769

  defaultRadius: 'md',

  components: {
    Button: {
      defaultProps: {
        radius: 'md',
      },
      styles: {
        root: {
          fontWeight: 500,
        },
      },
    },

    Input: {
      defaultProps: {
        radius: 'md',
      },
    },

    Card: {
      defaultProps: {
        radius: 'md',
        shadow: 'sm',
      },
    },

    Modal: {
      defaultProps: {
        radius: 'md',
        shadow: 'xl',
      },
    },

    Paper: {
      defaultProps: {
        radius: 'md',
      },
    },
  },

  other: {
    // Custom properties that can be accessed via theme.other
    customColors: {
      grayF0: '#F0F0F0',
      grayFA: '#FAFAFA',
      amber: '#BDA615',
      orange: '#FF932F',
      offWhite: '#F5F5F7',
      offBlack: '#222222',
      darkGray: '#2C2C2C',
      lightGray: '#8E8A8A',
      neutral10: '#1C1B1B',
      neutral35: '#545252',
    },
  },
})
