import { Dropdown, Radio } from 'antd'
import { Text } from '@mantine/core'
import { SlidersVertical } from 'lucide-react'
import type { MenuProps } from 'antd'

interface SortDropdownProps {
  sortValue: string
  onSortChange: (value: string) => void
}

export default function SortDropdown({
  sortValue,
  onSortChange,
}: SortDropdownProps) {
  const sortMenuItems: MenuProps['items'] = [
    {
      key: 'sort-content',
      label: (
        <div className="p-2 w-[250px]">
          <div className="flex items-center justify-between mb-4">
            <Text size="md" fw={600}>
              Sort
            </Text>
            <button className="text-gray-400 hover:text-gray-600">✕</button>
          </div>
          <Radio.Group
            value={sortValue}
            onChange={e => onSortChange(e.target.value)}
            className="w-full"
          >
            <div className="flex flex-col gap-3">
              <Radio value="name-asc" className="flex items-center w-full">
                Name (A–Z)
              </Radio>
              <Radio value="name-desc" className="flex items-center w-full">
                Name (Z–A)
              </Radio>
              <Radio value="date-newest" className="flex items-center w-full">
                Date Joined (Newest → Oldest)
              </Radio>
              <Radio value="date-oldest" className="flex items-center w-full">
                Date Joined (Oldest → Newest)
              </Radio>
              <Radio value="location-asc" className="flex items-center w-full">
                Location (A–Z)
              </Radio>
              <Radio value="location-desc" className="flex items-center w-full">
                Location (Z–A)
              </Radio>
            </div>
          </Radio.Group>
        </div>
      ),
    },
  ]

  return (
    <Dropdown
      menu={{ items: sortMenuItems }}
      placement="bottomRight"
      trigger={['click']}
    >
      <button className="flex items-center gap-2 px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
        <SlidersVertical size={16} /> Sort
      </button>
    </Dropdown>
  )
}
