// Helper function to truncate strings
export const truncateString = (str: string, maxLength: number): string => {
  if (!str || str.length <= maxLength) return str
  return `${str.substring(0, maxLength)}...`
}

// Helper function to get status indicator color
export const getStatusIndicatorColor = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'available':
    case 'online':
      return 'bg-green-500'
    case 'busy':
    case 'engaged':
      return 'bg-[#F6505C]'
    case 'off_duty':
    case 'offline':
      return 'bg-[#AEAAAA]'
    case 'standby':
      return 'bg-yellow-500'
    default:
      return 'bg-gray-500'
  }
}
