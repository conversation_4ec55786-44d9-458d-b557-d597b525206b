import { Dropdown, Checkbox } from 'antd'
import { Button, Text } from '@mantine/core'
import { Filter } from 'lucide-react'
import type { MenuProps } from 'antd'

interface FilterDropdownProps {
  filterStatuses: string[]
  filterTypes: string[]
  onStatusChange: (values: string[]) => void
  onTypeChange: (values: string[]) => void
  onClearFilters: () => void
}

export default function FilterDropdown({
  filterStatuses,
  filterTypes,
  onStatusChange,
  onTypeChange,
  onClearFilters,
}: FilterDropdownProps) {
  const filterMenuItems: MenuProps['items'] = [
    {
      key: 'filter-content',
      label: (
        <div className="p-4 w-[250px]">
          <div className="flex items-center justify-between mb-4">
            <Text size="md" fw={600}>
              Filter Options
            </Text>
            <button className="text-gray-400 hover:text-gray-600">✕</button>
          </div>

          <div className="space-y-6">
            {/* Responder Status */}
            <div>
              <Text size="sm" fw={500} mb="sm">
                Responder Status
              </Text>
              <Checkbox.Group
                value={filterStatuses}
                onChange={onStatusChange}
                className="w-full"
              >
                <div className="space-y-2">
                  <Checkbox value="online" className="flex items-center w-full">
                    Online
                  </Checkbox>
                  <Checkbox
                    value="engaged"
                    className="flex items-center w-full"
                  >
                    Engaged
                  </Checkbox>
                  <Checkbox
                    value="standby"
                    className="flex items-center w-full"
                  >
                    Standby
                  </Checkbox>
                  <Checkbox
                    value="offline"
                    className="flex items-center w-full"
                  >
                    Offline
                  </Checkbox>
                </div>
              </Checkbox.Group>
            </div>

            {/* Responder Type */}
            <div>
              <Text size="sm" fw={500} mb="sm">
                Responder Type
              </Text>
              <Checkbox.Group
                value={filterTypes}
                onChange={onTypeChange}
                className="w-full"
              >
                <div className="space-y-2">
                  <Checkbox
                    value="paramedic"
                    className="flex items-center w-full"
                  >
                    Paramedic
                  </Checkbox>
                  <Checkbox value="nurse" className="flex items-center w-full">
                    Nurse
                  </Checkbox>
                </div>
              </Checkbox.Group>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-between pt-2">
              <Button
                variant="outline"
                size="xs"
                c="gray"
                onClick={onClearFilters}
              >
                Cancel
              </Button>
              <Button size="xs">Apply (#)</Button>
            </div>
          </div>
        </div>
      ),
    },
  ]

  return (
    <Dropdown
      menu={{ items: filterMenuItems }}
      placement="bottomRight"
      trigger={['click']}
    >
      <button className="flex items-center gap-2 px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
        <Filter size={16} /> Filter
      </button>
    </Dropdown>
  )
}
