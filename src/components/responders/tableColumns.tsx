import { MoreVertical, Calendar } from 'lucide-react'
import { Text } from '@mantine/core'
import { format } from 'date-fns'
import { Responder } from '@/types/api/responders'
import { truncateString, getStatusIndicatorColor } from './utils'

export const getResponderTableColumns = () => [
  {
    key: 'name',
    label: 'Name',
    render: (_value: any, row: Responder) => (
      <div className="flex items-center gap-3">
        <div className="relative">
          <div
            className={`w-3 h-3 rounded-full ${getStatusIndicatorColor(row.status || '')}`}
            title={`Status: ${row.status || 'Unknown'}`}
          />
        </div>
        <Text fw={500} c="dark">
          {truncateString(
            `${row.first_name ?? ''} ${row.last_name ?? ''}`.trim(),
            25
          )}
        </Text>
      </div>
    ),
  },
  {
    key: 'responder_type',
    label: 'Type',
    render: (value: string) => <Text c="dimmed">{value || 'Unknown'}</Text>,
  },
  {
    key: 'email',
    label: 'Email',
    render: (_value: any, row: Responder) => (
      <Text c="dimmed">{row.email || 'Not provided'}</Text>
    ),
  },
  {
    key: 'phone_number',
    label: 'Phone',
    render: (_value: any, row: Responder) => (
      <Text c="dimmed">{row.phone_number || 'Not provided'}</Text>
    ),
  },
  {
    key: 'location',
    label: 'Location',
    render: (_value: any, row: Responder) => (
      <div className="space-y-1">
        <Text c="dimmed">
          {row.city
            ? `${row.city}, ${row.state}`
            : row.state || 'Location not specified'}
        </Text>
        <Text size="xs" c="dimmed" title={row.address_line_1}>
          {row.address_line_1 && truncateString(row.address_line_1, 25)}
        </Text>
      </div>
    ),
  },
  {
    key: 'registration_datetime',
    label: 'Date Joined',
    render: (value: string | number) => {
      if (!value) {
        return <Text c="dimmed">Not available</Text>
      }
      try {
        const date =
          typeof value === 'string' ? new Date(value) : new Date(value)
        return (
          <div className="flex items-center gap-2">
            <Calendar size={14} className="text-gray-400" />
            <Text size="sm" c="dimmed">
              {format(date, 'MMM dd, yyyy')}
            </Text>
          </div>
        )
      } catch {
        return <Text c="dimmed">Invalid date</Text>
      }
    },
  },
  {
    key: 'action',
    label: 'Actions',
    render: () => (
      <button className="p-2 hover:bg-gray-100 rounded-full transition-colors">
        <MoreVertical size={16} className="text-gray-600" />
      </button>
    ),
  },
]
