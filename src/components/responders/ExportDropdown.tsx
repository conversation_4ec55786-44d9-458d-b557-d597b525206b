import { Dropdown } from 'antd'
import { Text } from '@mantine/core'
import { Download, FileText, FileBarChart } from 'lucide-react'
import type { MenuProps } from 'antd'

interface ExportDropdownProps {
  onExport?: (type: 'pdf' | 'csv') => void
}

export default function ExportDropdown({ onExport }: ExportDropdownProps) {
  const exportMenuItems: MenuProps['items'] = [
    {
      key: 'export-content',
      label: (
        <div className="p-3 min-w-[150px]">
          <Text size="sm" fw={500} mb="sm">
            Export as
          </Text>
          <div className="space-y-2">
            <button
              className="flex items-center gap-3 w-full p-2 hover:bg-gray-50 rounded transition-colors"
              onClick={() => onExport?.('pdf')}
            >
              <FileText size={16} className="text-red-500" />
              <Text size="sm" c="dimmed">
                PDF
              </Text>
            </button>
            <button
              className="flex items-center gap-3 w-full p-2 hover:bg-gray-50 rounded transition-colors"
              onClick={() => onExport?.('csv')}
            >
              <FileBarChart size={16} className="text-green-600" />
              <Text size="sm" c="dimmed">
                CSV
              </Text>
            </button>
          </div>
        </div>
      ),
    },
  ]

  return (
    <Dropdown
      menu={{ items: exportMenuItems }}
      placement="bottomRight"
      trigger={['click']}
    >
      <button className="flex items-center gap-2 px-4 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
        <Download size={16} /> Export
      </button>
    </Dropdown>
  )
}
