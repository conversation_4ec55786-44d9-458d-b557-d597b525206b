import classNames from 'classnames'
import { Text } from '@mantine/core'
import { Responder } from '@/types/api/responders'

interface StatusTabsProps {
  selectedStatus: string
  onStatusChange: (status: string) => void
  respondersData?: {
    items?: Responder[]
  }
}

export default function StatusTabs({
  selectedStatus,
  onStatusChange,
  respondersData,
}: StatusTabsProps) {
  const statusTabs = [
    {
      label: 'All Responders',
      value: '',
      active: selectedStatus === '',
    },
    {
      label: 'Available',
      value: 'online',
      active: selectedStatus === 'online',
    },
    {
      label: 'Engaged',
      value: 'engaged',
      active: selectedStatus === 'engaged',
    },
    {
      label: 'Standby',
      value: 'standby',
      active: selectedStatus === 'standby',
    },
    {
      label: 'Offline',
      value: 'offline',
      active: selectedStatus === 'offline',
    },
  ]

  const getStatusCount = (value: string) => {
    if (value === '') {
      return respondersData?.items?.length || 0
    }
    return (
      respondersData?.items?.filter(
        (r: Responder) => r.status?.toLowerCase() === value.toLowerCase()
      ).length || 0
    )
  }

  return (
    <div className="flex gap-3">
      {statusTabs.map(({ label, value, active }) => (
        <button
          key={label}
          onClick={() => onStatusChange(value)}
          className={classNames(
            'flex items-center gap-1 px-2 py-1 rounded-[8px] text-sm font-medium border transition-colors',
            active
              ? 'bg-blue-100 text-blue-900 border-blue-300'
              : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
          )}
        >
          <Text size="sm" fw={500}>
            {label}
          </Text>
          <span className="inline-flex items-center justify-center w-5 h-5 text-xs font-semibold text-white bg-gray-800 rounded-[8px]">
            {getStatusCount(value)}
          </span>
        </button>
      ))}
    </div>
  )
}
