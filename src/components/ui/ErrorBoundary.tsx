import { Component, ErrorInfo, ReactNode } from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>fresh<PERSON><PERSON>, Bug, Code } from 'lucide-react'
import { captureException, addBreadcrumb } from '@/config/sentry'

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
  errorId?: string
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    const errorId = `error-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
    return { hasError: true, error, errorId }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.group('🚨 ErrorBoundary caught an error')
    console.error('Error:', error)
    console.error('Error Info:', errorInfo)
    console.error('Component Stack:', errorInfo.componentStack)
    console.error('Error Stack:', error.stack)
    console.groupEnd()

    // Store error info in state for display
    this.setState({ errorInfo })

    // Add breadcrumb for error context
    addBreadcrumb('React Error Boundary triggered', 'error', 'error', {
      errorId: this.state.errorId,
      componentStack: errorInfo.componentStack,
    })

    // Report to Sentry
    captureException(error, {
      errorBoundary: 'ErrorBoundary',
      errorId: this.state.errorId,
      componentStack: errorInfo.componentStack,
    })
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined })
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      const isDevelopment = import.meta.env.DEV
      const isImportError =
        this.state.error?.message.includes('import') ||
        this.state.error?.message.includes('module') ||
        this.state.error?.message.includes('Cannot resolve')

      return (
        <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
          <div className="sm:mx-auto sm:w-full sm:max-w-2xl">
            <div className="flex justify-center">
              {isImportError ? (
                <Code className="h-16 w-16 text-orange-500" />
              ) : (
                <AlertTriangle className="h-16 w-16 text-red-500" />
              )}
            </div>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
              {isImportError ? 'Module Import Error' : 'Something went wrong'}
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600">
              {isImportError
                ? 'There was an error loading a module. Check the console for details.'
                : 'An unexpected error occurred. Please try again.'}
            </p>
            {isDevelopment && (
              <p className="mt-1 text-center text-xs text-blue-600">
                Development Mode - Error ID: {this.state.errorId}
              </p>
            )}
          </div>

          <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-2xl">
            <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
              <div className="text-center">
                {this.state.error && (
                  <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md text-left">
                    <div className="flex items-center mb-2">
                      <Bug className="w-4 h-4 text-red-500 mr-2" />
                      <span className="text-sm font-medium text-red-700">
                        Error Details
                      </span>
                    </div>
                    <p className="text-sm text-red-700 font-mono mb-2">
                      {this.state.error.message}
                    </p>
                    {isDevelopment && this.state.error.stack && (
                      <details className="mt-2">
                        <summary className="text-xs text-red-600 cursor-pointer hover:text-red-800">
                          Show Stack Trace
                        </summary>
                        <pre className="mt-2 text-xs text-red-600 bg-red-25 p-2 rounded overflow-auto max-h-40">
                          {this.state.error.stack}
                        </pre>
                      </details>
                    )}
                    {isDevelopment && this.state.errorInfo?.componentStack && (
                      <details className="mt-2">
                        <summary className="text-xs text-red-600 cursor-pointer hover:text-red-800">
                          Show Component Stack
                        </summary>
                        <pre className="mt-2 text-xs text-red-600 bg-red-25 p-2 rounded overflow-auto max-h-40">
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </details>
                    )}
                  </div>
                )}

                <div className="space-y-3">
                  <button
                    onClick={this.handleRetry}
                    className="w-full flex justify-center items-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Try Again
                  </button>

                  <button
                    onClick={() => window.location.reload()}
                    className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    Reload Page
                  </button>

                  {isDevelopment && (
                    <button
                      onClick={() => {
                        console.clear()
                        console.log('Error Details:', this.state.error)
                        console.log('Error Info:', this.state.errorInfo)
                      }}
                      className="w-full flex justify-center items-center py-2 px-4 border border-blue-300 rounded-md shadow-sm text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <Bug className="w-4 h-4 mr-2" />
                      Log Error to Console
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}
