import { Skeleton as MantineSkeleton } from '@mantine/core'

interface SkeletonProps {
  height?: number | string
  width?: number | string
  radius?: number | string
  className?: string
  animate?: boolean
}

export function Skeleton({
  height = 20,
  width = '100%',
  radius = 'md',
  className = '',
  animate = true,
}: SkeletonProps) {
  return (
    <MantineSkeleton
      height={height}
      width={width}
      radius={radius}
      className={className}
      animate={animate}
    />
  )
}

// Dashboard specific skeleton components
export function SummaryCardSkeleton() {
  return (
    <div className="bg-white p-4 rounded-lg shadow border-[0.5px] border-gray-80">
      <Skeleton height={16} width="60%" className="mb-2" />
      <Skeleton height={24} width="40%" />
    </div>
  )
}

export function CaseTableRowSkeleton() {
  return (
    <tr className="border-b border-gray-100">
      <td className="px-4 py-3">
        <Skeleton height={16} width="80%" />
      </td>
      <td className="px-4 py-3">
        <Skeleton height={16} width="90%" />
      </td>
      <td className="px-4 py-3">
        <Skeleton height={16} width="70%" />
      </td>
      <td className="px-4 py-3">
        <Skeleton height={16} width="60%" />
      </td>
      <td className="px-4 py-3">
        <Skeleton height={20} width={60} radius="xl" />
      </td>
    </tr>
  )
}

export function ResponderCardSkeleton() {
  return (
    <div className="bg-white p-3 rounded-lg border border-gray-200 flex items-center gap-3">
      <Skeleton height={40} width={40} radius="50%" />
      <div className="flex-1">
        <Skeleton height={14} width="70%" className="mb-1" />
        <Skeleton height={12} width="50%" />
      </div>
    </div>
  )
}

export function ResourceTabSkeleton() {
  return (
    <div className="space-y-3">
      {Array.from({ length: 6 }).map((_, index) => (
        <ResponderCardSkeleton key={index} />
      ))}
    </div>
  )
}
