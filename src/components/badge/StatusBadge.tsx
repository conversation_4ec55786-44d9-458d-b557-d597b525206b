import React from 'react'
import classNames from 'classnames'

type BadgeType = 'insurance' | 'severity'

interface StatusBadgeProps {
  type: BadgeType
  value: string
  className?: string
}

const StatusBadge: React.FC<StatusBadgeProps> = ({
  type,
  value,
  className,
}) => {
  const v = String(value || '')
    .trim()
    .toLowerCase()

  const base =
    'inline-flex items-center px-2 py-0.5 rounded-md text-xs font-semibold tracking-wide select-none'

  let styles = ''
  let text = value ?? ''

  if (type === 'insurance') {
    const isRegistered = v === 'registered'
    text = isRegistered ? 'Registered' : 'Not Registered'

    styles = isRegistered
      ? [
          // green chip + soft “sparkle” glow
          'text-green-700 bg-green-50 ring-1 ring-green-200',
          'shadow-[0_0_10px_rgba(16,185,129,0.25)]',
        ].join(' ')
      : [
          // black text, subtle shadow
          'text-gray-800 bg-gray-100 ring-1 ring-gray-300',
          'shadow-[0_1px_4px_rgba(0,0,0,0.12)]',
        ].join(' ')
  }

  if (type === 'severity') {
    if (v === 'high') {
      text = 'High'
      styles = 'bg-red-600 text-white'
    } else if (v === 'medium') {
      text = 'Medium'
      styles = 'bg-yellow-300 text-gray-900'
    } else if (v === 'low') {
      text = 'Low'
      styles = 'bg-gray-300 text-gray-800'
    } else {
      // fallback
      styles = 'bg-gray-200 text-gray-800'
    }
  }

  return <span className={classNames(base, styles, className)}>{text}</span>
}

export default StatusBadge
