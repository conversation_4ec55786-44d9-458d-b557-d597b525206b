import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import {
  LayoutDashboard,
  Users,
  Map,
  HelpCircle,
  Hospital,
  Bell,
  Trophy,
  User,
  Ambulance,
  Cog,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react'
import { Tooltip } from '@mantine/core'
import { ERALogo } from '../../assets/icons/ERALogo'
import { edoLogo, lagosLogo, ogunLogo, bayelsaLogo } from '../../assets/images'
import { useDispatcher } from '@/providers/DispatcherProvider'

interface SidebarProps {
  isCollapsed: boolean
  onToggle: () => void
}

interface MenuItem {
  path: string
  icon: React.ComponentType<{ className?: string }>
  label: string
}

const precinctLogos = {
  'edo dispatch center': edoLogo,
  'lagos dispatch center': lagosLogo,
  'ogun dispatch center': ogunLogo,
  'bayelsa dispatch center': bayelsaLogo,
} as const

const getPrecinctLogo = (precinct?: string) => {
  if (!precinct) return edoLogo
  const key = precinct.trim().toLowerCase()
  return precinctLogos[key as keyof typeof precinctLogos] || edoLogo
}

const precinctLogo = (precinct: string) => (
  <img src={getPrecinctLogo(precinct)} alt="Logo" width={40} height={40} />
)

const Sidebar: React.FC<SidebarProps> = ({ isCollapsed, onToggle }) => {
  const location = useLocation()
  const { dispatcher } = useDispatcher()

  const isActive = (path: string) =>
    location.pathname === path
      ? 'bg-primary text-white rounded-lg font-semibold'
      : 'text-gray-700 hover:bg-primary-light hover:text-primary'

  const mainMenuItems: MenuItem[] = [
    { path: '/dashboard', icon: LayoutDashboard, label: 'Dashboard' },
    { path: '/cases', icon: Users, label: 'Case Overview' },
    { path: '/ambulance-request', icon: Ambulance, label: 'Ambulance Request' },
    { path: '/map', icon: Map, label: 'Map' },
  ]

  const resourceMenuItems: MenuItem[] = [
    { path: '/inquiries', icon: HelpCircle, label: 'Inquiries' },
    { path: '/responders', icon: User, label: 'Responders' },
    { path: '/dispatchers', icon: User, label: 'Dispatchers' },
    {
      path: '/ambulance-providers',
      icon: Ambulance,
      label: 'Ambulance Providers',
    },
    { path: '/hospitals', icon: Hospital, label: 'Hospitals' },
    { path: '/registered-users', icon: Users, label: 'Registered Users' },
  ]

  const supportMenuItems: MenuItem[] = [
    { path: '/leaderboard', icon: Trophy, label: 'Leaderboard' },
    { path: '/profile', icon: Cog, label: 'Profile' },
    { path: '/notifications', icon: Bell, label: 'Notifications' },
  ]

  const renderMenuItem = (item: MenuItem) => {
    const IconComponent = item.icon
    const linkContent = (
      <Link
        to={item.path}
        className={`px-4 py-2 flex items-center rounded transition-all duration-200 ${isActive(item.path)} ${
          isCollapsed ? 'justify-center' : ''
        }`}
        aria-label={item.label}
      >
        <IconComponent
          className={`w-4 h-4 ${
            location.pathname === item.path ? 'text-white' : ''
          } ${isCollapsed ? '' : 'mr-3'}`}
        />
        {!isCollapsed && (
          <span className="font-heading text-sm hover:text-gray-300">
            {item.label}
          </span>
        )}
      </Link>
    )

    if (isCollapsed) {
      return (
        <Tooltip label={item.label} position="right" withArrow key={item.path}>
          <li>{linkContent}</li>
        </Tooltip>
      )
    }

    return <li key={item.path}>{linkContent}</li>
  }

  return (
    <aside
      id="main-sidebar"
      className={`fixed top-0 left-0 h-screen bg-white shadow-lg transition-all duration-300 ease-in-out z-50 ${
        isCollapsed ? 'w-16' : 'w-64'
      }`}
      role="navigation"
      aria-label="Main navigation"
      aria-expanded={!isCollapsed}
    >
      <div className="flex flex-col h-full">
        {/* Header with Client Logo and Toggle */}
        <div className="p-4 bg-white">
          <div className="flex items-center justify-between">
            {!isCollapsed && (
              <div className="flex items-center space-x-2">
                {precinctLogo(
                  dispatcher?.precinct_name || 'Edo Dispatch Center'
                )}
                <span className="font-heading font-semibold text-gray-800">
                  {dispatcher?.precinct_name || 'Dispatch Center'}
                </span>
              </div>
            )}
            {isCollapsed && (
              <div className="flex justify-center w-full">
                {precinctLogo(
                  dispatcher?.precinct_name || 'Edo Dispatch Center'
                )}
              </div>
            )}
          </div>
        </div>

        {/* Navigation Menu */}
        <div className="flex-1 overflow-y-auto">
          <nav className="mt-4 px-2">
            {/* Main Section */}
            <div className="bg-light-blueOne rounded-lg py-2 mb-4">
              {!isCollapsed && (
                <h2 className="text-gray-500 uppercase px-4 text-xs font-heading font-semibold mb-2">
                  Main
                </h2>
              )}
              <ul className="space-y-1">{mainMenuItems.map(renderMenuItem)}</ul>
            </div>

            {/* Resources & Support Section */}
            <div className="bg-light-blueOne rounded-lg py-2 mb-4">
              {!isCollapsed && (
                <h2 className="text-gray-500 uppercase px-4 text-xs font-heading font-semibold mb-2">
                  Resources & Support
                </h2>
              )}
              <ul className="space-y-1">
                {resourceMenuItems.map(renderMenuItem)}
              </ul>
            </div>

            {/* Support Section */}
            <div className="bg-light-blueOne rounded-lg py-2 mb-4">
              {!isCollapsed && (
                <h2 className="text-gray-500 uppercase px-4 text-xs font-heading font-semibold mb-2">
                  Support
                </h2>
              )}
              <ul className="space-y-1">
                {supportMenuItems.map(renderMenuItem)}
              </ul>
            </div>
          </nav>
        </div>

        {/* ERA Logo Footer - Always at bottom */}
        <div className="mt-auto p-1 bg-white">
          <div className="flex justify-center">
            <ERALogo />
          </div>
        </div>
      </div>

      {/* Toggle Button */}
      <button
        onClick={onToggle}
        onKeyDown={e => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault()
            onToggle()
          }
        }}
        className="absolute -right-3 top-12 bg-white border border-gray-200 rounded-full p-1 shadow-md hover:shadow-lg focus:outline-none transition-all duration-200 cursor-pointer"
        aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        aria-expanded={!isCollapsed}
        aria-controls="main-sidebar"
        tabIndex={0}
      >
        {isCollapsed ? (
          <ChevronRight className="w-4 h-4 text-gray-600" />
        ) : (
          <ChevronLeft className="w-4 h-4 text-gray-600" />
        )}
      </button>
    </aside>
  )
}

export default Sidebar
