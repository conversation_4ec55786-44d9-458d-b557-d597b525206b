import React, { useState, useRef, useEffect } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { Bell, User, LogOut } from 'lucide-react'
import { Avatar, Badge, Menu } from '@mantine/core'
import { useAuth } from '@/providers/AuthProvider'
import { useDispatcher } from '@/providers/DispatcherProvider'
import { useUnreadNotifications } from '@/hooks/api/useNotifications'
import NotificationsModal from '@/components/notifications/NotificationsModal'
import ROUTES from '@/utils/constants/routes'

interface HeaderProps {
  onToggleSidebar?: () => void
  isSidebarCollapsed?: boolean
}

// Route to page title mapping


const Header: React.FC<HeaderProps> = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const { user, signOut } = useAuth()
  const { dispatcher } = useDispatcher()

  // State for notifications modal
  const [showNotifications, setShowNotifications] = useState(false)
  const notificationsRef = useRef<HTMLDivElement>(null)

  // Fetch unread notifications with polling
  const {
    data: notifications,
    isLoading: isLoadingNotifications,
    refetch: refetchNotifications,
  } = useUnreadNotifications(20)

  // Handle clicking outside notifications modal
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        notificationsRef.current &&
        !notificationsRef.current.contains(event.target as Node)
      ) {
        setShowNotifications(false)
      }
    }

    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setShowNotifications(false)
      }
    }

    if (showNotifications) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleEscapeKey)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEscapeKey)
    }
  }, [showNotifications])

  const handleSignOut = async () => {
    try {
      await signOut()
    } catch (error) {
      console.error('Sign out error:', error)
    }
  }

  const handleViewProfile = () => {
    navigate('/profile')
  }

  const toggleNotifications = () => {
    setShowNotifications(!showNotifications)
  }

  // Get current page title
  const getCurrentPageTitle = (): string => {
    const currentPath = location.pathname
    return PAGE_TITLES[currentPath] || 'ERA Dispatch'
  }

  // Get user initials for avatar
  const getUserInitials = (): string => {
    if (dispatcher?.first_name || dispatcher?.last_name) {
      return `${dispatcher?.first_name?.[0] || ''}${dispatcher?.last_name?.[0] || ''}`.toUpperCase()
    }
    if (user?.attributes?.name) {
      return user.attributes.name
        .split(' ')
        .map((name: string) => name.charAt(0))
        .join('')
        .toUpperCase()
        .slice(0, 2)
    }
    if (user?.email) {
      return user.email.charAt(0).toUpperCase()
    }
    return 'U'
  }

  // Get user display name
  const getUserDisplayName = (): string => {
    return user?.attributes?.name || user?.email || 'User'
  }

  return (
    <header
      className="bg-gray-70 py-4 flex items-center justify-between px-4 sm:px-6 sticky top-0 z-40"
      role="banner"
    >
      {/* Left side - Page title */}
      <div className="">
        <h2 className="text-sm font-semibold font-heading truncate">
          {getCurrentPageTitle()}
        </h2>
        <h1 className="font-bold font-heading truncate">
          Dispatch and Control
        </h1>
      </div>

      {/* Right side - Notifications and user avatar */}
      <div className="flex items-center gap-6">
        <div
          className="h-10 flex items-center justify-center bg-era-red font-semibold transition-all text-white px-4 py-2 rounded-[10px] text-xs"
          style={{ boxShadow: '2px 5px 30px rgba(0, 0, 0, 0.25)' }}
          // onClick={toggleModal}
        >
          Report New Case
        </div>

        {/* Notifications */}
        <div className="relative" ref={notificationsRef}>
          <button
            onClick={toggleNotifications}
            className="p-2 rounded-full text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors cursor-pointer"
            aria-label="View notifications"
            aria-describedby="notification-count"
          >
            {notifications && notifications.length > 0 && (
              <Badge
                size="xs"
                color="red"
                variant="filled"
                className="absolute -top-1 -right-1"
                aria-hidden="true"
              >
                {notifications.length > 99 ? '99+' : notifications.length}
              </Badge>
            )}
            <Bell className="h-5 w-5" />
          </button>

          {/* Notifications Modal */}
          {showNotifications && (
            <NotificationsModal
              notifications={notifications || []}
              isLoading={isLoadingNotifications}
              onClose={() => setShowNotifications(false)}
              onRefresh={refetchNotifications}
            />
          )}
        </div>

        {/* User avatar and info */}
        <div className="flex items-center space-x-2 sm:space-x-3">
          {/* User avatar with dropdown */}
          <Menu
            shadow="md"
            width={200}
            position="bottom-end"
            trigger="hover"
            openDelay={100}
            closeDelay={300}
          >
            <Menu.Target>
              <button
                className="cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-full flex items-center space-x-2 hover:bg-gray-100 p-1 transition-colors"
                aria-label={`User menu for ${getUserDisplayName()}`}
                title={getUserDisplayName()}
              >
                <Avatar
                  size="md"
                  radius="xl"
                  color="primary"
                  className="cursor-pointer transition-all"
                  src={dispatcher?.profile_image_url || undefined}
                >
                  {!dispatcher?.profile_image_url && getUserInitials()}
                </Avatar>
              </button>
            </Menu.Target>

            <Menu.Dropdown>
              <Menu.Label>
                {dispatcher?.first_name && dispatcher?.last_name
                  ? `${dispatcher.first_name} ${dispatcher.last_name}`
                  : getUserDisplayName()}
              </Menu.Label>
              <Menu.Item
                leftSection={<User className="h-4 w-4" />}
                onClick={handleViewProfile}
              >
                View Profile
              </Menu.Item>
              <Menu.Divider />
              <Menu.Item
                leftSection={<LogOut className="h-4 w-4" />}
                onClick={handleSignOut}
                color="red"
              >
                Logout
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </div>

        {/* <MoreVertical className="text-blue-60" /> */}
      </div>
    </header>
  )
}

export default Header
