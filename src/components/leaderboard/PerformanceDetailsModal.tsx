import React from 'react'
import { X, CalendarDays, Star } from 'lucide-react'
import { Text, useMantineTheme } from '@mantine/core'
import ReactECharts from 'echarts-for-react'

interface PerformanceDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  dispatcherData: {
    name: string
    role: string
    avatar?: string
    totalCalls: number
    avgCallDuration: number
    casesConverted: number
    conversionEfficiency: number
    avgDispatchTime: string
    idleTime: string
    satisfactionRating: number
    lastActiveTimestamp: string
  }
}

const PerformanceDetailsModal: React.FC<PerformanceDetailsModalProps> = ({
  isOpen,
  onClose,
  dispatcherData,
}) => {
  const theme = useMantineTheme()

  if (!isOpen) return null

  // Sample data for the area chart
  const chartData = {
    xAxis: {
      type: 'category',
      data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: { color: '#6B7280' },
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 10,
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: { color: '#6B7280' },
      splitLine: { show: true },
    },
    series: [
      {
        name: 'Performance',
        type: 'line',
        data: [4, 6, 8, 9, 7, 3, 6],
        smooth: true,
        lineStyle: {
          color: '#EF4444',
          width: 3,
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(239, 68, 68, 0.3)' },
              { offset: 1, color: 'rgba(239, 68, 68, 0.05)' },
            ],
          },
        },
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: '#3B82F6',
          borderColor: '#FFFFFF',
          borderWidth: 2,
        },
        markPoint: {
          data: [
            { type: 'max', name: 'Max' },
            { type: 'min', name: 'Min' },
          ],
          symbol: 'circle',
          symbolSize: 13,
          itemStyle: {
            color: '#3B82F6',
            borderColor: '#FFFFFF',
            borderWidth: 2,
          },
          label: {
            show: false,
          },
        },
      },
    ],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: '#FFFFFF',
      borderColor: '#E5E7EB',
      borderWidth: 1,
      textStyle: {
        color: '#374151',
      },
    },
  }

  // Sample call log data
  const callLogData = [
    {
      callId: '#2373',
      callDuration: '00:05:55',
      conversionStatus: 'Converted',
      dispatchTime: '00:05:55',
      satisfactionRating: 3,
    },
    {
      callId: '#2374',
      callDuration: '00:03:42',
      conversionStatus: 'Not Converted',
      dispatchTime: '00:03:42',
      satisfactionRating: 4,
    },
    {
      callId: '#2375',
      callDuration: '00:07:18',
      conversionStatus: 'Converted',
      dispatchTime: '00:07:18',
      satisfactionRating: 5,
    },
    {
      callId: '#2376',
      callDuration: '00:04:25',
      conversionStatus: 'Converted',
      dispatchTime: '00:04:25',
      satisfactionRating: 3,
    },
    {
      callId: '#2377',
      callDuration: '00:06:12',
      conversionStatus: 'Not Converted',
      dispatchTime: '00:06:12',
      satisfactionRating: 4,
    },
  ]

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map(star => (
          <Star
            key={star}
            size={16}
            className={
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }
          />
        ))}
      </div>
    )
  }

  return (
    <div className="fixed inset-0 flex items-center  bg-black/35  justify-center z-50 p-4">
      <div className="px-6 bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200">
          <Text size="md" fw={600} c="primary.6">
            Performance Details
          </Text>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            aria-label="Close modal"
          >
            <X size={20} className="text-gray-500" />
          </button>
        </div>

        {/* Dispatcher Info */}
        <div className="py-3.5 ">
          <div className="flex items-center gap-4">
            <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
              <span className="text-lg font-semibold text-gray-600">
                {dispatcherData.name
                  .split(' ')
                  .map(n => n[0])
                  .join('')}
              </span>
            </div>
            <div>
              <Text size="md" fw={500} c={theme.other.customColors.neutral10}>
                {dispatcherData.name}
              </Text>
              <Text size="sm" c={theme.other.customColors.neutral10}>
                {dispatcherData.role}
              </Text>
            </div>
          </div>
        </div>

        {/* Performance Summary */}
        <div className="py-3.5">
          <div className="flex items-center justify-between mb-2">
            <Text size="md" fw={700} c="dark">
              Performance Summary
            </Text>
            <button className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
              <CalendarDays size={16} />
              <Text size="sm" fw={500} c={theme.other.customColors.neutral35}>
                Today
              </Text>
            </button>
          </div>

          <div className="grid grid-cols-4 gap-0 border border-gray-200 rounded-lg overflow-hidden">
            <div className="p-2 border-r border-b border-gray-200">
              <Text size="sm" c="grey.6" mb={4}>
                Total Call Received
              </Text>
              <Text size="sm" fw={500} c="grey.6">
                {dispatcherData.totalCalls}
              </Text>
            </div>
            <div className="p-2 border-r border-b border-gray-200">
              <Text size="sm" c="grey.6" mb={4}>
                Average Call Duration
              </Text>
              <Text size="sm" fw={500} c="grey.6">
                {dispatcherData.avgCallDuration || 13}
              </Text>
            </div>
            <div className="p-2 border-r border-b border-gray-200">
              <Text size="sm" c="grey.6" mb={4}>
                Cases Converted
              </Text>
              <Text size="sm" fw={500} c="grey.6">
                {dispatcherData.casesConverted}
              </Text>
            </div>
            <div className="p-2 border-b border-gray-200">
              <Text size="sm" c="grey.6" mb={4}>
                Conversion Efficiency
              </Text>
              <Text size="sm" fw={500} c="grey.6">
                {dispatcherData.conversionEfficiency}%
              </Text>
            </div>
            <div className="p-2 border-r border-gray-200">
              <Text size="sm" c="grey.6" mb={4}>
                Average Dispatch Time
              </Text>
              <Text size="sm" fw={500} c="grey.6">
                {dispatcherData.avgDispatchTime}
              </Text>
            </div>
            <div className="p-2 border-r border-gray-200">
              <Text size="sm" c="grey.6" mb={4}>
                Idle Time
              </Text>
              <Text size="sm" fw={500} c="grey.6">
                {dispatcherData.idleTime}
              </Text>
            </div>
            <div className="p-2 border-r border-gray-200">
              <Text size="sm" c="grey.6" mb={4}>
                Satisfaction Rating
              </Text>
              <div className="flex items-center gap-2">
                {renderStars(dispatcherData.satisfactionRating)}
                <Text size="sm" c="grey.6">
                  {dispatcherData.satisfactionRating}/5
                </Text>
              </div>
            </div>
            <div className="p-2">
              <Text size="sm" c="grey.6" mb={4}>
                Last Active Timestamp
              </Text>
              <Text size="sm" fw={500} c="grey.6">
                {dispatcherData.lastActiveTimestamp}
              </Text>
            </div>
          </div>
        </div>

        {/* Performance Trend */}
        <div className="py-3.5 ">
          <Text size="md" fw={700} c="dark" mb={16}>
            Performance Trend
          </Text>
          <div className="h-64">
            <ReactECharts
              option={chartData}
              style={{ height: '100%', width: '100%' }}
              opts={{ renderer: 'svg' }}
            />
          </div>
        </div>

        {/* Call Log */}
        <div className="py-3.5">
          <Text size="md" fw={700} c="dark" mb={16}>
            Call Log
          </Text>
          <div className="overflow-x-auto">
            <table className="min-w-full table-auto text-left text-sm">
              <thead className="border-b border-gray-200">
                <tr>
                  <th className="px-4 py-2">
                    <Text size="sm" fw={500} c="secondary.2">
                      Call ID
                    </Text>
                  </th>
                  <th className="px-4 py-2">
                    <Text size="sm" fw={500} c="secondary.2">
                      Call Duration
                    </Text>
                  </th>
                  <th className="px-4 py-2">
                    <Text size="sm" fw={500} c="secondary.2">
                      Conversion Status
                    </Text>
                  </th>
                  <th className="px-4 py-2">
                    <Text size="sm" fw={500} c="secondary.2">
                      Dispatch Time
                    </Text>
                  </th>
                  <th className="px-4 py-2">
                    <Text size="sm" fw={500} c="secondary.2">
                      Satisfaction Rating
                    </Text>
                  </th>
                </tr>
              </thead>
              <tbody>
                {callLogData.map((call, idx) => (
                  <tr key={idx} className="border-b border-gray-200">
                    <td className="px-4 py-3">
                      <Text size="sm" c="grey.7">
                        {call.callId}
                      </Text>
                    </td>
                    <td className="px-4 py-3">
                      <Text size="sm" c="grey.7">
                        {call.callDuration}
                      </Text>
                    </td>
                    <td className="px-4 py-3">
                      <span
                        className={`px-2 py-1 text-xs font-medium rounded-full ${
                          call.conversionStatus === 'Converted'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {call.conversionStatus}
                      </span>
                    </td>
                    <td className="px-4 py-3">
                      <Text size="sm" c="grey.7">
                        {call.dispatchTime}
                      </Text>
                    </td>
                    <td className="px-4 py-3">
                      {renderStars(call.satisfactionRating)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PerformanceDetailsModal
