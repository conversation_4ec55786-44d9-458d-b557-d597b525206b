import {
  useRouteError,
  isRouteErrorResponse,
  Link,
  useLocation,
} from 'react-router-dom'
import { AlertTriangle, Home, RefreshCw } from 'lucide-react'
import { useEffect } from 'react'
import { captureException, addBreadcrumb } from '@/config/sentry'
import ROUTES from '@/utils/constants/routes'

export function RouteErrorBoundary() {
  const error = useRouteError()
  const location = useLocation()

  let errorMessage = 'An unexpected error occurred'
  let errorStatus = 500

  if (isRouteErrorResponse(error)) {
    errorMessage = error.statusText || error.data?.message || errorMessage
    errorStatus = error.status
  } else if (error instanceof Error) {
    errorMessage = error.message
  }

  // Report error to Sentry when component mounts
  useEffect(() => {
    const errorId = `route-error-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`

    // Add breadcrumb for route error context
    addBreadcrumb('Route Error Boundary triggered', 'navigation', 'error', {
      errorId,
      route: location.pathname,
      errorStatus,
      errorMessage,
    })

    // Report to Sentry
    if (error instanceof Error) {
      captureException(error, {
        errorBoundary: 'RouteErrorBoundary',
        errorId,
        route: location.pathname,
        errorStatus,
        errorMessage,
      })
    } else {
      // For non-Error route errors, create a synthetic error
      const syntheticError = new Error(
        `Route Error ${errorStatus}: ${errorMessage}`
      )
      captureException(syntheticError, {
        errorBoundary: 'RouteErrorBoundary',
        errorId,
        route: location.pathname,
        errorStatus,
        errorMessage,
        originalError: error,
      })
    }
  }, [error, location.pathname, errorMessage, errorStatus])

  const handleRetry = () => {
    window.location.reload()
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <AlertTriangle className="h-16 w-16 text-red-500" />
        </div>
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          {errorStatus === 404 ? 'Page Not Found' : 'Something went wrong'}
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          {errorStatus === 404
            ? "The page you're looking for doesn't exist."
            : 'An error occurred while loading this page.'}
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center">
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-700">
                <strong>Error {errorStatus}:</strong> {errorMessage}
              </p>
            </div>

            <div className="space-y-3">
              <button
                onClick={handleRetry}
                className="w-full flex justify-center items-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Try Again
              </button>

              <Link
                to={ROUTES.dashboard}
                className="w-full flex justify-center items-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                <Home className="w-4 h-4 mr-2" />
                Go to Dashboard
              </Link>

              <Link
                to={ROUTES.home}
                className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                Go to Home
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
