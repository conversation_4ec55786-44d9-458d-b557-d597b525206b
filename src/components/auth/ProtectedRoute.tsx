import { ReactNode } from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { useAuth } from '@/providers/AuthProvider'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import ROUTES from '@/utils/constants/routes'

interface ProtectedRouteProps {
  children: ReactNode
  fallback?: ReactNode
  redirectTo?: string
}

/**
 * ProtectedRoute component that ensures only authenticated users can access certain routes
 * Redirects unauthenticated users to the login page with return URL
 */
export function ProtectedRoute({
  children,
  fallback,
  redirectTo = ROUTES.login,
}: ProtectedRouteProps) {
  const { isAuthenticated, isLoading, user } = useAuth()
  const location = useLocation()

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center">
          <LoadingSpinner />
        </div>
      )
    )
  }

  // If user is not authenticated, redirect to login with return URL
  if (!isAuthenticated) {
    return (
      <Navigate
        to={redirectTo}
        state={{ from: location.pathname + location.search }}
        replace
      />
    )
  }

  // If user is in a challenge state (needs to set password, MFA, etc.)
  if (user?.challengeName) {
    switch (user.challengeName) {
      case 'NEW_PASSWORD_REQUIRED':
        return <Navigate to={ROUTES.setPassword} replace />
      case 'SMS_MFA':
      case 'SOFTWARE_TOKEN_MFA':
        return <Navigate to={ROUTES.phoneVerification} replace />
      default:
        // For unknown challenges, redirect to login
        return <Navigate to={ROUTES.login} replace />
    }
  }

  // User is authenticated and has no pending challenges
  return <>{children}</>
}

/**
 * Higher-order component version of ProtectedRoute
 */
export function withProtectedRoute<P extends object>(
  Component: React.ComponentType<P>,
  options?: Omit<ProtectedRouteProps, 'children'>
) {
  return function ProtectedComponent(props: P) {
    return (
      <ProtectedRoute {...options}>
        <Component {...props} />
      </ProtectedRoute>
    )
  }
}
