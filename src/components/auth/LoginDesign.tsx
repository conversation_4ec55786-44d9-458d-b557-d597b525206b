import React from 'react'
import {
  quote,
  hospital,
  donut,
  lShape,
  dotBox,
  eraLogoNoText,
} from '../../assets/images'

export const LoginDesign: React.FC = () => {
  return (
    <div
      className="min-h-full text-white bg-cover bg-no-repeat landing-left"
      style={{ backgroundImage: `url(${hospital})` }}
    >
      <div className="bg-primary/90 h-full">
        <div className="flex flex-col py-12 w-7/12 mx-auto">
          <div className="flex items-center">
            <img src={eraLogoNoText} alt="ERA Logo" width={35} height={40.83} />
            <h2 className="pl-2 font-semibold">
              ERA Dispatch &amp; Control Center
            </h2>
          </div>
          <div className="self-end mr-11 my-14">
            <img src={dotBox} alt="Dotted box" width={60} />
          </div>
          <div className="font-inter flex flex-col">
            <div>
              <img src={quote} alt="Quote" width={20} />
            </div>
            <p className="font-light leading-7">
              An Africa where everyone can receive emergency medical care in 10
              minutes or less. We model excellence in communication and leverage
              state-of-the-art technology to ensure information flows unimpeded
            </p>
            <div className="mx-20 mt-4 text-right self-end">
              <img src={lShape} alt="l-Shape" width={20} />
            </div>
          </div>
        </div>
        <div className="absolute bottom-1">
          <img src={donut} alt="" />
        </div>
      </div>
    </div>
  )
}
