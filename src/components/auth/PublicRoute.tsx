import { ReactNode } from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { useAuth } from '@/providers/AuthProvider'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import ROUTES from '@/utils/constants/routes'

interface PublicRouteProps {
  children: ReactNode
  fallback?: ReactNode
  redirectTo?: string
  allowAuthenticated?: boolean
}

/**
 * PublicRoute component for routes that should be accessible to unauthenticated users
 * Can optionally redirect authenticated users away from auth pages
 */
export function PublicRoute({
  children,
  fallback,
  redirectTo = ROUTES.dashboard,
  allowAuthenticated = false,
}: PublicRouteProps) {
  const { isAuthenticated, isLoading, user } = useAuth()
  const location = useLocation()

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center">
          <LoadingSpinner />
        </div>
      )
    )
  }

  // If user is authenticated and we don't allow authenticated users on this route
  if (isAuthenticated && !allowAuthenticated) {
    // Check if user has pending challenges
    if (user?.challengeName) {
      switch (user.challengeName) {
        case 'NEW_PASSWORD_REQUIRED':
          return <Navigate to={ROUTES.setPassword} replace />
        case 'SMS_MFA':
        case 'SOFTWARE_TOKEN_MFA':
          return <Navigate to={ROUTES.phoneVerification} replace />
        default:
          break
      }
    }

    // Get the intended destination from location state or use default
    const from = (location.state as any)?.from || redirectTo
    return <Navigate to={from} replace />
  }

  // User is not authenticated or authenticated users are allowed
  return <>{children}</>
}

/**
 * Higher-order component version of PublicRoute
 */

export function withPublicRoute<P extends object>(
  Component: React.ComponentType<P>,
  options?: Omit<PublicRouteProps, 'children'>
) {
  return function PublicComponent(props: P) {
    return (
      <PublicRoute {...options}>
        <Component {...props} />
      </PublicRoute>
    )
  }
}
