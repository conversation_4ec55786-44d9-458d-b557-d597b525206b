import React from 'react'
import classNames from 'classnames'

interface TableColumn {
  key: string
  label: string
  render?: (value: any, row: any) => React.ReactNode
}

// Use a generic type for better type safety
interface TableProps<T = Record<string, any>> {
  columns: TableColumn[]
  data: T[]
  onRowClick?: (row: T) => void
  onCaseIdClick?: (row: T) => void
}

const CustomTable = <T extends Record<string, any>>({
  columns,
  data,
  onRowClick,
  onCaseIdClick,
}: TableProps<T>) => {
  return (
    <div className="overflow-x-auto rounded-xl shadow-sm bg-white border border-gray-200">
      <table className="min-w-full table-auto text-left text-sm">
        <thead className="text-gray-600 text-xs font-semibold border-b border-gray-200">
          <tr>
            {columns.map(col => (
              <th
                key={col.key}
                className="px-4 py-2 text-sm font-semibold text-gray-700"
              >
                {col.label}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((row, idx) => (
            <tr
              key={idx}
              className="border-b border-gray-200 cursor-pointer hover:bg-gray-100"
              onClick={() => onRowClick && onRowClick(row)}
            >
              {columns.map(col => (
                <td
                  key={col.key}
                  className={classNames(
                    'px-4 py-3 text-sm text-gray-800 whitespace-nowrap',
                    col.key === 'caseId' &&
                      '!text-blue-600 font-semibold underline cursor-pointer'
                  )}
                  onClick={e => {
                    if (col.key === 'caseId') {
                      e.stopPropagation()
                      onCaseIdClick && onCaseIdClick(row)
                    }
                  }}
                >
                  {col.render ? col.render(row[col.key], row) : row[col.key]}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

export default CustomTable
