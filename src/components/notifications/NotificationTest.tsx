import { useState } from 'react'
import {
  Bell,
  CheckCircle,
  XCircle,
  AlertCircle,
  RefreshCw,
} from 'lucide-react'
import { useNotifications } from '@/hooks/useNotifications'
import { NotificationStatus } from './NotificationPermissionBanner'
import toast from 'react-hot-toast'

/**
 * Notification Test Component
 *
 * Development component for testing notification functionality.
 * This component should only be used in development/testing environments.
 */

export function NotificationTest() {
  const {
    isSupported,
    permission,
    isEnabled,
    token,
    isLoading,
    error,
    requestPermission,
    disableNotifications,
    refreshState,
  } = useNotifications()

  const [testLoading, setTestLoading] = useState(false)

  const handleTestForegroundNotification = () => {
    setTestLoading(true)

    // Simulate a foreground notification using toast
    toast.custom(
      t => (
        <div
          className={`${
            t.visible ? 'animate-enter' : 'animate-leave'
          } max-w-md w-full bg-white shadow-lg rounded-lg pointer-events-auto flex ring-1 ring-black ring-opacity-5`}
        >
          <div className="flex-1 w-0 p-4">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <div className="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center">
                  <Bell className="h-5 w-5 text-white" />
                </div>
              </div>
              <div className="ml-3 flex-1">
                <p className="text-sm font-medium text-gray-900">
                  Test Notification
                </p>
                <p className="mt-1 text-sm text-gray-500">
                  This is a test foreground notification from ERA Dispatch App
                </p>
              </div>
            </div>
          </div>
          <div className="flex border-l border-gray-200">
            <button
              onClick={() => toast.dismiss(t.id)}
              className="w-full border border-transparent rounded-none rounded-r-lg p-4 flex items-center justify-center text-sm font-medium text-blue-600 hover:text-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Dismiss
            </button>
          </div>
        </div>
      ),
      {
        duration: 6000,
        position: 'top-right',
      }
    )

    setTimeout(() => setTestLoading(false), 1000)
  }

  const handleTestBackgroundNotification = () => {
    if (!isEnabled) {
      toast.error('Notifications must be enabled first')
      return
    }

    // In a real app, this would trigger a notification from your backend
    toast.success(
      'Background notification test would be sent from your backend'
    )
  }

  const getStatusIcon = () => {
    if (!isSupported) return <XCircle className="h-5 w-5 text-red-500" />
    if (error) return <XCircle className="h-5 w-5 text-red-500" />
    if (isEnabled) return <CheckCircle className="h-5 w-5 text-green-500" />
    if (permission === 'denied')
      return <XCircle className="h-5 w-5 text-red-500" />
    return <AlertCircle className="h-5 w-5 text-yellow-500" />
  }

  const getStatusText = () => {
    if (!isSupported) return 'Not Supported'
    if (error) return `Error: ${error}`
    if (isEnabled) return 'Enabled'
    if (permission === 'denied') return 'Blocked'
    return 'Not Enabled'
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">
          Notification System Test
        </h2>

        {/* Status Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              {getStatusIcon()}
              <div>
                <p className="text-sm font-medium text-gray-900">Status</p>
                <p className="text-sm text-gray-500">{getStatusText()}</p>
              </div>
            </div>

            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-500">Browser Support:</span>
                <span
                  className={isSupported ? 'text-green-600' : 'text-red-600'}
                >
                  {isSupported ? 'Yes' : 'No'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Permission:</span>
                <span
                  className={`${
                    permission === 'granted'
                      ? 'text-green-600'
                      : permission === 'denied'
                        ? 'text-red-600'
                        : 'text-yellow-600'
                  }`}
                >
                  {permission}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">FCM Token:</span>
                <span className={token ? 'text-green-600' : 'text-gray-400'}>
                  {token ? 'Available' : 'None'}
                </span>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <NotificationStatus />
          </div>
        </div>

        {/* Token Display */}
        {token && (
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              FCM Token (for backend integration)
            </label>
            <textarea
              value={token}
              readOnly
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm font-mono bg-gray-50"
            />
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
            <div className="flex">
              <XCircle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-3">
          {!isEnabled && (
            <button
              onClick={requestPermission}
              disabled={isLoading}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {isLoading ? (
                <>
                  <RefreshCw className="animate-spin h-4 w-4 mr-2" />
                  Requesting...
                </>
              ) : (
                <>
                  <Bell className="h-4 w-4 mr-2" />
                  Enable Notifications
                </>
              )}
            </button>
          )}

          {isEnabled && (
            <button
              onClick={disableNotifications}
              disabled={isLoading}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              Disable Notifications
            </button>
          )}

          <button
            onClick={handleTestForegroundNotification}
            disabled={testLoading}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {testLoading ? (
              <>
                <RefreshCw className="animate-spin h-4 w-4 mr-2" />
                Testing...
              </>
            ) : (
              'Test Foreground Notification'
            )}
          </button>

          <button
            onClick={handleTestBackgroundNotification}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Test Background Notification
          </button>

          <button
            onClick={refreshState}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh State
          </button>
        </div>

        {/* Instructions */}
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
          <h4 className="text-sm font-medium text-blue-900 mb-2">
            Testing Instructions
          </h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>
              1. Click &quot;Enable Notifications&quot; to request permission
            </li>
            <li>2. Test foreground notifications with the test button</li>
            <li>
              3. For background notifications, use your backend to send FCM
              messages
            </li>
            <li>4. Copy the FCM token above to use in your backend testing</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

export default NotificationTest
