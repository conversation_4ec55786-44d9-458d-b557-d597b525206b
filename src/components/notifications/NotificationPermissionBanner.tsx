import { useState, useEffect } from 'react'
import { Bell, X, AlertCircle } from 'lucide-react'
import { notificationService } from '@/services/notificationService'
import { useAuthStore } from '@/store/authStore'
import { addBreadcrumb } from '@/config/sentry'

/**
 * Notification Permission Banner Component
 *
 * Displays a banner at the top of the app requesting notification permissions
 * when they haven't been granted yet. Provides a user-friendly way to enable
 * push notifications for the ERA Dispatch Application.
 */

interface NotificationPermissionBannerProps {
  className?: string
}

export function NotificationPermissionBanner({
  className = '',
}: NotificationPermissionBannerProps) {
  const [showBanner, setShowBanner] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isDismissed, setIsDismissed] = useState(false)
  const { user } = useAuthStore()

  useEffect(() => {
    checkPermissionStatus()
  }, [])

  const checkPermissionStatus = () => {
    // Don't show banner if user dismissed it in this session
    if (isDismissed) return

    // Check if notifications are supported
    if (!('Notification' in window)) {
      return
    }

    // Check current permission status
    const permission = notificationService.getPermissionStatus()

    // Show banner only if permission is default (not granted or denied)
    if (permission === 'default') {
      setShowBanner(true)
    }
  }

  const handleEnableNotifications = async () => {
    try {
      setIsLoading(true)

      addBreadcrumb('User clicked enable notifications', 'notification', 'info')

      // Request permission and get token
      const token = await notificationService.requestPermissionAndGetToken(
        user?.sub
      )

      if (token) {
        // Permission granted and token obtained
        setShowBanner(false)

        // You can send the token to your backend here
        console.log('FCM token ready for backend:', token)

        addBreadcrumb(
          'Notifications enabled successfully',
          'notification',
          'info',
          {
            userId: user?.sub,
          }
        )
      } else {
        // Permission denied or error occurred
        console.log('Failed to get notification permission or token')
      }
    } catch (error) {
      console.error('Error enabling notifications:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleDismiss = () => {
    setShowBanner(false)
    setIsDismissed(true)

    addBreadcrumb(
      'Notification permission banner dismissed',
      'notification',
      'info'
    )
  }

  if (!showBanner) {
    return null
  }

  return (
    <div className={`bg-blue-50 border-b border-blue-200 ${className}`}>
      <div className="max-w-7xl mx-auto py-3 px-3 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between flex-wrap">
          <div className="w-0 flex-1 flex items-center">
            <span className="flex p-2 rounded-lg bg-blue-100">
              <Bell className="h-5 w-5 text-blue-600" aria-hidden="true" />
            </span>
            <p className="ml-3 font-medium text-blue-900 truncate">
              <span className="md:hidden">
                Enable notifications for dispatch updates
              </span>
              <span className="hidden md:inline">
                Stay updated with real-time dispatch notifications. Enable push
                notifications to receive important updates even when the app is
                closed.
              </span>
            </p>
          </div>
          <div className="order-3 mt-2 flex-shrink-0 w-full sm:order-2 sm:mt-0 sm:w-auto">
            <button
              onClick={handleEnableNotifications}
              disabled={isLoading}
              className="flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Enabling...
                </>
              ) : (
                <>
                  <Bell className="h-4 w-4 mr-2" />
                  Enable Notifications
                </>
              )}
            </button>
          </div>
          <div className="order-2 flex-shrink-0 sm:order-3 sm:ml-3">
            <button
              type="button"
              onClick={handleDismiss}
              className="-mr-1 flex p-2 rounded-md hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:-mr-2"
            >
              <span className="sr-only">Dismiss</span>
              <X className="h-5 w-5 text-blue-600" aria-hidden="true" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

/**
 * Notification Status Indicator Component
 *
 * Shows the current notification permission status in settings or profile
 */
interface NotificationStatusProps {
  className?: string
}

export function NotificationStatus({
  className = '',
}: NotificationStatusProps) {
  const [permission, setPermission] =
    useState<NotificationPermission>('default')
  const [isSupported, setIsSupported] = useState(false)

  useEffect(() => {
    checkNotificationSupport()
    checkPermissionStatus()
  }, [])

  const checkNotificationSupport = () => {
    setIsSupported('Notification' in window && 'serviceWorker' in navigator)
  }

  const checkPermissionStatus = () => {
    if ('Notification' in window) {
      setPermission(Notification.permission)
    }
  }

  const getStatusInfo = () => {
    if (!isSupported) {
      return {
        status: 'Not Supported',
        description: 'Push notifications are not supported in this browser',
        color: 'text-gray-500',
        bgColor: 'bg-gray-100',
        icon: AlertCircle,
      }
    }

    switch (permission) {
      case 'granted':
        return {
          status: 'Enabled',
          description: 'You will receive push notifications',
          color: 'text-green-700',
          bgColor: 'bg-green-100',
          icon: Bell,
        }
      case 'denied':
        return {
          status: 'Blocked',
          description:
            'Notifications are blocked. Enable them in browser settings.',
          color: 'text-red-700',
          bgColor: 'bg-red-100',
          icon: AlertCircle,
        }
      default:
        return {
          status: 'Not Enabled',
          description: 'Click to enable push notifications',
          color: 'text-yellow-700',
          bgColor: 'bg-yellow-100',
          icon: Bell,
        }
    }
  }

  const statusInfo = getStatusInfo()
  const StatusIcon = statusInfo.icon

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      <div className={`flex-shrink-0 p-2 rounded-full ${statusInfo.bgColor}`}>
        <StatusIcon className={`h-5 w-5 ${statusInfo.color}`} />
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900">Push Notifications</p>
        <p className="text-sm text-gray-500">{statusInfo.description}</p>
      </div>
      <div className="flex-shrink-0">
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusInfo.bgColor} ${statusInfo.color}`}
        >
          {statusInfo.status}
        </span>
      </div>
    </div>
  )
}

export default NotificationPermissionBanner
