import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { X, ChevronDown, ChevronUp } from 'lucide-react'
import { Loader, Button, Text } from '@mantine/core'
import { useMarkNotificationRead } from '@/hooks/api/useNotifications'
import { NotificationSummary } from '@/types/api/notifications'
import { toast } from 'react-hot-toast'

interface NotificationsModalProps {
  notifications: NotificationSummary[]
  isLoading: boolean
  onClose: () => void
  onRefresh: () => void
}

interface GroupedNotification {
  data_id: string
  notifications: NotificationSummary[]
  hasUnread: boolean
  timestamp: string
}

// Group notifications by related entity (case/inquiry/etc)
const groupNotifications = (
  notifications: NotificationSummary[]
): GroupedNotification[] => {
  const grouped = notifications.reduce(
    (acc, notification) => {
      // Use related_entity_id as grouping key, fallback to notification id
      const groupKey = notification.related_entity_id || notification.id

      if (!acc[groupKey]) {
        acc[groupKey] = []
      }
      acc[groupKey].push(notification)
      return acc
    },
    {} as Record<string, NotificationSummary[]>
  )

  return Object.entries(grouped).map(([data_id, notifications]) => ({
    data_id,
    notifications,
    hasUnread: notifications.some(n => n.status === 'unread'),
    timestamp: notifications[0]?.created_at || '',
  }))
}

// Extract diagnosis/case type from notification message
const extractDiagnosis = (notifications: NotificationSummary[]): string => {
  const messages = notifications.map(n => n.message)

  for (const message of messages) {
    // Try to extract meaningful case type from message
    const lowerMessage = message.toLowerCase()

    if (lowerMessage.includes('cardiac') || lowerMessage.includes('heart'))
      return 'Cardiac Emergency'
    if (lowerMessage.includes('trauma') || lowerMessage.includes('accident'))
      return 'Trauma'
    if (
      lowerMessage.includes('respiratory') ||
      lowerMessage.includes('breathing')
    )
      return 'Respiratory Emergency'
    if (
      lowerMessage.includes('stroke') ||
      lowerMessage.includes('neurological')
    )
      return 'Stroke/Neurological'
    if (lowerMessage.includes('ambulance')) return 'Ambulance Request'
    if (lowerMessage.includes('inquiry')) return 'Inquiry'
  }

  return 'General Case'
}

const NotificationsModal: React.FC<NotificationsModalProps> = ({
  notifications,
  isLoading,
  onClose,
  onRefresh,
}) => {
  const navigate = useNavigate()
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>(
    {}
  )
  const [markingAsRead, setMarkingAsRead] = useState<Record<string, boolean>>(
    {}
  )

  const markAsReadMutation = useMarkNotificationRead()

  const groupedNotifications = groupNotifications(notifications)

  const toggleGroupExpansion = (groupId: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupId]: !prev[groupId],
    }))
  }

  const markGroupAsRead = async (group: GroupedNotification) => {
    if (!group.hasUnread) return

    setMarkingAsRead(prev => ({ ...prev, [group.data_id]: true }))

    try {
      // Mark all unread notifications in the group as read
      const unreadNotifications = group.notifications.filter(
        n => n.status === 'unread'
      )

      await Promise.all(
        unreadNotifications.map(notification =>
          markAsReadMutation.mutateAsync({
            notificationId: notification.id,
            entity: 'dispatcher',
          })
        )
      )

      toast.success(
        `${unreadNotifications.length} notification${unreadNotifications.length > 1 ? 's' : ''} marked as read`
      )
      onRefresh() // Refresh the notifications list
    } catch (error) {
      console.error('Failed to mark notifications as read:', error)
      toast.error('Failed to mark notifications as read')
    } finally {
      setMarkingAsRead(prev => ({ ...prev, [group.data_id]: false }))
    }
  }

  const viewCaseDetails = (group: GroupedNotification) => {
    const caseId = group.data_id

    // Mark as read if has unread notifications
    if (group.hasUnread) {
      markGroupAsRead(group)
    }

    // Navigate based on case ID prefix
    if (caseId.startsWith('cas-')) {
      navigate(`/cases/update/${caseId}`)
    } else if (caseId.startsWith('ar-')) {
      navigate('/ambulance-request')
    } else if (caseId.startsWith('inq-')) {
      navigate('/inquiries')
    } else {
      // Default to cases page
      navigate('/cases')
    }
  }

  return (
    <div className="absolute right-0 top-full mt-2 w-96 max-w-sm bg-white rounded-lg shadow-xl border border-gray-200 z-50 max-h-96 overflow-hidden">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <Text size="lg" fw={600}>
          Notifications
        </Text>
        <button
          onClick={onClose}
          className="p-1 rounded-full hover:bg-gray-100 transition-colors"
          aria-label="Close notifications"
        >
          <X className="h-4 w-4" />
        </button>
      </div>

      {/* Content */}
      <div className="overflow-y-auto max-h-80">
        {isLoading ? (
          <div className="flex items-center justify-center p-8">
            <Loader size="md" />
          </div>
        ) : groupedNotifications.length > 0 ? (
          <div>
            {groupedNotifications.map((group, index) => {
              const diagnosis = extractDiagnosis(group.notifications)
              const isExpanded = expandedGroups[group.data_id]
              const isMarkingRead = markingAsRead[group.data_id]

              return (
                <div
                  key={group.data_id}
                  className={`border-b border-gray-100 ${index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}`}
                >
                  <div className="p-4">
                    {/* Group Header */}
                    <div className="flex justify-between items-start mb-2">
                      <Text size="sm" fw={600} c="blue" className="capitalize">
                        {diagnosis}
                      </Text>
                      <Text size="xs" c="dimmed">
                        {new Date(group.timestamp).toLocaleDateString('en-US', {
                          weekday: 'short',
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric',
                          hour: 'numeric',
                          minute: '2-digit',
                          hour12: true,
                        })}
                      </Text>
                    </div>

                    {/* Notification Summary */}
                    <div className="border border-dashed border-gray-300 rounded p-3 mb-3">
                      <div
                        className="flex items-center justify-between cursor-pointer"
                        onClick={() => toggleGroupExpansion(group.data_id)}
                      >
                        <div className="flex items-center gap-2">
                          <div
                            className={`w-2 h-2 rounded-full ${
                              group.hasUnread ? 'bg-blue-500' : 'bg-gray-300'
                            }`}
                          />
                          <Text size="xs" c="dimmed">
                            Case activities ({group.notifications.length})
                          </Text>
                        </div>
                        {isExpanded ? (
                          <ChevronUp className="h-4 w-4 text-gray-400" />
                        ) : (
                          <ChevronDown className="h-4 w-4 text-gray-400" />
                        )}
                      </div>

                      {/* Expanded Notifications */}
                      {isExpanded && (
                        <div className="mt-3 space-y-2">
                          {group.notifications.map(notification => (
                            <div
                              key={notification.id}
                              className="flex items-start gap-2 text-xs"
                            >
                              <div className="flex-shrink-0 mt-1">
                                <div
                                  className={`w-1.5 h-1.5 rounded-full ${
                                    notification.status === 'unread'
                                      ? 'bg-blue-500'
                                      : 'bg-gray-300'
                                  }`}
                                />
                              </div>
                              <div className="flex-1">
                                <Text size="xs" c="dimmed">
                                  {notification.message}
                                </Text>
                              </div>
                              <Text
                                size="xs"
                                c="dimmed"
                                className="flex-shrink-0"
                              >
                                {new Date(
                                  notification.created_at
                                ).toLocaleTimeString('en-US', {
                                  hour: 'numeric',
                                  minute: '2-digit',
                                  hour12: true,
                                })}
                              </Text>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>

                    {/* Action Buttons */}
                    <div className="flex justify-end gap-2">
                      <Button
                        size="xs"
                        variant="outline"
                        disabled={!group.hasUnread || isMarkingRead}
                        onClick={() => markGroupAsRead(group)}
                        loading={isMarkingRead}
                      >
                        Mark as read
                      </Button>
                      <Button size="xs" onClick={() => viewCaseDetails(group)}>
                        View details
                      </Button>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center p-8 text-center">
            <Text size="lg" c="dimmed" mb="xs">
              No New Alerts Available
            </Text>
            <Text size="sm" c="dimmed">
              You&apos;re all caught up!
            </Text>
          </div>
        )}
      </div>
    </div>
  )
}

export default NotificationsModal
