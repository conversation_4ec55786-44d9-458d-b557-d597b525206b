/**
 * Custom Map Markers for Different Entity Types
 * 
 * This file contains custom marker components and utilities for displaying
 * cases, responders, and hospitals on the Google Maps with distinct icons.
 */

import type { <PERSON><PERSON><PERSON><PERSON>, MockResponder, MockHospital } from '@/utils/mockData/mapData'

// Marker icon configurations
export const MARKER_ICONS = {
  case: {
    high: {
      url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M16 2C10.48 2 6 6.48 6 12c0 7.5 10 18 10 18s10-10.5 10-18c0-5.52-4.48-10-10-10z" fill="#F31222"/>
          <circle cx="16" cy="12" r="4" fill="white"/>
          <text x="16" y="16" text-anchor="middle" fill="#F31222" font-size="8" font-weight="bold">!</text>
        </svg>
      `),
      scaledSize: new google.maps.Size(32, 32),
      anchor: new google.maps.Point(16, 32),
    },
    medium: {
      url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
        <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M14 2C9.58 2 6 5.58 6 10c0 6.25 8 16 8 16s8-9.75 8-16c0-4.42-3.58-8-8-8z" fill="#FFCC00"/>
          <circle cx="14" cy="10" r="3.5" fill="white"/>
          <text x="14" y="13" text-anchor="middle" fill="#FFCC00" font-size="7" font-weight="bold">!</text>
        </svg>
      `),
      scaledSize: new google.maps.Size(28, 28),
      anchor: new google.maps.Point(14, 28),
    },
    low: {
      url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z" fill="#E6E2E1"/>
          <circle cx="12" cy="9" r="3" fill="white"/>
          <text x="12" y="12" text-anchor="middle" fill="#666" font-size="6" font-weight="bold">i</text>
        </svg>
      `),
      scaledSize: new google.maps.Size(24, 24),
      anchor: new google.maps.Point(12, 24),
    },
  },
  responder: {
    online: {
      url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
        <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M14 2C9.58 2 6 5.58 6 10c0 6.25 8 16 8 16s8-9.75 8-16c0-4.42-3.58-8-8-8z" fill="#254769"/>
          <circle cx="14" cy="10" r="3.5" fill="white"/>
          <path d="M14 7c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 7c-1.33 0-4 .67-4 2v1h8v-1c0-1.33-2.67-2-4-2z" fill="#254769"/>
        </svg>
      `),
      scaledSize: new google.maps.Size(28, 28),
      anchor: new google.maps.Point(14, 28),
    },
    busy: {
      url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
        <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M14 2C9.58 2 6 5.58 6 10c0 6.25 8 16 8 16s8-9.75 8-16c0-4.42-3.58-8-8-8z" fill="#FF932F"/>
          <circle cx="14" cy="10" r="3.5" fill="white"/>
          <path d="M14 7c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 7c-1.33 0-4 .67-4 2v1h8v-1c0-1.33-2.67-2-4-2z" fill="#FF932F"/>
        </svg>
      `),
      scaledSize: new google.maps.Size(28, 28),
      anchor: new google.maps.Point(14, 28),
    },
    offline: {
      url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z" fill="#8E8A8A"/>
          <circle cx="12" cy="9" r="3" fill="white"/>
          <path d="M12 6.5c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5 1.5-.67 1.5-1.5-.67-1.5-1.5-1.5zm0 5.5c-1 0-3 .5-3 1.5v.75h6V13.5c0-1-2-1.5-3-1.5z" fill="#8E8A8A"/>
        </svg>
      `),
      scaledSize: new google.maps.Size(24, 24),
      anchor: new google.maps.Point(12, 24),
    },
  },
  hospital: {
    public: {
      url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
        <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M15 2C10.03 2 6 6.03 6 11c0 7.5 9 17 9 17s9-9.5 9-17c0-4.97-4.03-9-9-9z" fill="#21B241"/>
          <rect x="11" y="7" width="8" height="8" rx="1" fill="white"/>
          <path d="M15 9v6M12 12h6" stroke="#21B241" stroke-width="2" stroke-linecap="round"/>
        </svg>
      `),
      scaledSize: new google.maps.Size(30, 30),
      anchor: new google.maps.Point(15, 30),
    },
    private: {
      url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
        <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M15 2C10.03 2 6 6.03 6 11c0 7.5 9 17 9 17s9-9.5 9-17c0-4.97-4.03-9-9-9z" fill="#254769"/>
          <rect x="11" y="7" width="8" height="8" rx="1" fill="white"/>
          <path d="M15 9v6M12 12h6" stroke="#254769" stroke-width="2" stroke-linecap="round"/>
        </svg>
      `),
      scaledSize: new google.maps.Size(30, 30),
      anchor: new google.maps.Point(15, 30),
    },
    'non-profit': {
      url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
        <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M15 2C10.03 2 6 6.03 6 11c0 7.5 9 17 9 17s9-9.5 9-17c0-4.97-4.03-9-9-9z" fill="#0E8428"/>
          <rect x="11" y="7" width="8" height="8" rx="1" fill="white"/>
          <path d="M15 9v6M12 12h6" stroke="#0E8428" stroke-width="2" stroke-linecap="round"/>
        </svg>
      `),
      scaledSize: new google.maps.Size(30, 30),
      anchor: new google.maps.Point(15, 30),
    },
  },
}

// Utility functions to get appropriate marker icons
export const getCaseMarkerIcon = (severity: MockCase['severity']) => {
  return MARKER_ICONS.case[severity] || MARKER_ICONS.case.low
}

export const getResponderMarkerIcon = (status: MockResponder['status']) => {
  return MARKER_ICONS.responder[status] || MARKER_ICONS.responder.offline
}

export const getHospitalMarkerIcon = (ownershipType: MockHospital['ownership_type']) => {
  return MARKER_ICONS.hospital[ownershipType] || MARKER_ICONS.hospital.public
}

// Marker creation utilities
export const createCaseMarker = (
  caseItem: MockCase,
  map: google.maps.Map,
  onClick: (caseItem: MockCase) => void
): google.maps.Marker => {
  const marker = new google.maps.Marker({
    position: {
      lat: caseItem.location_latitude,
      lng: caseItem.location_longitude,
    },
    map,
    title: `Case: ${caseItem.provisional_diagnosis} (${caseItem.severity.toUpperCase()})`,
    icon: getCaseMarkerIcon(caseItem.severity),
  })

  marker.addListener('click', () => onClick(caseItem))
  return marker
}

export const createResponderMarker = (
  responder: MockResponder,
  map: google.maps.Map,
  onClick: (responder: MockResponder) => void
): google.maps.Marker => {
  const marker = new google.maps.Marker({
    position: {
      lat: responder.location_latitude,
      lng: responder.location_longitude,
    },
    map,
    title: `${responder.responder_type.toUpperCase()}: ${responder.first_name} ${responder.last_name} (${responder.status.toUpperCase()})`,
    icon: getResponderMarkerIcon(responder.status),
  })

  marker.addListener('click', () => onClick(responder))
  return marker
}

export const createHospitalMarker = (
  hospital: MockHospital,
  map: google.maps.Map,
  onClick: (hospital: MockHospital) => void
): google.maps.Marker => {
  const marker = new google.maps.Marker({
    position: {
      lat: hospital.location_latitude,
      lng: hospital.location_longitude,
    },
    map,
    title: `${hospital.hospital_name} (${hospital.ownership_type.toUpperCase()}) - Capacity: ${hospital.capacity}`,
    icon: getHospitalMarkerIcon(hospital.ownership_type),
  })

  marker.addListener('click', () => onClick(hospital))
  return marker
}

// Info window content generators
export const generateCaseInfoWindow = (caseItem: MockCase): string => {
  return `
    <div style="max-width: 300px; padding: 12px;">
      <h3 style="margin: 0 0 8px 0; color: #254769; font-size: 16px; font-weight: 600;">
        Emergency Case
      </h3>
      <div style="margin-bottom: 8px;">
        <strong>Diagnosis:</strong> ${caseItem.provisional_diagnosis}
      </div>
      <div style="margin-bottom: 8px;">
        <strong>Severity:</strong> 
        <span style="
          background: ${caseItem.severity === 'high' ? '#F31222' : caseItem.severity === 'medium' ? '#FFCC00' : '#E6E2E1'};
          color: ${caseItem.severity === 'high' ? 'white' : 'black'};
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
        ">
          ${caseItem.severity.toUpperCase()}
        </span>
      </div>
      <div style="margin-bottom: 8px;">
        <strong>Location:</strong> ${caseItem.address_line_1}
      </div>
      <div style="margin-bottom: 8px;">
        <strong>Caller:</strong> ${caseItem.caller_first_name} ${caseItem.caller_last_name}
      </div>
      <div style="margin-bottom: 8px;">
        <strong>Status:</strong> ${caseItem.case_status.replace('_', ' ').toUpperCase()}
      </div>
      <div style="font-size: 12px; color: #666;">
        Created: ${new Date(caseItem.case_created_time).toLocaleString()}
      </div>
    </div>
  `
}

export const generateResponderInfoWindow = (responder: MockResponder): string => {
  return `
    <div style="max-width: 300px; padding: 12px;">
      <h3 style="margin: 0 0 8px 0; color: #254769; font-size: 16px; font-weight: 600;">
        ${responder.responder_type === 'medic' ? 'Medic' : 'Responder'}
      </h3>
      <div style="margin-bottom: 8px;">
        <strong>Name:</strong> ${responder.first_name} ${responder.last_name}
      </div>
      <div style="margin-bottom: 8px;">
        <strong>Status:</strong> 
        <span style="
          background: ${responder.status === 'online' ? '#21B241' : responder.status === 'busy' ? '#FF932F' : '#8E8A8A'};
          color: white;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
        ">
          ${responder.status.toUpperCase()}
        </span>
      </div>
      <div style="margin-bottom: 8px;">
        <strong>Type:</strong> ${responder.responder_type.replace('-', ' ').toUpperCase()}
      </div>
      <div style="margin-bottom: 8px;">
        <strong>Employment:</strong> ${responder.employee_type.replace('-', ' ').toUpperCase()}
      </div>
      <div style="margin-bottom: 8px;">
        <strong>Location:</strong> ${responder.address_line_1}
      </div>
      ${responder.responding_to ? `
        <div style="margin-bottom: 8px;">
          <strong>Responding to:</strong> ${responder.responding_to}
        </div>
      ` : ''}
      <div style="font-size: 12px; color: #666;">
        Contact: ${responder.phone_number}
      </div>
    </div>
  `
}

export const generateHospitalInfoWindow = (hospital: MockHospital): string => {
  return `
    <div style="max-width: 300px; padding: 12px;">
      <h3 style="margin: 0 0 8px 0; color: #254769; font-size: 16px; font-weight: 600;">
        ${hospital.hospital_name}
      </h3>
      <div style="margin-bottom: 8px;">
        <strong>Type:</strong> ${hospital.ownership_type.replace('-', ' ').toUpperCase()}
      </div>
      <div style="margin-bottom: 8px;">
        <strong>Capacity:</strong> ${hospital.capacity} beds
      </div>
      <div style="margin-bottom: 8px;">
        <strong>Status:</strong> 
        <span style="
          background: ${hospital.setup_completed ? '#21B241' : '#FF932F'};
          color: white;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
        ">
          ${hospital.setup_completed ? 'OPERATIONAL' : 'SETUP PENDING'}
        </span>
      </div>
      <div style="margin-bottom: 8px;">
        <strong>Location:</strong> ${hospital.address_line_1}
      </div>
      <div style="margin-bottom: 8px;">
        <strong>State:</strong> ${hospital.state}
      </div>
      <div style="font-size: 12px; color: #666;">
        Registered: ${new Date(hospital.registration_datetime).toLocaleDateString()}
      </div>
    </div>
  `
}
