/**
 * Map Legend Component
 * 
 * Displays a legend showing what different map markers represent
 */

import React from 'react'
import { Card, Text, Group, Stack } from '@mantine/core'
import { AlertCircle, User, Building2 } from 'lucide-react'

interface LegendItemProps {
  icon: React.ReactNode
  label: string
  color: string
  description?: string
}

const LegendItem: React.FC<LegendItemProps> = ({ icon, label, color, description }) => (
  <Group gap="xs" align="center">
    <div
      style={{
        width: 20,
        height: 20,
        backgroundColor: color,
        borderRadius: '50%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white',
        fontSize: '12px',
      }}
    >
      {icon}
    </div>
    <div>
      <Text size="sm" fw={500}>
        {label}
      </Text>
      {description && (
        <Text size="xs" c="dimmed">
          {description}
        </Text>
      )}
    </div>
  </Group>
)

interface MapLegendProps {
  className?: string
}

export const MapLegend: React.FC<MapLegendProps> = ({ className }) => {
  return (
    <Card
      shadow="md"
      padding="md"
      radius="md"
      className={`absolute top-4 left-4 z-10 bg-white ${className || ''}`}
      style={{ minWidth: 200 }}
    >
      <Text size="sm" fw={600} mb="sm">
        Map Legend
      </Text>
      
      <Stack gap="sm">
        {/* Cases */}
        <div>
          <Text size="xs" fw={500} c="dimmed" mb="xs">
            Emergency Cases
          </Text>
          <Stack gap="xs">
            <LegendItem
              icon={<AlertCircle size={12} />}
              label="High Priority"
              color="#F31222"
              description="Critical emergencies"
            />
            <LegendItem
              icon={<AlertCircle size={12} />}
              label="Medium Priority"
              color="#FFCC00"
              description="Urgent cases"
            />
            <LegendItem
              icon={<AlertCircle size={12} />}
              label="Low Priority"
              color="#E6E2E1"
              description="Non-urgent cases"
            />
          </Stack>
        </div>

        {/* Responders */}
        <div>
          <Text size="xs" fw={500} c="dimmed" mb="xs">
            Responders
          </Text>
          <Stack gap="xs">
            <LegendItem
              icon={<User size={12} />}
              label="Online"
              color="#254769"
              description="Available responders"
            />
            <LegendItem
              icon={<User size={12} />}
              label="Busy"
              color="#FF932F"
              description="Currently responding"
            />
            <LegendItem
              icon={<User size={12} />}
              label="Offline"
              color="#8E8A8A"
              description="Not available"
            />
          </Stack>
        </div>

        {/* Hospitals */}
        <div>
          <Text size="xs" fw={500} c="dimmed" mb="xs">
            Hospitals
          </Text>
          <Stack gap="xs">
            <LegendItem
              icon={<Building2 size={12} />}
              label="Public"
              color="#21B241"
              description="Government hospitals"
            />
            <LegendItem
              icon={<Building2 size={12} />}
              label="Private"
              color="#254769"
              description="Private hospitals"
            />
            <LegendItem
              icon={<Building2 size={12} />}
              label="Non-Profit"
              color="#0E8428"
              description="NGO hospitals"
            />
          </Stack>
        </div>
      </Stack>
    </Card>
  )
}
