import { useEffect, useState } from 'react'
import { ChevronDown, Search, Building2, X } from 'lucide-react'
import { Modal } from '@mantine/core'
import { Hospital } from '@/types/api/hospital'
import { ResourceTabSkeleton } from '@/components/ui/Skeleton'

// Static hospital image placeholder (unused for now)
// const hospitalImageUrl = '/hospital-placeholder.png' // You can replace with actual image

const hospitalTypes = ['All Hospitals', 'Public', 'Private', 'Specialist']

const Hospitals = ({
  hospitals,
  isLoading,
}: {
  hospitals: Hospital[]
  isLoading: boolean
}) => {
  const [filteredHospitals, setFilteredHospitals] = useState<Hospital[]>([])
  const [showHospitalDropdown, setShowHospitalDropdown] = useState(false)
  const [selectedHospitalType, setSelectedHospitalType] =
    useState<string>('All Hospitals')
  const [selectedHospitalForModal, setSelectedHospitalForModal] =
    useState<Hospital | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  useEffect(() => {
    setFilteredHospitals(hospitals)
  }, [hospitals])

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (showHospitalDropdown && !target.closest('.hospital-dropdown')) {
        setShowHospitalDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [showHospitalDropdown])

  const handleHospitalTypeSelect = (type: string) => {
    setSelectedHospitalType(type)
    setShowHospitalDropdown(false)

    if (type === 'All Hospitals') {
      setFilteredHospitals(hospitals)
      return
    }

    // Filter by hospital type (using ownership_type)
    const filtered = hospitals.filter(
      item => item?.ownership_type === type.toLowerCase()
    )
    setFilteredHospitals(filtered)
  }

  const handleViewMore = (hospital: Hospital) => {
    setSelectedHospitalForModal(hospital)
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setSelectedHospitalForModal(null)
  }

  return (
    <div className="text-xs w-full overflow-y-auto h-[400px]">
      <div className="flex items-center justify-between mb-4">
        <div>
          All:{' '}
          <span className="text-primary font-semibold">
            {filteredHospitals.length}
          </span>
        </div>

        {/* Hospital Type Filter */}
        <div className="hospital-dropdown relative">
          <div
            onClick={() => setShowHospitalDropdown(!showHospitalDropdown)}
            className="flex h-7 items-center gap-2 rounded-lg border-[0.5px] border-gray-80 px-3 py-1.5 text-dark-gray-30 transition-colors hover:shadow-lg cursor-pointer"
          >
            <span className="truncate">{selectedHospitalType}</span>
            <ChevronDown
              className={`h-4 w-4 transition-transform ${showHospitalDropdown ? 'rotate-180' : ''}`}
            />
          </div>

          {showHospitalDropdown && (
            <div className="absolute right-0 top-full z-10 mt-1 w-48 rounded-lg border border-gray-200 bg-white shadow-lg">
              <div className="py-1">
                {hospitalTypes.map(type => (
                  <div
                    key={type}
                    onClick={() => handleHospitalTypeSelect(type)}
                    className="cursor-pointer px-3 py-2 text-sm hover:bg-gray-100"
                  >
                    {type}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Search Bar */}
      <div className="relative w-full mb-4">
        <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
        <input
          type="text"
          placeholder="Search Hospitals"
          className="w-full h-[30px] rounded-lg border-[0.5px] border-gray-80 py-2 pl-7 pr-4 placeholder:text-gray-500 focus:border-primary focus:outline-none"
        />
      </div>

      {isLoading ? (
        <ResourceTabSkeleton />
      ) : filteredHospitals?.length > 0 ? (
        filteredHospitals?.map((item: Hospital) => (
          <div
            key={item.hospital_id}
            className="bg-white p-3 rounded-lg border-[0.5px] border-gray-80 mb-3 flex items-center justify-between"
          >
            <div className="flex items-center gap-3">
              {/* Static hospital image */}
              <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                <Building2 size={20} className="text-gray-500" />
              </div>

              <div>
                <div className="text-off-black font-semibold">
                  {item.hospital_name || 'Unknown Hospital'}
                </div>
                <div className="text-lighter-gray font-light">
                  {item.state || 'Unknown State'}
                </div>
              </div>
            </div>

            <div className="-mt-2">
              <div
                className="bg-white flex items-center gap-2 cursor-pointer px-3 py-1.5 rounded-lg shadow hover:shadow-lg border-[0.5px] border-gray-80 font-medium"
                onClick={() => handleViewMore(item)}
              >
                View More
              </div>
            </div>
          </div>
        ))
      ) : (
        <div className="w-full h-full -mt-2 flex flex-col justify-center items-center text-gray-500 font-medium">
          <Building2 className="mb-4" />
          <p>There are no hospitals available</p>
        </div>
      )}

      {/* View More Modal */}
      <Modal
        opened={isModalOpen}
        onClose={handleCloseModal}
        title=""
        centered
        size="sm"
        styles={{
          header: {
            display: 'none',
          },
          body: {
            padding: 0,
          },
        }}
      >
        {selectedHospitalForModal && (
          <div className="p-6">
            {/* Close button */}
            <div className="flex justify-end mb-4">
              <button
                onClick={handleCloseModal}
                className="p-1 hover:bg-gray-100 rounded-full transition-colors"
              >
                <X size={20} className="text-gray-500" />
              </button>
            </div>

            {/* Hospital details */}
            <div className="text-center">
              {/* Static hospital image */}
              <div className="w-20 h-20 mx-auto mb-4 bg-gray-200 rounded-full flex items-center justify-center">
                <Building2 size={32} className="text-gray-400" />
              </div>

              {/* Hospital name */}
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {selectedHospitalForModal.hospital_name || 'Unknown Hospital'}
              </h3>

              {/* State */}
              <p className="text-sm text-gray-600">
                {selectedHospitalForModal.state || 'Unknown State'}
              </p>
            </div>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default Hospitals
