import { useEffect, useState } from 'react'
import { ChevronDown, Search, Truck, X } from 'lucide-react'
import { Modal } from '@mantine/core'
import { AmbulanceProviderSummary } from '@/types/api/ambulance'
import { ResourceTabSkeleton } from '@/components/ui/Skeleton'

// Static ambulance image placeholder (unused for now)
// const ambulanceImageUrl = '/ambulance-placeholder.png' // You can replace with actual image

const ambulanceTypes = ['All Ambulances', 'Basic', 'Advanced', 'Critical Care']

const Ambulances = ({
  ambulances,
  isLoading,
}: {
  ambulances: AmbulanceProviderSummary[]
  isLoading: boolean
}) => {
  const [filteredAmbulances, setFilteredAmbulances] = useState<
    AmbulanceProviderSummary[]
  >([])
  const [showAmbulanceDropdown, setShowAmbulanceDropdown] = useState(false)
  const [selectedAmbulanceType, setSelectedAmbulanceType] =
    useState<string>('All Ambulances')
  const [selectedAmbulanceForModal, setSelectedAmbulanceForModal] =
    useState<AmbulanceProviderSummary | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  useEffect(() => {
    setFilteredAmbulances(ambulances)
  }, [ambulances])

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (showAmbulanceDropdown && !target.closest('.ambulance-dropdown')) {
        setShowAmbulanceDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [showAmbulanceDropdown])

  const handleAmbulanceTypeSelect = (type: string) => {
    setSelectedAmbulanceType(type)
    setShowAmbulanceDropdown(false)

    if (type === 'All Ambulances') {
      setFilteredAmbulances(ambulances)
      return
    }

    // Filter by ambulance type if needed (this would depend on API data structure)
    setFilteredAmbulances(ambulances)
  }

  const handleViewMore = (ambulance: AmbulanceProviderSummary) => {
    setSelectedAmbulanceForModal(ambulance)
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setSelectedAmbulanceForModal(null)
  }

  return (
    <div className="text-xs w-full overflow-y-auto h-[400px]">
      <div className="flex items-center justify-between mb-4">
        <div>
          All:{' '}
          <span className="text-primary font-semibold">
            {filteredAmbulances.length}
          </span>
        </div>

        {/* Ambulance Type Filter */}
        <div className="ambulance-dropdown relative">
          <div
            onClick={() => setShowAmbulanceDropdown(!showAmbulanceDropdown)}
            className="flex h-7 items-center gap-2 rounded-lg border-[0.5px] border-gray-80 px-3 py-1.5 text-dark-gray-30 transition-colors hover:shadow-lg cursor-pointer"
          >
            <span className="truncate">{selectedAmbulanceType}</span>
            <ChevronDown
              className={`h-4 w-4 transition-transform ${showAmbulanceDropdown ? 'rotate-180' : ''}`}
            />
          </div>

          {showAmbulanceDropdown && (
            <div className="absolute right-0 top-full z-10 mt-1 w-48 rounded-lg border border-gray-200 bg-white shadow-lg">
              <div className="py-1">
                {ambulanceTypes.map(type => (
                  <div
                    key={type}
                    onClick={() => handleAmbulanceTypeSelect(type)}
                    className="cursor-pointer px-3 py-2 text-sm hover:bg-gray-100"
                  >
                    {type}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Search Bar */}
      <div className="relative w-full mb-4">
        <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
        <input
          type="text"
          placeholder="Search Ambulances"
          className="w-full h-[30px] rounded-lg border-[0.5px] border-gray-80 py-2 pl-7 pr-4 placeholder:text-gray-500 focus:border-primary focus:outline-none"
        />
      </div>

      {isLoading ? (
        <ResourceTabSkeleton />
      ) : filteredAmbulances?.length > 0 ? (
        filteredAmbulances?.map((item: AmbulanceProviderSummary) => (
          <div
            key={item.ambulance_provider_id}
            className="bg-white p-3 rounded-lg border-[0.5px] border-gray-80 mb-3 flex items-center justify-between"
          >
            <div className="flex items-center gap-3">
              {/* Static ambulance image */}
              <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                <Truck size={20} className="text-gray-500" />
              </div>

              <div>
                <div className="text-off-black font-semibold">{item.name}</div>
                <div className="text-lighter-gray font-light">
                  {item.contact_city}
                </div>
              </div>
            </div>

            <div className="-mt-2">
              <div
                className="bg-white flex items-center gap-2 cursor-pointer px-3 py-1.5 rounded-lg shadow hover:shadow-lg border-[0.5px] border-gray-80 font-medium"
                onClick={() => handleViewMore(item)}
              >
                View More
              </div>
            </div>
          </div>
        ))
      ) : (
        <div className="w-full h-full -mt-2 flex flex-col justify-center items-center text-gray-500 font-medium">
          <Truck className="mb-4" />
          <p>There are no ambulances available</p>
        </div>
      )}

      {/* View More Modal */}
      <Modal
        opened={isModalOpen}
        onClose={handleCloseModal}
        title=""
        centered
        size="sm"
        styles={{
          header: {
            display: 'none',
          },
          body: {
            padding: 0,
          },
        }}
      >
        {selectedAmbulanceForModal && (
          <div className="p-6">
            {/* Close button */}
            <div className="flex justify-end mb-4">
              <button
                onClick={handleCloseModal}
                className="p-1 hover:bg-gray-100 rounded-full transition-colors"
              >
                <X size={20} className="text-gray-500" />
              </button>
            </div>

            {/* Ambulance details */}
            <div className="text-center">
              {/* Static ambulance image */}
              <div className="w-20 h-20 mx-auto mb-4 bg-gray-200 rounded-full flex items-center justify-center">
                <Truck size={32} className="text-gray-400" />
              </div>

              {/* Name */}
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {selectedAmbulanceForModal.name}
              </h3>

              {/* Contact city */}
              <p className="text-sm text-gray-600">
                {selectedAmbulanceForModal.contact_city}
              </p>
            </div>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default Ambulances
