/**
 * Service Worker Configuration Updater
 *
 * This utility updates the Firebase messaging service worker with the
 * actual Firebase configuration from environment variables.
 */

import { writeFileSync, readFileSync } from 'fs'
import { join } from 'path'

/**
 * Update the service worker with Firebase configuration
 */
export function updateServiceWorkerConfig(): void {
  try {
    const serviceWorkerPath = join(
      process.cwd(),
      'public',
      'firebase-messaging-sw.js'
    )

    // Read the service worker template
    let serviceWorkerContent = readFileSync(serviceWorkerPath, 'utf-8')

    // Firebase configuration from environment variables
    const firebaseConfig = {
      apiKey: process.env.VITE_FIREBASE_API_KEY || 'your_firebase_api_key',
      authDomain:
        process.env.VITE_FIREBASE_AUTH_DOMAIN || 'your_project.firebaseapp.com',
      projectId: process.env.VITE_FIREBASE_PROJECT_ID || 'your_project_id',
      storageBucket:
        process.env.VITE_FIREBASE_STORAGE_BUCKET || 'your_project.appspot.com',
      messagingSenderId:
        process.env.VITE_FIREBASE_MESSAGING_SENDER_ID || 'your_sender_id',
      appId: process.env.VITE_FIREBASE_APP_ID || 'your_app_id',
    }

    // Replace the placeholder configuration
    const configString = JSON.stringify(firebaseConfig, null, 2)
    serviceWorkerContent = serviceWorkerContent.replace(
      /const firebaseConfig = {[\s\S]*?}/,
      `const firebaseConfig = ${configString}`
    )

    // Write the updated service worker
    writeFileSync(serviceWorkerPath, serviceWorkerContent)

    console.log('✅ Service worker configuration updated successfully')
  } catch (error) {
    console.error('❌ Failed to update service worker configuration:', error)
  }
}

// Run if called directly
if (require.main === module) {
  updateServiceWorkerConfig()
}
