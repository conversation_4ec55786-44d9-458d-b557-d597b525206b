import { useLocation } from 'react-router-dom'
import ROUTES, { PUBLIC_ROUTES, PROTECTED_ROUTES } from './constants/routes'

/**
 * Check if a route is public (doesn't require authentication)
 */
export const isPublicRoute = (pathname: string): boolean => {
  return PUBLIC_ROUTES.some(route => {
    // Handle exact matches
    if (route === pathname) return true

    // Handle parameterized routes (e.g., /incidents/:id)
    if (route.includes(':')) {
      const routePattern = route.replace(/:[^/]+/g, '[^/]+')
      const regex = new RegExp(`^${routePattern}$`)
      return regex.test(pathname)
    }

    return false
  })
}

/**
 * Check if a route is protected (requires authentication)
 */
export const isProtectedRoute = (pathname: string): boolean => {
  return PROTECTED_ROUTES.some(route => {
    // Handle exact matches
    if (route === pathname) return true

    // Handle parameterized routes (e.g., /incidents/:id)
    if (route.includes(':')) {
      const routePattern = route.replace(/:[^/]+/g, '[^/]+')
      const regex = new RegExp(`^${routePattern}$`)
      return regex.test(pathname)
    }

    return false
  })
}

/**
 * Get the appropriate redirect route based on authentication status
 */
export const getRedirectRoute = (
  isAuthenticated: boolean,
  currentPath: string
) => {
  if (isAuthenticated) {
    // If user is authenticated and trying to access auth pages, redirect to dashboard
    if (isPublicRoute(currentPath) && currentPath.startsWith('/auth/')) {
      return ROUTES.dashboard
    }
    return null
  } else {
    // If user is not authenticated and trying to access protected routes, redirect to login
    if (isProtectedRoute(currentPath)) {
      return ROUTES.login
    }
    return null
  }
}

/**
 * Hook to get current route information
 */
export const useRouteInfo = () => {
  const location = useLocation()

  return {
    pathname: location.pathname,
    search: location.search,
    hash: location.hash,
    state: location.state,
    isPublic: isPublicRoute(location.pathname),
    isProtected: isProtectedRoute(location.pathname),
    isAuthRoute: location.pathname.startsWith('/auth/'),
  }
}

/**
 * Build a URL with query parameters
 */
export const buildUrl = (
  path: string,
  params?: Record<string, string | number>
) => {
  if (!params) return path

  const searchParams = new URLSearchParams()
  Object.entries(params).forEach(([key, value]) => {
    searchParams.append(key, String(value))
  })

  return `${path}?${searchParams.toString()}`
}

/**
 * Extract route parameters from a parameterized route
 */
export const extractRouteParams = (
  routePattern: string,
  actualPath: string
): Record<string, string> => {
  const params: Record<string, string> = {}

  const patternParts = routePattern.split('/')
  const pathParts = actualPath.split('/')

  if (patternParts.length !== pathParts.length) {
    return params
  }

  // Check if the non-parameter parts match
  for (let i = 0; i < patternParts.length; i++) {
    const patternPart = patternParts[i]
    const pathPart = pathParts[i]

    if (!patternPart.startsWith(':') && patternPart !== pathPart) {
      return params // Routes don't match
    }
  }

  // Extract parameters
  patternParts.forEach((part, index) => {
    if (part.startsWith(':')) {
      const paramName = part.slice(1)
      params[paramName] = pathParts[index]
    }
  })

  return params
}
