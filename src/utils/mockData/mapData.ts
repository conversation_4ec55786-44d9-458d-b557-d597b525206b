/**
 * Mock Data for Map Components
 *
 * This file contains mock data for cases, responders, and hospitals
 * to be displayed on the map. This data structure is designed to be
 * easily replaceable with live API endpoints in the future.
 */

// Case Mock Data
export interface MockCase {
  address_line_1: string
  caller_first_name: string
  caller_last_name: string
  caller_phone_number: string
  case_created_time: string
  case_id: string
  case_open_time: string
  location_latitude: number
  location_longitude: number
  case_status: 'open' | 'closed' | 'in_progress' | 'pending'
  incident_description: string
  provisional_diagnosis: string
  severity: 'low' | 'medium' | 'high'
  source: string
  arrival_time: string | null
}

// Responder Mock Data
export interface MockResponder {
  location_longitude: number
  profile_image_url: string
  status: 'online' | 'offline' | 'busy'
  registration_datetime: number
  responder_type: 'medic' | 'non-medic'
  email: string
  country: string
  state: string
  last_name: string
  address_line_1: string
  precinct_id: string
  first_name: string
  location_latitude: number
  phone_number: string
  responder_id: string
  employee_type: 'full-time' | 'part-time' | 'volunteer'
  responding_to: string | null
}

// Hospital Mock Data
export interface MockHospital {
  capacity: number
  hospital_id: string
  ownership_type: 'public' | 'private' | 'non-profit'
  hospital_name: string
  location_longitude: number
  registration_datetime: string
  setup_completed: boolean
  geohash_key: string
  country: string
  state: string
  precinct_id: string
  address_line_1: string
  location_latitude: number
  geohash: string
  precinct_name: string
  registration_datetime_pk: string
}

// Mock Cases Data
export const mockCases: MockCase[] = [
  {
    address_line_1: '20559 Collier Lights',
    caller_first_name: 'Rachel',
    caller_last_name: 'Labadie',
    caller_phone_number: '+2344310568971',
    case_created_time: '2025-07-31T20:18:05.522Z',
    case_id: 'cas-636f8439b47f10b201164ae51d4550c3',
    case_open_time: '2025-07-31T20:18:20.172Z',
    location_latitude: 6.246023999999998,
    location_longitude: 5.5813541,
    case_status: 'open',
    incident_description:
      'Autem nostrum illo distinctio delectus delectus natus perferendis quo quo.',
    provisional_diagnosis: 'Breathing Problems',
    severity: 'high',
    source: 'web-app',
    arrival_time: null,
  },
  {
    address_line_1: '15 Victoria Island Road',
    caller_first_name: 'John',
    caller_last_name: 'Doe',
    caller_phone_number: '+2348123456789',
    case_created_time: '2025-07-31T19:45:12.123Z',
    case_id: 'cas-123456789abcdef',
    case_open_time: '2025-07-31T19:45:30.456Z',
    location_latitude: 6.4281,
    location_longitude: 3.4219,
    case_status: 'in_progress',
    incident_description:
      'Patient experiencing chest pain and shortness of breath',
    provisional_diagnosis: 'Cardiac Emergency',
    severity: 'high',
    source: 'mobile-app',
    arrival_time: null,
  },
  {
    address_line_1: '45 Allen Avenue, Ikeja',
    caller_first_name: 'Mary',
    caller_last_name: 'Johnson',
    caller_phone_number: '+2347098765432',
    case_created_time: '2025-07-31T18:30:45.789Z',
    case_id: 'cas-987654321fedcba',
    case_open_time: '2025-07-31T18:31:00.012Z',
    location_latitude: 6.6018,
    location_longitude: 3.3515,
    case_status: 'open',
    incident_description:
      'Elderly patient fell and cannot get up, possible fracture',
    provisional_diagnosis: 'Trauma - Possible Fracture',
    severity: 'medium',
    source: 'phone-call',
    arrival_time: null,
  },
  {
    address_line_1: '12 Broad Street, Lagos Island',
    caller_first_name: 'Ahmed',
    caller_last_name: 'Ibrahim',
    caller_phone_number: '+2348012345678',
    case_created_time: '2025-07-31T17:15:30.456Z',
    case_id: 'cas-abcdef123456789',
    case_open_time: '2025-07-31T17:16:00.789Z',
    location_latitude: 6.4541,
    location_longitude: 3.3947,
    case_status: 'pending',
    incident_description: 'Child with high fever and difficulty breathing',
    provisional_diagnosis: 'Respiratory Distress',
    severity: 'high',
    source: 'web-app',
    arrival_time: null,
  },
  {
    address_line_1: '78 Opebi Road, Ikeja',
    caller_first_name: 'Sarah',
    caller_last_name: 'Williams',
    caller_phone_number: '+2349087654321',
    case_created_time: '2025-07-31T16:45:15.234Z',
    case_id: 'cas-fedcba987654321',
    case_open_time: '2025-07-31T16:45:45.567Z',
    location_latitude: 6.6056,
    location_longitude: 3.3569,
    case_status: 'open',
    incident_description: 'Traffic accident with multiple injuries',
    provisional_diagnosis: 'Multiple Trauma',
    severity: 'high',
    source: 'emergency-call',
    arrival_time: null,
  },
]

// Mock Responders Data
export const mockResponders: MockResponder[] = [
  {
    location_longitude: 3.321479522394929,
    profile_image_url: 'profile_image_url',
    status: 'online',
    registration_datetime: 1747293851955,
    responder_type: 'non-medic',
    email: '<EMAIL>',
    country: 'Nigeria',
    state: 'Lagos',
    last_name: 'Sauer',
    address_line_1: '7695 Spinka Rapids',
    precinct_id: 'prec-695b283cf67736ce8a2b4f7d756df048',
    first_name: 'Pat',
    location_latitude: 6.579849352635663,
    phone_number: '+2340143079618',
    responder_id: 'e7be8e9c-19dd-4a32-9ddf-30b74770bd7b',
    employee_type: 'part-time',
    responding_to: null,
  },
  {
    location_longitude: 3.3792,
    profile_image_url: 'profile_image_url',
    status: 'online',
    registration_datetime: 1647293851955,
    responder_type: 'medic',
    email: '<EMAIL>',
    country: 'Nigeria',
    state: 'Lagos',
    last_name: 'Smith',
    address_line_1: '23 Medical Center Drive',
    precinct_id: 'prec-695b283cf67736ce8a2b4f7d756df048',
    first_name: 'John',
    location_latitude: 6.4474,
    phone_number: '+2348123456789',
    responder_id: 'resp-123456789abcdef',
    employee_type: 'full-time',
    responding_to: 'cas-636f8439b47f10b201164ae51d4550c3',
  },
  {
    location_longitude: 3.3515,
    profile_image_url: 'profile_image_url',
    status: 'busy',
    registration_datetime: 1647293851955,
    responder_type: 'medic',
    email: '<EMAIL>',
    country: 'Nigeria',
    state: 'Lagos',
    last_name: 'Johnson',
    address_line_1: '45 Emergency Response Unit',
    precinct_id: 'prec-695b283cf67736ce8a2b4f7d756df048',
    first_name: 'Sarah',
    location_latitude: 6.6018,
    phone_number: '+2347098765432',
    responder_id: 'resp-987654321fedcba',
    employee_type: 'full-time',
    responding_to: 'cas-987654321fedcba',
  },
  {
    location_longitude: 3.4219,
    profile_image_url: 'profile_image_url',
    status: 'online',
    registration_datetime: 1647293851955,
    responder_type: 'non-medic',
    email: '<EMAIL>',
    country: 'Nigeria',
    state: 'Lagos',
    last_name: 'Brown',
    address_line_1: '12 Ambulance Station Road',
    precinct_id: 'prec-695b283cf67736ce8a2b4f7d756df048',
    first_name: 'Mike',
    location_latitude: 6.4281,
    phone_number: '+2348012345678',
    responder_id: 'resp-abcdef123456789',
    employee_type: 'part-time',
    responding_to: null,
  },
  {
    location_longitude: 5.5813541,
    profile_image_url: 'profile_image_url',
    status: 'offline',
    registration_datetime: 1647293851955,
    responder_type: 'medic',
    email: '<EMAIL>',
    country: 'Nigeria',
    state: 'Edo',
    last_name: 'Wilson',
    address_line_1: '89 Health Center Avenue',
    precinct_id: 'prec-695b283cf67736ce8a2b4f7d756df048',
    first_name: 'David',
    location_latitude: 6.246023999999998,
    phone_number: '+2349087654321',
    responder_id: 'resp-fedcba987654321',
    employee_type: 'volunteer',
    responding_to: null,
  },
]

// Mock Hospitals Data
export const mockHospitals: MockHospital[] = [
  {
    capacity: 50,
    hospital_id: '866d94c1-c7af-4479-9f9f-9d8faa6b362f',
    ownership_type: 'private',
    hospital_name: 'Okhuoromi Community Hospital',
    location_longitude: 5.5813541,
    registration_datetime: '2024-07-29T19:02:22.074Z',
    setup_completed: false,
    geohash_key: 'geohash_key',
    country: 'Nigeria',
    state: 'Edo',
    precinct_id: 'prec-695b283cf67736ce8a2b4f7d756df048',
    address_line_1: 'Gra/Etete, Benin City 300102, Edo, Nigeria',
    location_latitude: 6.246023999999998,
    geohash: 's15gx101',
    precinct_name: 'Edo Dispatch Center',
    registration_datetime_pk: 'registration_datetime_pk',
  },
  {
    capacity: 120,
    hospital_id: 'hosp-123456789abcdef',
    ownership_type: 'public',
    hospital_name: 'Lagos University Teaching Hospital',
    location_longitude: 3.3792,
    registration_datetime: '2024-06-15T10:30:45.123Z',
    setup_completed: true,
    geohash_key: 'geohash_key_2',
    country: 'Nigeria',
    state: 'Lagos',
    precinct_id: 'prec-695b283cf67736ce8a2b4f7d756df048',
    address_line_1: 'Idi-Araba, Surulere, Lagos',
    location_latitude: 6.4474,
    geohash: 's15gx102',
    precinct_name: 'Lagos Dispatch Center',
    registration_datetime_pk: 'registration_datetime_pk_2',
  },
  {
    capacity: 80,
    hospital_id: 'hosp-987654321fedcba',
    ownership_type: 'private',
    hospital_name: 'St. Nicholas Hospital',
    location_longitude: 3.4219,
    registration_datetime: '2024-05-20T14:15:30.456Z',
    setup_completed: true,
    geohash_key: 'geohash_key_3',
    country: 'Nigeria',
    state: 'Lagos',
    precinct_id: 'prec-695b283cf67736ce8a2b4f7d756df048',
    address_line_1: '57 Campbell Street, Lagos Island',
    location_latitude: 6.4541,
    geohash: 's15gx103',
    precinct_name: 'Lagos Dispatch Center',
    registration_datetime_pk: 'registration_datetime_pk_3',
  },
  {
    capacity: 200,
    hospital_id: 'hosp-abcdef123456789',
    ownership_type: 'public',
    hospital_name: 'National Hospital Abuja',
    location_longitude: 7.4951,
    registration_datetime: '2024-04-10T09:45:12.789Z',
    setup_completed: true,
    geohash_key: 'geohash_key_4',
    country: 'Nigeria',
    state: 'FCT',
    precinct_id: 'prec-695b283cf67736ce8a2b4f7d756df048',
    address_line_1: 'Central Business District, Abuja',
    location_latitude: 9.0579,
    geohash: 's15gx104',
    precinct_name: 'FCT Dispatch Center',
    registration_datetime_pk: 'registration_datetime_pk_4',
  },
  {
    capacity: 60,
    hospital_id: 'hosp-fedcba987654321',
    ownership_type: 'non-profit',
    hospital_name: 'Mercy Hospital',
    location_longitude: 3.3569,
    registration_datetime: '2024-03-25T16:20:45.012Z',
    setup_completed: true,
    geohash_key: 'geohash_key_5',
    country: 'Nigeria',
    state: 'Lagos',
    precinct_id: 'prec-695b283cf67736ce8a2b4f7d756df048',
    address_line_1: 'Opebi Road, Ikeja, Lagos',
    location_latitude: 6.6056,
    geohash: 's15gx105',
    precinct_name: 'Lagos Dispatch Center',
    registration_datetime_pk: 'registration_datetime_pk_5',
  },
]
