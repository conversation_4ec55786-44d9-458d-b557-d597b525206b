/**
 * Firebase Utilities
 *
 * Utilities for handling Firebase messaging and token management
 */

import { getToken, onMessage } from 'firebase/messaging'
import { addBreadcrumb, captureException } from '@/config/sentry'
import { getFirebaseApp, getFirebaseMessaging } from '@/config/firebase'
import { VAPID_KEY } from '@/config/firebase'

/**
 * Initialize Firebase app and messaging
 */
export const initializeFirebase = () => {
  try {
    if (!getFirebaseApp()) {
      // Only initialize if no app exists
      return getFirebaseApp()
    }
    // Return the existing app if already initialized
    return getFirebaseApp()
  } catch (error) {
    console.error('Failed to initialize Firebase:', error)
    throw error
  }
}

/**
 * Request notification permission from user
 */
export const requestNotificationPermission = async (): Promise<boolean> => {
  try {
    if (!('Notification' in window)) {
      console.warn('This browser does not support notifications')
      return false
    }

    const permission = await Notification.requestPermission()

    addBreadcrumb(`Notification permission: ${permission}`, 'firebase', 'info')

    return permission === 'granted'
  } catch (error) {
    console.error('Failed to request notification permission:', error)
    captureException(error as Error, {
      tags: { firebase_error: true },
      context: 'notification_permission',
    })
    return false
  }
}

/**
 * Get Firebase messaging token
 */
export const getFirebaseToken = async (): Promise<string | null> => {
  try {
    // Get Firebase app and messaging
    const app = getFirebaseApp()
    const firebaseMessaging = getFirebaseMessaging()

    if (!app || !firebaseMessaging) {
      console.warn('Firebase app or messaging not available')
      return null
    }

    // Request notification permission first
    const hasPermission = await requestNotificationPermission()
    if (!hasPermission) {
      console.warn('Notification permission not granted')
      return null
    }

    // Get the token
    const token = await getToken(firebaseMessaging, {
      vapidKey: VAPID_KEY,
    })

    if (token) {
      addBreadcrumb('Firebase token generated successfully', 'firebase', 'info')
      return token
    } else {
      console.warn('No registration token available')
      return null
    }
  } catch (error) {
    console.error('Failed to get Firebase token:', error)
    captureException(error as Error, {
      tags: { firebase_error: true },
      context: 'get_firebase_token',
    })
    return null
  }
}

/**
 * Set up foreground message listener
 */
export const setupForegroundMessageListener = (
  onMessageReceived?: (payload: any) => void
) => {
  try {
    const firebaseMessaging = getFirebaseMessaging()

    if (!firebaseMessaging) {
      console.warn('Firebase messaging not available for foreground listener')
      return
    }

    onMessage(firebaseMessaging, payload => {
      console.log('Message received in foreground:', payload)

      addBreadcrumb(
        'Firebase message received in foreground',
        'firebase',
        'info',
        {
          title: payload.notification?.title,
          body: payload.notification?.body,
        }
      )

      // Call custom handler if provided
      if (onMessageReceived) {
        onMessageReceived(payload)
      }

      // Show notification if the page is in focus
      if (document.visibilityState === 'visible' && payload.notification) {
        new Notification(payload.notification.title || 'New Message', {
          body: payload.notification.body,
          icon: payload.notification.icon || '/favicon.ico',
          badge: '/favicon.ico',
        })
      }
    })
  } catch (error) {
    console.error('Failed to setup foreground message listener:', error)
    captureException(error as Error, {
      tags: { firebase_error: true },
      context: 'foreground_message_listener',
    })
  }
}

/**
 * Handle Firebase token generation and registration
 */
export const handleGetFirebaseToken = async (): Promise<string | null> => {
  try {
    // Generate new token
    const newToken = await getFirebaseToken()
    if (newToken) {
      return newToken
    }

    return null
  } catch (error) {
    console.error('Failed to handle Firebase token:', error)
    captureException(error as Error, {
      tags: { firebase_error: true },
      context: 'handle_firebase_token',
    })
    return null
  }
}

/**
 * Check if browser supports notifications
 */
export const isNotificationSupported = (): boolean => {
  return 'Notification' in window && 'serviceWorker' in navigator
}

/**
 * Get current notification permission status
 */
export const getNotificationPermissionStatus = (): NotificationPermission => {
  if (!('Notification' in window)) {
    return 'denied'
  }
  return Notification.permission
}
