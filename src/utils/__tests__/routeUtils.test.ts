import { describe, it, expect } from 'vitest'
import { isPublicRoute, isProtectedRoute, getRedirectRoute, extractRouteParams } from '../routeUtils'
import ROUTES from '../constants/routes'

describe('routeUtils', () => {
  describe('isPublicRoute', () => {
    it('should return true for public routes', () => {
      expect(isPublicRoute(ROUTES.home)).toBe(true)
      expect(isPublicRoute(ROUTES.login)).toBe(true)
      expect(isPublicRoute(ROUTES.dispatchLogin)).toBe(true)
      expect(isPublicRoute(ROUTES.forgotPassword)).toBe(true)
    })

    it('should return false for protected routes', () => {
      expect(isPublicRoute(ROUTES.dashboard)).toBe(false)
      expect(isPublicRoute(ROUTES.incidents)).toBe(false)
      expect(isPublicRoute(ROUTES.resources)).toBe(false)
    })

    it('should return false for unknown routes', () => {
      expect(isPublicRoute('/unknown-route')).toBe(false)
    })
  })

  describe('isProtectedRoute', () => {
    it('should return true for protected routes', () => {
      expect(isProtectedRoute(ROUTES.dashboard)).toBe(true)
      expect(isProtectedRoute(ROUTES.incidents)).toBe(true)
      expect(isProtectedRoute(ROUTES.resources)).toBe(true)
      expect(isProtectedRoute(ROUTES.personnel)).toBe(true)
    })

    it('should return false for public routes', () => {
      expect(isProtectedRoute(ROUTES.home)).toBe(false)
      expect(isProtectedRoute(ROUTES.login)).toBe(false)
      expect(isProtectedRoute(ROUTES.dispatchLogin)).toBe(false)
    })

    it('should handle parameterized routes', () => {
      expect(isProtectedRoute('/incidents/123')).toBe(true)
      expect(isProtectedRoute('/resources/456')).toBe(true)
      expect(isProtectedRoute('/personnel/789')).toBe(true)
    })

    it('should return false for unknown routes', () => {
      expect(isProtectedRoute('/unknown-route')).toBe(false)
    })
  })

  describe('getRedirectRoute', () => {
    it('should redirect authenticated users away from auth pages', () => {
      expect(getRedirectRoute(true, ROUTES.login)).toBe(ROUTES.dashboard)
      expect(getRedirectRoute(true, ROUTES.dispatchLogin)).toBe(ROUTES.dashboard)
      expect(getRedirectRoute(true, ROUTES.forgotPassword)).toBe(ROUTES.dashboard)
    })

    it('should not redirect authenticated users from non-auth pages', () => {
      expect(getRedirectRoute(true, ROUTES.dashboard)).toBe(null)
      expect(getRedirectRoute(true, ROUTES.incidents)).toBe(null)
      expect(getRedirectRoute(true, ROUTES.home)).toBe(null)
    })

    it('should redirect unauthenticated users from protected routes', () => {
      expect(getRedirectRoute(false, ROUTES.dashboard)).toBe(ROUTES.login)
      expect(getRedirectRoute(false, ROUTES.incidents)).toBe(ROUTES.login)
      expect(getRedirectRoute(false, ROUTES.resources)).toBe(ROUTES.login)
    })

    it('should not redirect unauthenticated users from public routes', () => {
      expect(getRedirectRoute(false, ROUTES.home)).toBe(null)
      expect(getRedirectRoute(false, ROUTES.login)).toBe(null)
      expect(getRedirectRoute(false, ROUTES.unauthorized)).toBe(null)
    })
  })

  describe('extractRouteParams', () => {
    it('should extract parameters from routes', () => {
      const params = extractRouteParams('/incidents/:id', '/incidents/123')
      expect(params).toEqual({ id: '123' })
    })

    it('should extract multiple parameters', () => {
      const params = extractRouteParams('/users/:userId/posts/:postId', '/users/456/posts/789')
      expect(params).toEqual({ userId: '456', postId: '789' })
    })

    it('should return empty object for non-matching routes', () => {
      const params = extractRouteParams('/incidents/:id', '/resources/123')
      expect(params).toEqual({})
    })

    it('should return empty object for routes without parameters', () => {
      const params = extractRouteParams('/dashboard', '/dashboard')
      expect(params).toEqual({})
    })
  })
})
