/**
 * API Error Handling Utilities
 *
 * Utilities for handling API errors with user-friendly messages
 */

import { ApiError } from '@/types/api/common'
import { captureException, addBreadcrumb } from '@/config/sentry'

// User-friendly error messages mapping
const ERROR_MESSAGES: Record<string, string> = {
  // Network errors
  'Network Error':
    'Unable to connect to the server. Please check your internet connection.',
  timeout: 'Request timed out. Please try again.',
  ECONNABORTED: 'Request timed out. Please try again.',

  // Authentication errors
  Unauthorized: 'Your session has expired. Please log in again.',
  Forbidden: 'You do not have permission to perform this action.',
  'Invalid token': 'Your session has expired. Please log in again.',
  'Token expired': 'Your session has expired. Please log in again.',

  // Validation errors
  'Validation failed': 'Please check your input and try again.',
  'Invalid input': 'Please check your input and try again.',
  'Missing required field': 'Please fill in all required fields.',

  // Resource errors
  'Not found': 'The requested resource was not found.',
  'Resource not found': 'The requested resource was not found.',
  'Already exists': 'This resource already exists.',
  'Duplicate entry': 'This entry already exists.',

  // Server errors
  'Internal server error': 'A server error occurred. Please try again later.',
  'Service unavailable':
    'The service is temporarily unavailable. Please try again later.',
  'Bad gateway': 'Server communication error. Please try again later.',

  // Business logic errors
  'Case already approved': 'This case has already been approved.',
  'Case already closed': 'This case has already been closed.',
  'Insufficient permissions':
    'You do not have permission to perform this action.',
  'Invalid case status':
    'Cannot perform this action on a case with this status.',

  // Default fallbacks
  default_400: 'Invalid request. Please check your input.',
  default_401: 'Authentication required. Please log in.',
  default_403: 'Access denied. You do not have permission.',
  default_404: 'Resource not found.',
  default_409: 'Conflict occurred. The resource may already exist.',
  default_422: 'Invalid data provided. Please check your input.',
  default_429: 'Too many requests. Please wait a moment and try again.',
  default_500: 'Server error occurred. Please try again later.',
  default_502: 'Server communication error. Please try again later.',
  default_503: 'Service temporarily unavailable. Please try again later.',
  default_504: 'Request timed out. Please try again.',
}

/**
 * Get user-friendly error message from API error
 */
export const getUserFriendlyErrorMessage = (error: ApiError | any): string => {
  try {
    // Handle ApiError type
    if (error && typeof error === 'object' && 'message' in error) {
      const message = error.message?.toLowerCase() || ''

      // Check for exact message matches
      for (const [key, friendlyMessage] of Object.entries(ERROR_MESSAGES)) {
        if (message.includes(key.toLowerCase())) {
          return friendlyMessage
        }
      }

      // Check for status code defaults
      if (error.code) {
        const statusDefault = ERROR_MESSAGES[`default_${error.code}`]
        if (statusDefault) {
          return statusDefault
        }
      }

      // Return original message if it's user-friendly
      if (
        error.message &&
        error.message.length < 100 &&
        !error.message.includes('Error:')
      ) {
        return error.message
      }
    }

    // Handle axios errors
    if (error?.response?.data?.message) {
      return getUserFriendlyErrorMessage({
        message: error.response.data.message,
        code: error.response.status,
      })
    }

    // Handle network errors
    if (error?.code === 'ECONNABORTED' || error?.message?.includes('timeout')) {
      return ERROR_MESSAGES.timeout
    }

    if (error?.message === 'Network Error') {
      return ERROR_MESSAGES['Network Error']
    }

    // Default fallback
    return 'An unexpected error occurred. Please try again.'
  } catch (processingError) {
    console.error('Error processing error message:', processingError)
    return 'An unexpected error occurred. Please try again.'
  }
}

/**
 * Handle 401 Unauthorized errors with automatic session cleanup and redirect
 */
const handle401Error = async (): Promise<void> => {
  try {
    // Clear authentication storage
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth-token')
      localStorage.removeItem('refresh-token')
      localStorage.removeItem('user-data')
      sessionStorage.clear()
    }

    // Clear Sentry user context
    try {
      const { clearSentryUser } = await import('@/config/sentry')
      clearSentryUser()
    } catch (error) {
      console.warn('Failed to clear Sentry user context:', error)
    }

    // Add breadcrumb for session expiry
    addBreadcrumb('Session expired - redirecting to login', 'auth', 'warning')

    // Redirect to login page
    if (typeof window !== 'undefined') {
      // Use setTimeout to ensure the error message is shown first
      setTimeout(() => {
        window.location.href = '/auth/login'
      }, 1500)
    }
  } catch (error) {
    console.error('Error handling 401 unauthorized:', error)
  }
}

/**
 * Handle API error with logging and user notification
 */
export const handleApiError = (
  error: any,
  context: string,
  showToast: boolean = true
): ApiError => {
  const friendlyMessage = getUserFriendlyErrorMessage(error)

  const apiError: ApiError = {
    status: 'error',
    message: friendlyMessage,
    code: error?.response?.status || error?.code || 500,
    details: error?.response?.data?.details,
    timestamp: new Date().toISOString(),
  }

  // Handle 401 Unauthorized errors with automatic session cleanup and redirect
  if (apiError.code === 401 || isAuthError(error)) {
    // Ensure we show the correct 401 message
    apiError.message =
      ERROR_MESSAGES.Unauthorized ||
      'Your session has expired. Please log in again.'

    // Handle session cleanup and redirect
    handle401Error().catch(console.error)
  }

  // Add breadcrumb for error context
  addBreadcrumb(`API Error in ${context}`, 'error', 'error', {
    originalMessage: error?.message,
    friendlyMessage,
    statusCode: apiError.code,
  })

  // Report to Sentry for unexpected errors
  if (!isExpectedError(apiError.code)) {
    captureException(error, {
      tags: {
        api_error: true,
        context,
        status_code: apiError.code,
      },
      extra: {
        friendlyMessage,
        originalError: error?.message,
        response: error?.response?.data,
      },
    })
  }

  // Show toast notification if requested
  if (showToast && typeof window !== 'undefined') {
    // Import toast dynamically to avoid SSR issues
    import('react-hot-toast')
      .then(({ default: toast }) => {
        toast.error(friendlyMessage)
      })
      .catch(console.error)
  }

  return apiError
}

/**
 * Check if error is expected and shouldn't be reported to Sentry
 */
export const isExpectedError = (statusCode: number): boolean => {
  // Don't report client errors (4xx) except 401 and 403
  return (
    statusCode >= 400 &&
    statusCode < 500 &&
    statusCode !== 401 &&
    statusCode !== 403
  )
}

/**
 * Retry condition for API requests
 */
export const shouldRetryRequest = (error: any): boolean => {
  // Don't retry on client errors (4xx)
  if (error?.response?.status >= 400 && error?.response?.status < 500) {
    return false
  }

  // Retry on network errors
  if (!error?.response) {
    return true
  }

  // Retry on server errors (5xx)
  if (error?.response?.status >= 500) {
    return true
  }

  // Retry on timeout
  if (error?.code === 'ECONNABORTED') {
    return true
  }

  return false
}

/**
 * Get retry delay with exponential backoff
 */
export const getRetryDelay = (
  attemptIndex: number,
  baseDelay: number = 1000
): number => {
  return Math.min(baseDelay * Math.pow(2, attemptIndex), 10000) // Max 10 seconds
}

/**
 * Format validation errors for display
 */
export const formatValidationErrors = (
  errors: Record<string, string[]>
): string => {
  const errorMessages = Object.entries(errors)
    .map(([field, messages]) => `${field}: ${messages.join(', ')}`)
    .join('; ')

  return errorMessages || 'Validation failed'
}

/**
 * Check if error indicates authentication failure
 */
export const isAuthError = (error: any): boolean => {
  const status = error?.response?.status || error?.code
  const message = error?.message?.toLowerCase() || ''

  return (
    status === 401 ||
    message.includes('unauthorized') ||
    message.includes('token expired') ||
    message.includes('invalid token') ||
    message.includes('authentication')
  )
}

/**
 * Check if error indicates network connectivity issues
 */
export const isNetworkError = (error: any): boolean => {
  return (
    !error?.response ||
    error?.message === 'Network Error' ||
    error?.code === 'ECONNABORTED' ||
    error?.code === 'ENOTFOUND' ||
    error?.code === 'ECONNREFUSED'
  )
}

/**
 * Extract error details for debugging
 */
export const extractErrorDetails = (error: any): Record<string, any> => {
  return {
    message: error?.message,
    status: error?.response?.status,
    statusText: error?.response?.statusText,
    data: error?.response?.data,
    code: error?.code,
    config: {
      url: error?.config?.url,
      method: error?.config?.method,
      headers: error?.config?.headers,
    },
    timestamp: new Date().toISOString(),
  }
}
