/**
 * Authentication Security Utilities
 *
 * Enhanced security utilities for authentication state management
 * with improved error handling and data validation.
 */

import { captureException, addBreadcrumb } from '@/config/sentry'

export interface SecureAuthData {
  isAuthenticated: boolean
  user: {
    sub: string
    email: string
    attributes: Record<string, any>
    challengeName?: string
  } | null
  lastValidated: number
  sessionExpiry?: number
}

export interface AuthValidationResult {
  isValid: boolean
  shouldRefresh: boolean
  error?: string
}

/**
 * Validates stored authentication data for integrity and freshness
 */
export const validateStoredAuthData = (data: any): AuthValidationResult => {
  try {
    // Check if data exists and has required structure
    if (!data || typeof data !== 'object') {
      return {
        isValid: false,
        shouldRefresh: false,
        error: 'Invalid data structure',
      }
    }

    // Validate required fields
    const requiredFields = ['isAuthenticated', 'lastValidated']
    const missingFields = requiredFields.filter(field => !(field in data))

    if (missingFields.length > 0) {
      return {
        isValid: false,
        shouldRefresh: false,
        error: `Missing required fields: ${missingFields.join(', ')}`,
      }
    }

    // Check data freshness (24 hours)
    const maxAge = 24 * 60 * 60 * 1000 // 24 hours in milliseconds
    const age = Date.now() - (data.lastValidated || 0)

    if (age > maxAge) {
      return {
        isValid: false,
        shouldRefresh: true,
        error: 'Stored auth data is stale',
      }
    }

    // Validate user data structure if authenticated
    if (data.isAuthenticated && data.user) {
      const userRequiredFields = ['sub', 'email']
      const missingUserFields = userRequiredFields.filter(
        field => !data.user[field]
      )

      if (missingUserFields.length > 0) {
        return {
          isValid: false,
          shouldRefresh: true,
          error: `Invalid user data: missing ${missingUserFields.join(', ')}`,
        }
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(data.user.email)) {
        return {
          isValid: false,
          shouldRefresh: true,
          error: 'Invalid email format in user data',
        }
      }
    }

    return { isValid: true, shouldRefresh: false }
  } catch (error) {
    captureException(error as Error, {
      context: 'auth_data_validation',
      data: data ? 'present' : 'missing',
    })

    return {
      isValid: false,
      shouldRefresh: false,
      error: 'Validation error occurred',
    }
  }
}

/**
 * Sanitizes user data before storage to remove sensitive information
 */
export const sanitizeUserData = (user: any): SecureAuthData['user'] => {
  if (!user) return null

  try {
    // Define the type for allowed attributes
    type AllowedAttributes = {
      name?: string
      given_name?: string
      family_name?: string
      preferred_username?: string
      email_verified?: boolean | string
      phone_number_verified?: boolean | string
      [key: string]: any // This allows other string keys if needed
    }

    // Only keep safe, necessary user data
    const sanitized = {
      sub: user.sub || '',
      email: user.email || '',
      attributes: {} as AllowedAttributes,
      challengeName: user.challengeName,
    }

    // Sanitize attributes - only keep non-sensitive data
    if (user.attributes && typeof user.attributes === 'object') {
      const allowedAttributes = [
        'name',
        'given_name',
        'family_name',
        'preferred_username',
        'email_verified',
        'phone_number_verified',
      ]

      allowedAttributes.forEach(attr => {
        if (user.attributes[attr] !== undefined) {
          sanitized.attributes[attr] = user.attributes[attr]
        }
      })
    }

    return sanitized
  } catch (error) {
    captureException(error as Error, {
      context: 'user_data_sanitization',
    })
    return null
  }
}

/**
 * Creates secure auth data object with validation timestamp
 */
export const createSecureAuthData = (
  isAuthenticated: boolean,
  user: any,
  sessionExpiry?: number
): SecureAuthData => {
  return {
    isAuthenticated,
    user: sanitizeUserData(user),
    lastValidated: Date.now(),
    sessionExpiry,
  }
}

/**
 * Securely clears authentication data from storage
 */
export const clearAuthStorage = (): void => {
  try {
    // Clear auth-related localStorage items and FCM token
    const authKeys = ['auth-storage', 'fcm_token']

    authKeys.forEach(key => {
      localStorage.removeItem(key)
    })

    // Clear any session storage items
    sessionStorage.clear()

    addBreadcrumb('Auth storage cleared', 'auth', 'info')
  } catch (error) {
    captureException(error as Error, {
      context: 'auth_storage_cleanup',
    })
  }
}

/**
 * Checks if the current environment is secure for storing auth data
 */
export const isSecureEnvironment = (): boolean => {
  try {
    // Check if we're in HTTPS or localhost
    const isHttps = window.location.protocol === 'https:'
    const isLocalhost =
      window.location.hostname === 'localhost' ||
      window.location.hostname === '127.0.0.1'

    // Check if localStorage is available
    const hasLocalStorage = typeof Storage !== 'undefined'

    return (isHttps || isLocalhost) && hasLocalStorage
  } catch (error) {
    return false
  }
}

/**
 * Enhanced error handling for authentication operations
 */
export const handleAuthError = (
  error: any,
  operation: string,
  context?: Record<string, any>
): string => {
  let userFriendlyMessage = 'An authentication error occurred'

  try {
    // Map common AWS Cognito errors to user-friendly messages
    const errorMappings: Record<string, string> = {
      UserNotConfirmedException:
        'Please verify your email address before signing in',
      NotAuthorizedException: 'Invalid email or password',
      UserNotFoundException: 'No account found with this email address',
      InvalidPasswordException: 'Password does not meet requirements',
      TooManyRequestsException: 'Too many attempts. Please try again later',
      LimitExceededException: 'Too many attempts. Please try again later',
      CodeMismatchException: 'Invalid verification code',
      ExpiredCodeException: 'Verification code has expired',
      InvalidParameterException: 'Invalid request parameters',
      NetworkError:
        'Network connection error. Please check your internet connection',
    }

    // Extract error message
    const errorMessage = error?.message || error?.toString() || 'Unknown error'
    const errorCode = error?.name || error?.code

    // Find user-friendly message
    userFriendlyMessage =
      errorMappings[errorCode] ||
      errorMappings[errorMessage] ||
      userFriendlyMessage

    // Report to Sentry with context
    captureException(error, {
      operation,
      errorCode,
      errorMessage,
      ...context,
    })

    // Add breadcrumb for debugging
    addBreadcrumb(`Auth error in ${operation}`, 'auth', 'error', {
      errorCode,
      errorMessage: errorMessage.substring(0, 100), // Limit message length
    })
  } catch (reportingError) {
    console.error('Error in auth error handling:', reportingError)
  }

  return userFriendlyMessage
}
