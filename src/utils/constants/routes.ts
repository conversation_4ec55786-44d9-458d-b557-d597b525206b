const ROUTES = {
  // Public routes
  home: '/',
  login: '/auth/login',
  dispatchLogin: '/auth/dispatch-login',
  adminLogin: '/auth/admin-login',
  setPassword: '/auth/set-password',
  phoneVerification: '/auth/verification',
  residencyInfo: '/auth/residency-info',
  forgotPassword: '/auth/forgot-password',
  resetPassword: '/auth/reset-password',

  // Protected routes
  dashboard: '/dashboard',
  cases: '/cases',
  caseDetails: '/cases/:id',
  map: '/map',
  inquiries: '/inquiries',
  inquiryDetails: '/inquiries/:id',
  responders: '/responders',
  dispatchers: '/dispatchers',
  ambulanceProviders: '/ambulance-providers',
  hospitals: '/hospitals',
  registeredUsers: '/registered-users',
  leaderboard: '/leaderboard',
  profile: '/profile',

  // Auth callback routes
  authCallback: '/auth/callback',
  authSignout: '/auth/signout',

  // Error routes
  unauthorized: '/unauthorized',
  notFound: '/404',
} as const

// Helper function to get route with parameters
export const getRouteWithParams = (
  route: string,
  params: Record<string, string | number>
) => {
  let result = route
  Object.entries(params).forEach(([key, value]) => {
    result = result.replace(`:${key}`, String(value))
  })
  return result
}

// Route categories for easier management
export const PUBLIC_ROUTES = [
  ROUTES.home,
  ROUTES.login,
  ROUTES.dispatchLogin,
  ROUTES.adminLogin,
  ROUTES.setPassword,
  ROUTES.phoneVerification,
  ROUTES.residencyInfo,
  ROUTES.forgotPassword,
  ROUTES.resetPassword,
  ROUTES.authCallback,
  ROUTES.authSignout,
  ROUTES.unauthorized,
  ROUTES.notFound,
] as const

export const PROTECTED_ROUTES = [
  ROUTES.dashboard,
  ROUTES.cases,
  ROUTES.caseDetails,
  ROUTES.map,
  ROUTES.inquiries,
  ROUTES.inquiryDetails,
  ROUTES.responders,
  ROUTES.dispatchers,
  ROUTES.ambulanceProviders,
  ROUTES.hospitals,
  ROUTES.registeredUsers,
  ROUTES.leaderboard,
  ROUTES.profile,
] as const

export const PAGE_TITLES: Record<string, string> = {
  [ROUTES.dashboard]: 'Dashboard',
  [ROUTES.cases]: 'Case Overview',
  [ROUTES.inquiries]: 'Inquiries',
  [ROUTES.map]: 'Map',
  [ROUTES.responders]: 'Responders',
  [ROUTES.dispatchers]: 'Dispatchers',
  [ROUTES.ambulanceProviders]: 'Ambulance Providers',
  [ROUTES.hospitals]: 'Hospitals',
  [ROUTES.registeredUsers]: 'Registered Users',
  [ROUTES.profile]: 'Profile',
}

export default ROUTES
