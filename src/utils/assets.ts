import React from 'react'

/**
 * Centralized asset management
 * This file provides a single source of truth for all application assets
 * and includes optimization utilities for better performance
 */

// Import all assets with explicit URLs for better Vite optimization
import eraLogo from '@/assets/era-logo-no-text.png?url'
import eraLogoFull from '@/assets/era-logo.png?url'
import hospitalBg from '@/assets/hospital.jpg?url'
import quoteIcon from '@/assets/quote.svg?url'
import dotBoxIcon from '@/assets/dot-box.svg?url'
import lShapeIcon from '@/assets/l-shape.svg?url'
import donutIcon from '@/assets/donut.svg?url'

/**
 * Asset registry with metadata for optimization
 */
export const assets = {
  logos: {
    era: {
      url: eraLogo,
      alt: 'ERA Logo',
      width: 35,
      height: 40.83,
      format: 'png',
    },
    eraFull: {
      url: eraLogoFull,
      alt: 'ERA Full Logo',
      format: 'png',
    },
  },

  backgrounds: {
    hospital: {
      url: hospitalBg,
      alt: 'Hospital Background',
      format: 'jpg',
      loading: 'lazy' as const,
      // Provide multiple sizes for responsive images
      sizes: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
    },
  },

  icons: {
    quote: {
      url: quoteIcon,
      alt: 'Quote Icon',
      width: 20,
      height: 20,
      format: 'svg',
    },
    dotBox: {
      url: dotBoxIcon,
      alt: 'Dotted Box Decoration',
      width: 60,
      height: 60,
      format: 'svg',
    },
    lShape: {
      url: lShapeIcon,
      alt: 'L-Shape Decoration',
      width: 20,
      height: 20,
      format: 'svg',
    },
    donut: {
      url: donutIcon,
      alt: 'Donut Decoration',
      format: 'svg',
    },
  },
} as const

/**
 * Asset utility functions
 */
export const assetUtils = {
  /**
   * Preload critical assets for better performance
   */
  preloadCriticalAssets: () => {
    const criticalAssets = [
      assets.logos.era.url,
      assets.backgrounds.hospital.url,
    ]

    criticalAssets.forEach(url => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.href = url
      link.as = url.endsWith('.jpg') || url.endsWith('.png') ? 'image' : 'fetch'
      document.head.appendChild(link)
    })
  },

  /**
   * Create optimized image props for better performance
   */
  getImageProps: (asset: {
    url: string
    alt: string
    width?: number
    height?: number
  }) => ({
    src: asset.url,
    alt: asset.alt,
    ...(asset.width && { width: asset.width }),
    ...(asset.height && { height: asset.height }),
    loading: 'lazy' as const,
    decoding: 'async' as const,
  }),

  /**
   * Create optimized background image CSS
   */
  getBackgroundImageCSS: (asset: typeof assets.backgrounds.hospital) => ({
    backgroundImage: `url(${asset.url})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat',
  }),

  /**
   * Get asset URL by path (for dynamic imports)
   */
  getAssetUrl: (path: string): string => {
    // This would be used for dynamic asset loading
    return new URL(path, import.meta.url).href
  },
}

/**
 * Asset preloading hook for React components
 */
export const useAssetPreload = (assetUrls: string[]) => {
  React.useEffect(() => {
    assetUrls.forEach(url => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.href = url
      link.as = 'image'
      document.head.appendChild(link)
    })

    // Cleanup function to remove preload links
    return () => {
      assetUrls.forEach(url => {
        const link = document.querySelector(`link[href="${url}"]`)
        if (link) {
          document.head.removeChild(link)
        }
      })
    }
  }, [assetUrls])
}

// Export individual assets for direct use
export {
  eraLogo,
  eraLogoFull,
  hospitalBg,
  quoteIcon,
  dotBoxIcon,
  lShapeIcon,
  donutIcon,
}
