/**
 * Global Error Handling Utilities
 *
 * This module provides comprehensive error handling for the application,
 * including unhandled promise rejections, global errors, and development
 * error reporting. Integrates with Sentry for production error tracking.
 */

/* eslint-disable @typescript-eslint/no-explicit-any */

import { captureException, addBreadcrumb } from '@/config/sentry'

interface ErrorReport {
  message: string
  stack?: string
  url?: string
  line?: number
  column?: number
  timestamp: number
  userAgent: string
  isDevelopment: boolean
  errorType: 'javascript' | 'promise' | 'module' | 'network' | 'unknown'
}

class GlobalErrorHandler {
  private errorQueue: ErrorReport[] = []
  private maxErrors = 50
  private isDevelopment = import.meta.env.DEV

  constructor() {
    this.setupGlobalErrorHandlers()
  }

  private setupGlobalErrorHandlers() {
    // Handle unhandled JavaScript errors
    window.addEventListener('error', event => {
      this.handleError({
        message: event.message,
        stack: event.error?.stack,
        url: event.filename,
        line: event.lineno,
        column: event.colno,
        errorType: this.detectErrorType(event.message, event.error),
      })
    })

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', event => {
      this.handleError({
        message: event.reason?.message || String(event.reason),
        stack: event.reason?.stack,
        errorType: 'promise',
      })
    })

    // Handle module loading errors (for dynamic imports)
    // Note: This is a Vite-specific optimization that may not always be available
    try {
      const viteWindow = window as any
      const originalImport = viteWindow.__vitePreload
      if (typeof originalImport === 'function') {
        viteWindow.__vitePreload = async (...args: any[]) => {
          try {
            return await originalImport(...args)
          } catch (error) {
            this.handleError({
              message: `Module loading failed: ${error}`,
              stack: error instanceof Error ? error.stack : undefined,
              errorType: 'module',
            })
            throw error
          }
        }
      }
    } catch (error) {
      // Ignore if __vitePreload is not available
      console.debug('Vite preload hook not available:', error)
    }
  }

  private detectErrorType(
    message: string,
    error?: Error
  ): ErrorReport['errorType'] {
    const msg = message.toLowerCase()

    if (
      msg.includes('import') ||
      msg.includes('module') ||
      msg.includes('loading')
    ) {
      return 'module'
    }
    if (
      msg.includes('network') ||
      msg.includes('fetch') ||
      msg.includes('xhr')
    ) {
      return 'network'
    }
    if (error?.name === 'ChunkLoadError' || msg.includes('chunk')) {
      return 'module'
    }

    return 'javascript'
  }

  private handleError(errorData: Partial<ErrorReport>) {
    const report: ErrorReport = {
      message: errorData.message || 'Unknown error',
      stack: errorData.stack,
      url: errorData.url || window.location.href,
      line: errorData.line,
      column: errorData.column,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      isDevelopment: this.isDevelopment,
      errorType: errorData.errorType || 'unknown',
    }

    // Add to error queue
    this.errorQueue.push(report)
    if (this.errorQueue.length > this.maxErrors) {
      this.errorQueue.shift()
    }

    // Log to console in development
    if (this.isDevelopment) {
      console.group(
        `🚨 Global Error Handler - ${report.errorType.toUpperCase()}`
      )
      console.error('Message:', report.message)
      console.error('Stack:', report.stack)
      console.error(
        'Location:',
        `${report.url}:${report.line}:${report.column}`
      )
      console.error('Full Report:', report)
      console.groupEnd()
    }

    // Send to error tracking service in production
    if (!this.isDevelopment) {
      this.reportToService(report)
    }

    // Show user-friendly notification for critical errors
    if (this.isCriticalError(report)) {
      this.showErrorNotification(report)
    }
  }

  private isCriticalError(report: ErrorReport): boolean {
    const criticalPatterns = [
      'chunk load error',
      'loading css chunk',
      'loading chunk',
      'module not found',
      'cannot resolve module',
    ]

    return criticalPatterns.some(pattern =>
      report.message.toLowerCase().includes(pattern)
    )
  }

  private showErrorNotification(report: ErrorReport) {
    // Create a simple toast notification for critical errors
    const notification = document.createElement('div')
    notification.className =
      'fixed top-4 right-4 bg-red-500 text-white p-4 rounded-lg shadow-lg z-50 max-w-sm'
    notification.innerHTML = `
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-300" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium">Application Error</h3>
          <p class="mt-1 text-sm text-red-200">
            ${report.errorType === 'module' ? 'Module loading failed' : 'An error occurred'}. 
            Please refresh the page.
          </p>
          <button onclick="window.location.reload()" class="mt-2 text-xs bg-red-600 hover:bg-red-700 px-2 py-1 rounded">
            Refresh Page
          </button>
        </div>
      </div>
    `

    document.body.appendChild(notification)

    // Auto-remove after 10 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification)
      }
    }, 10000)
  }

  private async reportToService(report: ErrorReport) {
    try {
      // Add breadcrumb for global error context
      addBreadcrumb('Global error handler triggered', 'error', 'error', {
        errorType: report.errorType,
        url: report.url,
        timestamp: report.timestamp,
      })

      // Create error object for Sentry
      const error = new Error(report.message)
      if (report.stack) {
        error.stack = report.stack
      }

      // Report to Sentry with context
      captureException(error, {
        errorType: report.errorType,
        url: report.url,
        line: report.line,
        column: report.column,
        userAgent: report.userAgent,
        isDevelopment: report.isDevelopment,
        timestamp: report.timestamp,
        isGlobalError: true,
      })
    } catch (error) {
      console.error('Failed to report error to Sentry:', error)
    }
  }

  // Public methods for manual error reporting
  public reportError(error: Error, context?: string) {
    // Add breadcrumb for manual error reporting
    addBreadcrumb('Manual error reported', 'error', 'error', {
      context,
      errorMessage: error.message,
    })

    // Report directly to Sentry for manual errors
    captureException(error, {
      context,
      isManualReport: true,
    })

    // Also handle through the normal error handling flow
    this.handleError({
      message: `${context ? `${context}: ` : ''}${error.message}`,
      stack: error.stack,
      errorType: 'javascript',
    })
  }

  public getErrorHistory(): ErrorReport[] {
    return [...this.errorQueue]
  }

  public clearErrorHistory() {
    this.errorQueue = []
  }
}

// Create global instance
export const globalErrorHandler = new GlobalErrorHandler()

// Export utility functions
export const reportError = (error: Error, context?: string) => {
  globalErrorHandler.reportError(error, context)
}

export const getErrorHistory = () => {
  return globalErrorHandler.getErrorHistory()
}

export const clearErrorHistory = () => {
  globalErrorHandler.clearErrorHistory()
}

// Development helper to test error handling
if (import.meta.env.DEV) {
  // @ts-expect-error - We're deliberately adding to window for debugging
  window.__errorHandler = {
    handler: globalErrorHandler,
    testError: () => {
      throw new Error('Test error from error handler')
    },
    testPromiseRejection: () => {
      Promise.reject(new Error('Test promise rejection'))
    },
    getHistory: getErrorHistory,
    clearHistory: clearErrorHistory,
  }
}
