export const CalendarIcon = () => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_35150_77665)">
        <path
          d="M12 3.33398H4.00001C3.26363 3.33398 2.66667 3.93094 2.66667 4.66732V12.6673C2.66667 13.4037 3.26363 14.0007 4.00001 14.0007H12C12.7364 14.0007 13.3333 13.4037 13.3333 12.6673V4.66732C13.3333 3.93094 12.7364 3.33398 12 3.33398Z"
          stroke="#545252"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M10.6667 2V4.66667"
          stroke="#545252"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M5.33333 2V4.66667"
          stroke="#545252"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M2.66667 7.33398H13.3333"
          stroke="#545252"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M7.33333 10H7.99999"
          stroke="#545252"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M8 10V12"
          stroke="#545252"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_35150_77665">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}

export const DownloadIcon = () => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_35150_54174)">
        <path
          d="M2.66667 11.334V12.6673C2.66667 13.0209 2.80714 13.3601 3.05719 13.6101C3.30724 13.8602 3.64638 14.0007 4 14.0007H12C12.3536 14.0007 12.6928 13.8602 12.9428 13.6101C13.1929 13.3601 13.3333 13.0209 13.3333 12.6673V11.334"
          stroke="#545252"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M4.66667 7.33398L8 10.6673L11.3333 7.33398"
          stroke="#545252"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M8 2.66602V10.666"
          stroke="#545252"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_35150_54174">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}

export const FilterIcon = () => {
  return (
    <svg
      width="16"
      height="17"
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_35150_21560)">
        <path
          d="M3.66674 4.13477H12.3334C12.4294 4.16844 12.5164 4.22364 12.5878 4.29614C12.6592 4.36864 12.7131 4.45652 12.7452 4.55305C12.7774 4.64957 12.7871 4.75218 12.7735 4.85301C12.7598 4.95384 12.7233 5.05022 12.6667 5.13477L9.3334 8.80143V13.4681L6.66674 11.4681V8.80143L3.3334 5.13477C3.2768 5.05022 3.2403 4.95384 3.22669 4.85301C3.21309 4.75218 3.22274 4.64957 3.25491 4.55305C3.28709 4.45652 3.34093 4.36864 3.41231 4.29614C3.4837 4.22364 3.57073 4.16844 3.66674 4.13477Z"
          stroke="#545252"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_35150_21560">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(0 0.800781)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

export const AdjustmentIcon = () => {
  return (
    <svg
      width="16"
      height="17"
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_35150_21622)">
        <path
          d="M3.99999 8.80143C4.73637 8.80143 5.33332 8.20448 5.33332 7.4681C5.33332 6.73172 4.73637 6.13477 3.99999 6.13477C3.26361 6.13477 2.66666 6.73172 2.66666 7.4681C2.66666 8.20448 3.26361 8.80143 3.99999 8.80143Z"
          stroke="#545252"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M4 3.4668V6.13346"
          stroke="#545252"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M4 8.80078V14.1341"
          stroke="#545252"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M7.99999 12.8014C8.73637 12.8014 9.33332 12.2045 9.33332 11.4681C9.33332 10.7317 8.73637 10.1348 7.99999 10.1348C7.26361 10.1348 6.66666 10.7317 6.66666 11.4681C6.66666 12.2045 7.26361 12.8014 7.99999 12.8014Z"
          stroke="#545252"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M8 3.4668V10.1335"
          stroke="#545252"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M8 12.8008V14.1341"
          stroke="#667085"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M12 6.80143C12.7364 6.80143 13.3333 6.20448 13.3333 5.4681C13.3333 4.73172 12.7364 4.13477 12 4.13477C11.2636 4.13477 10.6667 4.73172 10.6667 5.4681C10.6667 6.20448 11.2636 6.80143 12 6.80143Z"
          stroke="#667085"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M12 3.4668V4.13346"
          stroke="#667085"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M12 6.80078V14.1341"
          stroke="#667085"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_35150_21622">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(0 0.800781)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

export const SearchIcon = () => {
  return (
    <svg
      width="16"
      height="17"
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.66671 15.3014C3.90004 15.3014 0.833374 12.2348 0.833374 8.4681C0.833374 4.70143 3.90004 1.63477 7.66671 1.63477C11.4334 1.63477 14.5 4.70143 14.5 8.4681C14.5 12.2348 11.4334 15.3014 7.66671 15.3014ZM7.66671 2.63477C4.44671 2.63477 1.83337 5.25477 1.83337 8.4681C1.83337 11.6814 4.44671 14.3014 7.66671 14.3014C10.8867 14.3014 13.5 11.6814 13.5 8.4681C13.5 5.25477 10.8867 2.63477 7.66671 2.63477Z"
        fill="#777F8C"
      />
      <path
        d="M14.6666 15.9684C14.54 15.9684 14.4133 15.9217 14.3133 15.8217L12.98 14.4884C12.7866 14.2951 12.7866 13.9751 12.98 13.7817C13.1733 13.5884 13.4933 13.5884 13.6866 13.7817L15.02 15.1151C15.2133 15.3084 15.2133 15.6284 15.02 15.8217C14.92 15.9217 14.7933 15.9684 14.6666 15.9684Z"
        fill="#777F8C"
      />
    </svg>
  )
}

export const ChevronDown = () => {
  return (
    <svg
      width="17"
      height="17"
      viewBox="0 0 17 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_35150_19519)">
        <path
          d="M4.60001 6.20117L8.60001 10.2012L12.6 6.20117"
          stroke="#545252"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_35150_19519">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(0.600006 0.201172)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}
