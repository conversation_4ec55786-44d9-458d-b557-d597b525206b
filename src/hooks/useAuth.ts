/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useState } from 'react'
import { useAuthStore } from '@/store/authStore'
import { fetchAuthSession, getCurrentUser } from 'aws-amplify/auth'
import { setSentryUser, clearSentryUser, addBreadcrumb } from '@/config/sentry'
import { handleAuthError } from '@/utils/authSecurity'

export interface AuthUser {
  sub: string
  email: string
  attributes: Record<string, any>
  challengeName?: string
}

export interface UseAuthReturn {
  // State
  isAuthenticated: boolean
  isLoading: boolean
  user: AuthUser | null
  error: string | null
  token: string | null

  // Actions
  signIn: (email: string, password: string) => Promise<void>
  signOut: () => Promise<void>
  setNewPassword: (password: string, userInfo?: any) => Promise<void>
  confirmMFA: (code: string) => Promise<void>
  resetPassword: (email: string) => Promise<void>
  confirmResetPassword: (
    email: string,
    code: string,
    newPassword: string
  ) => Promise<void>
  clearError: () => void
  setLoading: (loading: boolean) => void

  // Utility methods
  checkAuthStatus: () => Promise<boolean>
  refreshAuth: () => Promise<void>
}

/**
 * Custom hook that provides authentication state and methods
 * Integrates with the Zustand auth store and AWS Amplify
 */
export const useAuth = (): UseAuthReturn => {
  const authStore = useAuthStore()
  const [isInitialized, setIsInitialized] = useState(false)

  // Initialize auth state on mount
  useEffect(() => {
    const initializeAuth = async () => {
      if (isInitialized) return

      try {
        authStore.setLoading(true)

        const isAuthenticated = await checkAuthStatus()

        if (isAuthenticated) {
          await refreshAuth()
        }
      } catch (error) {
        const errorMessage = handleAuthError(error, 'auth_initialization')
        console.error('Auth initialization error:', errorMessage)
        authStore.clearError()
      } finally {
        authStore.setLoading(false)
        setIsInitialized(true)
      }
    }

    initializeAuth()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isInitialized])

  /**
   * Check if user is currently authenticated
   */
  const checkAuthStatus = async (): Promise<boolean> => {
    try {
      const session = await fetchAuthSession()
      return !!(session.tokens?.accessToken && session.tokens.idToken)
    } catch (error) {
      console.error('Auth status check failed:', error)
      return false
    }
  }

  /**
   * Refresh authentication state from AWS Amplify
   */
  const refreshAuth = async (): Promise<void> => {
    try {
      const [session, currentUser] = await Promise.all([
        fetchAuthSession({ forceRefresh: true }),
        getCurrentUser(),
      ])

      if (session.tokens?.idToken && currentUser) {
        const token = session.tokens.idToken.toString()

        const userData = {
          sub: currentUser.userId,
          email: currentUser.username,
          attributes: {},
        }

        // Update the store with fresh auth data
        useAuthStore.setState({
          isAuthenticated: true,
          user: userData,
          token,
          error: null,
        })

        // Set Sentry user context
        setSentryUser({
          id: userData.sub,
          email: userData.email,
          username: userData.email,
        })

        // Add breadcrumb for successful authentication
        addBreadcrumb('User authentication refreshed', 'auth', 'info', {
          userId: userData.sub,
          email: userData.email,
        })
      } else {
        // Clear auth state if tokens are invalid
        useAuthStore.setState({
          isAuthenticated: false,
          user: null,
          token: null,
        })

        // Clear Sentry user context
        clearSentryUser()

        // Add breadcrumb for authentication cleared
        addBreadcrumb('User authentication cleared', 'auth', 'info')
      }
    } catch (error) {
      console.error('Auth refresh failed:', error)
      useAuthStore.setState({
        isAuthenticated: false,
        user: null,
        token: null,
        error: 'Authentication refresh failed',
      })
    }
  }

  return {
    // State from store
    isAuthenticated: authStore.isAuthenticated,
    isLoading: authStore.isLoading || !isInitialized,
    user: authStore.user,
    error: authStore.error,
    token: authStore.token,

    // Actions from store
    signIn: authStore.signIn,
    signOut: authStore.signOut,
    setNewPassword: authStore.setNewPassword,
    confirmMFA: authStore.confirmMFA,
    resetPassword: authStore.resetPassword,
    confirmResetPassword: authStore.confirmResetPassword,
    clearError: authStore.clearError,
    setLoading: authStore.setLoading,

    // Utility methods
    checkAuthStatus,
    refreshAuth,
  }
}
