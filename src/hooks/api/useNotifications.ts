/**
 * Notifications API Hooks
 *
 * TanStack Query hooks for notification management operations
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import { notificationService } from '@/api/services/notificationService'
import { QUERY_KEYS, MUTATION_KEYS } from '@/config/api'
import { useAuthStore } from '@/store/authStore'
import {
  NotificationListParams,
  BulkMarkReadRequest,
  NotificationPreferences,
} from '@/types/api/notifications'
import { FirebaseTokenRegistrationRequest } from '@/types/api/auth'

/**
 * Hook to get notifications list
 */
export const useNotifications = (params: NotificationListParams = {}) => {
  return useQuery({
    queryKey: QUERY_KEYS.notifications.list(params),
    queryFn: () => notificationService.getNotifications(params),
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

/**
 * Hook to get a single notification
 */
export const useNotification = (notificationId: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.notifications.detail(notificationId),
    queryFn: () => notificationService.getNotification(notificationId),
    enabled: !!notificationId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  })
}

/**
 * Hook to get recent notifications
 */
export const useRecentNotifications = (limit: number = 10) => {
  return useQuery({
    queryKey: QUERY_KEYS.notifications.list({ recent: true, limit }),
    queryFn: () => notificationService.getRecentNotifications(limit),
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
    refetchInterval: 60 * 1000, // Refetch every minute
  })
}

/**
 * Hook to get unread notifications
 */
export const useUnreadNotifications = (limit: number = 50) => {
  return useQuery({
    queryKey: QUERY_KEYS.notifications.list({ unread: true, limit }),
    queryFn: () => notificationService.getUnreadNotifications(limit),
    staleTime: 45 * 1000, // 45 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
    refetchInterval: 60 * 1000, // Refetch every 60 seconds
  })
}

/**
 * Hook to get notification preferences
 */
export const useNotificationPreferences = (userId?: string) => {
  const authStore = useAuthStore()
  const currentUserId = userId || authStore.user?.sub

  return useQuery({
    queryKey: QUERY_KEYS.notifications.detail(`${currentUserId}-preferences`),
    queryFn: () =>
      notificationService.getNotificationPreferences(currentUserId!),
    enabled: !!currentUserId,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
  })
}

// MUTATION HOOKS

/**
 * Mutation hook to mark notification as read
 */
export const useMarkNotificationRead = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: [MUTATION_KEYS.MARK_NOTIFICATION_READ],
    mutationFn: ({
      notificationId,
      entity,
    }: {
      notificationId: string
      entity?: 'dispatcher' | 'responder'
    }) => notificationService.markNotificationAsRead(notificationId, entity),
    onSuccess: (updatedNotification, { notificationId }) => {
      // Update the specific notification in cache
      queryClient.setQueryData(
        QUERY_KEYS.notifications.detail(notificationId),
        updatedNotification
      )

      // Invalidate notifications lists to reflect read status
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.notifications.lists(),
      })

      // Update unread count
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.notifications.list({ unread_count: true }),
      })
    },
    onError: (error: any) => {
      console.error('Failed to mark notification as read:', error)
      // Don't show toast for this operation as it's usually automatic
    },
  })
}

/**
 * Mutation hook to bulk mark notifications as read
 */
export const useBulkMarkAsRead = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: [MUTATION_KEYS.MARK_NOTIFICATION_READ, 'bulk'],
    mutationFn: (data: BulkMarkReadRequest) =>
      notificationService.bulkMarkAsRead(data),
    onSuccess: result => {
      // Invalidate all notification queries
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.notifications.all,
      })

      if (result.success_count > 0) {
        toast.success(`${result.success_count} notifications marked as read`)
      }
    },
    onError: (error: any) => {
      console.error('Failed to mark notifications as read:', error)
      toast.error(error.message || 'Failed to mark notifications as read')
    },
  })
}

/**
 * Mutation hook to mark all notifications as read
 */
export const useMarkAllAsRead = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: [MUTATION_KEYS.MARK_NOTIFICATION_READ, 'all'],
    mutationFn: (entity: 'dispatcher' | 'responder' = 'dispatcher') =>
      notificationService.markAllAsRead(entity),
    onSuccess: count => {
      // Invalidate all notification queries
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.notifications.all,
      })

      if (count > 0) {
        toast.success(`All ${count} notifications marked as read`)
      } else {
        toast('No unread notifications')
      }
    },
    onError: (error: any) => {
      console.error('Failed to mark all notifications as read:', error)
      toast.error(error.message || 'Failed to mark all notifications as read')
    },
  })
}

/**
 * Mutation hook to register Firebase token
 */
export const useRegisterFirebaseToken = () => {
  return useMutation({
    mutationKey: [MUTATION_KEYS.REGISTER_FIREBASE_TOKEN],
    mutationFn: (data: FirebaseTokenRegistrationRequest) =>
      notificationService.registerFirebaseToken(data),
    onError: (error: any) => {
      console.error('Failed to register Firebase token:', error)
      // Don't show error toast as this is not critical for app functionality
    },
  })
}

/**
 * Mutation hook to update notification preferences
 */
export const useUpdateNotificationPreferences = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: [MUTATION_KEYS.UPDATE_DISPATCHER, 'notification-preferences'],
    mutationFn: ({
      userId,
      preferences,
    }: {
      userId: string
      preferences: Partial<NotificationPreferences>
    }) =>
      notificationService.updateNotificationPreferences(userId, preferences),
    onSuccess: (updatedPreferences, { userId }) => {
      // Update preferences in cache
      queryClient.setQueryData(
        QUERY_KEYS.notifications.detail(`${userId}-preferences`),
        updatedPreferences
      )

      toast.success('Notification preferences updated')
    },
    onError: (error: any) => {
      console.error('Failed to update notification preferences:', error)
      toast.error(error.message || 'Failed to update notification preferences')
    },
  })
}
