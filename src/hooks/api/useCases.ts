/**
 * Cases API Hooks
 *
 * TanStack Query hooks for case management operations
 */

import {
  useQuery,
  useMutation,
  useQueryClient,
  useInfiniteQuery,
} from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import { caseService } from '@/api/services/caseService'
import { QUERY_KEYS, MUTATION_KEYS } from '@/config/api'
import {
  CreateCaseRequest,
  UpdateCaseRequest,
  ApproveCaseRequest,
  CloseCaseRequest,
  CaseUploadRequest,
  CaseListParams,
} from '@/types/api/cases'

/**
 * Hook to get a single case by ID
 */
export const useCase = (caseId: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.cases.detail(caseId),
    queryFn: () => caseService.getCase(caseId),
    enabled: !!caseId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error: any) => {
      if (error?.code === 404) return false
      return failureCount < 2
    },
  })
}

/**
 * Hook to get cases list with pagination
 */
export const useCases = (params: CaseListParams = {}) => {
  return useQuery({
    queryKey: QUERY_KEYS.cases.list(params),
    queryFn: () => caseService.getCases(params),
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

/**
 * Hook for infinite scrolling cases list
 */
export const useInfiniteCases = (params: Omit<CaseListParams, 'page'> = {}) => {
  return useInfiniteQuery({
    queryKey: QUERY_KEYS.cases.infinite(params),
    queryFn: ({ pageParam = 1 }) =>
      caseService.getCases({ ...params, page: pageParam }),
    initialPageParam: 1,
    getNextPageParam: lastPage => {
      const { pagination } = lastPage
      return pagination.hasNextPage ? pagination.currentPage + 1 : undefined
    },
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to get recent cases
 */
export const useRecentCases = (limit: number = 10) => {
  return useQuery({
    queryKey: QUERY_KEYS.cases.list({ recent: true, limit }),
    queryFn: () => caseService.getRecentCases(limit),
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

/**
 * Hook to get active cases
 */
export const useActiveCases = () => {
  return useQuery({
    queryKey: QUERY_KEYS.cases.list({ active: true }),
    queryFn: () => caseService.getActiveCases(),
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
    refetchInterval: 60 * 1000, // Refetch every minute for active cases
  })
}

/**
 * Hook to get cases by status
 */
export const useCasesByStatus = (status: string, limit: number = 10) => {
  return useQuery({
    queryKey: QUERY_KEYS.cases.list({ status, limit }),
    queryFn: () => caseService.getCasesByStatus(status, limit),
    enabled: !!status,
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

/**
 * Hook to get dashboard metrics and cases
 */
export const useCaseStats = (
  precinctId?: string,
  startDate?: string,
  stopDate?: string
) => {
  return useQuery({
    queryKey: QUERY_KEYS.cases.list({
      precinctId,
      startDate,
      stopDate,
    }),
    queryFn: () =>
      caseService.getDashboardCases(precinctId, startDate, stopDate),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
  })
}

/**
 * Hook to get case activity log
 */
export const useCaseActivity = (caseId: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.cases.detail(`${caseId}-activity`),
    queryFn: () => caseService.getCaseActivity(caseId),
    enabled: !!caseId,
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  })
}

/**
 * Hook to get case timeline
 */
export const useCaseTimeline = (caseId: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.cases.detail(`${caseId}-timeline`),
    queryFn: () => caseService.getCaseTimeline(caseId),
    enabled: !!caseId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  })
}

/**
 * Hook to search cases
 */
export const useSearchCases = (
  query: string,
  filters?: Partial<CaseListParams>
) => {
  return useQuery({
    queryKey: QUERY_KEYS.cases.list({ search: query, ...filters }),
    queryFn: () => caseService.searchCases(query, filters),
    enabled: !!query && query.length >= 2,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

// MUTATION HOOKS

/**
 * Mutation hook to create a new case
 */
export const useCreateCase = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: [MUTATION_KEYS.CREATE_CASE],
    mutationFn: (data: CreateCaseRequest) => {
      // Validate data before submission
      const errors = caseService.validateCaseData(data)
      if (errors.length > 0) {
        throw new Error(`Validation failed: ${errors.join(', ')}`)
      }
      return caseService.createCase(data)
    },
    onSuccess: newCase => {
      // Invalidate and refetch cases lists
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.cases.lists(),
      })

      // Add the new case to the cache
      queryClient.setQueryData(
        QUERY_KEYS.cases.detail(newCase.case_id),
        newCase
      )

      toast.success('Case created successfully')
    },
    onError: (error: any) => {
      console.error('Failed to create case:', error)
      toast.error(error.message || 'Failed to create case')
    },
  })
}

/**
 * Mutation hook to update a case
 */
export const useUpdateCase = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: [MUTATION_KEYS.UPDATE_CASE],
    mutationFn: ({
      caseId,
      data,
    }: {
      caseId: string
      data: UpdateCaseRequest
    }) => {
      // Validate data before submission
      const errors = caseService.validateCaseData(data)
      if (errors.length > 0) {
        throw new Error(`Validation failed: ${errors.join(', ')}`)
      }
      return caseService.updateCase(caseId, data)
    },
    onSuccess: (updatedCase, { caseId }) => {
      // Update the specific case in cache
      queryClient.setQueryData(QUERY_KEYS.cases.detail(caseId), updatedCase)

      // Invalidate cases lists to reflect changes
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.cases.lists(),
      })

      toast.success('Case updated successfully')
    },
    onError: (error: any) => {
      console.error('Failed to update case:', error)
      toast.error(error.message || 'Failed to update case')
    },
  })
}

/**
 * Mutation hook to approve a case
 */
export const useApproveCase = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: [MUTATION_KEYS.APPROVE_CASE],
    mutationFn: ({
      caseId,
      data,
    }: {
      caseId: string
      data: ApproveCaseRequest
    }) => caseService.approveCase(caseId, data),
    onSuccess: (approvedCase, { caseId }) => {
      // Update the specific case in cache
      queryClient.setQueryData(QUERY_KEYS.cases.detail(caseId), approvedCase)

      // Invalidate cases lists
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.cases.lists(),
      })

      // Invalidate case activity
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.cases.detail(`${caseId}-activity`),
      })

      toast.success('Case approved successfully')
    },
    onError: (error: any) => {
      console.error('Failed to approve case:', error)
      toast.error(error.message || 'Failed to approve case')
    },
  })
}

/**
 * Mutation hook to close a case
 */
export const useCloseCase = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: [MUTATION_KEYS.CLOSE_CASE],
    mutationFn: ({
      caseId,
      data,
    }: {
      caseId: string
      data: CloseCaseRequest
    }) => caseService.closeCase(caseId, data),
    onSuccess: (closedCase, { caseId }) => {
      // Update the specific case in cache
      queryClient.setQueryData(QUERY_KEYS.cases.detail(caseId), closedCase)

      // Invalidate cases lists
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.cases.lists(),
      })

      // Invalidate case activity and timeline
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.cases.detail(`${caseId}-activity`),
      })
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.cases.detail(`${caseId}-timeline`),
      })

      // Invalidate case stats
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.cases.list({ stats: true }),
      })

      toast.success('Case closed successfully')
    },
    onError: (error: any) => {
      console.error('Failed to close case:', error)
      toast.error(error.message || 'Failed to close case')
    },
  })
}

/**
 * Mutation hook to upload multiple cases
 */
export const useUploadCases = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: [MUTATION_KEYS.UPLOAD_CASE],
    mutationFn: (data: CaseUploadRequest) => caseService.uploadCases(data),
    onSuccess: result => {
      // Invalidate all cases lists to show new cases
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.cases.lists(),
      })

      // Show success message with details
      const { uploaded_count, failed_count } = result
      if (failed_count > 0) {
        toast.success(
          `${uploaded_count} cases uploaded successfully, ${failed_count} failed`
        )
      } else {
        toast.success(`${uploaded_count} cases uploaded successfully`)
      }
    },
    onError: (error: any) => {
      console.error('Failed to upload cases:', error)
      toast.error(error.message || 'Failed to upload cases')
    },
  })
}

/**
 * Mutation hook to initiate USSD case
 */
export const useInitiateUssdCase = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: [MUTATION_KEYS.INITIATE_USSD_CASE],
    mutationFn: (caseId: string) => caseService.initiateUssdCase(caseId),
    onSuccess: (updatedCase, caseId) => {
      // Update the specific case in cache
      queryClient.setQueryData(QUERY_KEYS.cases.detail(caseId), updatedCase)

      // Invalidate cases lists
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.cases.lists(),
      })

      toast.success('Offline dispatch initiated successfully')
    },
    onError: (error: any) => {
      console.error('Failed to initiate offline dispatch:', error)
      toast.error(error.message || 'Failed to initiate offline dispatch')
    },
  })
}

// Additional case-related hooks from useAdditional.ts

/**
 * Hook to search all cases (from additional service)
 */
export const useSearchAllCases = (searchParam: string, searchValue: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.cases.list({ search: true, searchParam, searchValue }),
    queryFn: () => caseService.searchAllCases(searchParam, searchValue),
    enabled: !!(searchParam && searchValue && searchValue.length >= 2),
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

/**
 * Hook to get incident report by case ID
 */
export const useIncidentReport = (caseId: string) => {
  return useQuery({
    queryKey: ['incidentReport', caseId],
    queryFn: () => caseService.getIncidentReportByID(caseId),
    enabled: !!caseId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  })
}

/**
 * Hook to get case responders
 */
export const useCaseResponders = (caseId: string) => {
  return useQuery({
    queryKey: ['caseResponders', caseId],
    queryFn: () => caseService.getCaseResponders(caseId),
    enabled: !!caseId,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    refetchInterval: 60 * 1000, // Refetch every minute
  })
}

/**
 * Hook to get case metrics
 */
export const useCaseMetrics = () => {
  return useQuery({
    queryKey: ['caseMetrics'],
    queryFn: () => caseService.getCasesMetrics(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
  })
}

/**
 * Hook to get distance matrix
 */
export const useDistanceMatrix = (origin: string, destinations: string) => {
  return useQuery({
    queryKey: ['distanceMatrix', origin, destinations],
    queryFn: () => caseService.getDistance(origin, destinations),
    enabled: !!(origin && destinations),
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
  })
}
