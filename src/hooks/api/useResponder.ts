/**
 * Responder API Hooks
 *
 * TanStack Query hooks for responder-related operations
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import { responderService } from '@/api/services/responderService'
import { MUTATION_KEYS } from '@/api'
import { NotifyResponderRequest } from '@/types/api/responders'

/**
 * Hook to get all responders
 */
export const useAllResponders = (
  pageNumber: number = 1,
  numberPerPage: number = 500
) => {
  return useQuery({
    queryKey: [MUTATION_KEYS.RESPONDERS, pageNumber, numberPerPage],
    queryFn: () => responderService.getAllResponders(pageNumber, numberPerPage),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  })
}

// MUTATION HOOKS

/**
 * Mutation hook to notify responders
 */
export const useNotifyResponder = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: [MUTATION_KEYS.NOTIFY_RESPONDER],
    mutationFn: (data: NotifyResponderRequest) =>
      responderService.notifyResponder(data),
    onSuccess: (_, { caseId }) => {
      // Invalidate case responders to show updated status
      queryClient.invalidateQueries({
        queryKey: ['caseResponders', caseId],
      })

      toast.success('Responders notified successfully')
    },
    onError: (error: any) => {
      console.error('Failed to notify responders:', error)
      toast.error(error.message || 'Failed to notify responders')
    },
  })
}
