/**
 * Authentication API Hooks
 *
 * TanStack Query hooks for authentication-related API calls
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import { authService } from '@/api/services/authService'
import { QUERY_KEYS, MUTATION_KEYS } from '@/config/api'
import { useAuthStore } from '@/store/authStore'
import { UpdateDispatcherRequest } from '@/types/api/auth'

/**
 * Hook to get dispatcher information
 */
export const useDispatcher = (dispatcherId?: string) => {
  const authStore = useAuthStore()
  const userId = dispatcherId || authStore.user?.sub

  return useQuery({
    queryKey: QUERY_KEYS.dispatchers.detail(userId || ''),
    queryFn: () => authService.getDispatcher(userId!),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error: any) => {
      // Don't retry on 404 or auth errors
      if (error?.code === 404 || error?.code === 401 || error?.code === 403) {
        return false
      }
      return failureCount < 2
    },
  })
}

/**
 * Hook to get precinct information
 */
export const usePrecinct = (dispatcherId?: string) => {
  const authStore = useAuthStore()
  const userId = dispatcherId || authStore.user?.sub

  return useQuery({
    queryKey: QUERY_KEYS.precinct.detail(userId || 'current'),
    queryFn: () => authService.getPrecinct(),
    enabled: !!userId,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
  })
}

/**
 * Hook to get dispatcher data (with caching)
 */
export const useDispatcherData = () => {
  const authStore = useAuthStore()
  const userId = authStore.user?.sub

  return useQuery({
    queryKey: QUERY_KEYS.dispatchers.detail(`${userId}-data`),
    queryFn: () => authService.getDispatcher(userId!),
    enabled: !!userId,
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
    retry: 2,
  })
}

/**
 * Hook to automatically fetch dispatcher data on login
 */
export const useAutoFetchDispatcherData = () => {
  const { user, isAuthenticated } = useAuthStore()

  return useQuery({
    queryKey: QUERY_KEYS.dispatchers.detail(user?.sub || 'current'),
    queryFn: () => {
      const dispatcherId = user?.sub || user?.attributes?.sub
      if (!dispatcherId) {
        throw new Error('No dispatcher ID found in user data')
      }
      return authService.getDispatcher(dispatcherId)
    },
    enabled: isAuthenticated && !!(user?.sub || user?.attributes?.sub),
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  })
}

/**
 * Mutation hook to update dispatcher information
 */
export const useUpdateDispatcher = () => {
  const queryClient = useQueryClient()
  const authStore = useAuthStore()
  const dispatcherId = authStore.user?.sub

  return useMutation({
    mutationKey: [MUTATION_KEYS.UPDATE_DISPATCHER],
    mutationFn: (data: UpdateDispatcherRequest) =>
      authService.updateDispatcher(data),
    onSuccess: updatedDispatcher => {
      if (dispatcherId) {
        // Update the dispatcher query cache
        queryClient.setQueryData(
          QUERY_KEYS.dispatchers.detail(dispatcherId),
          updatedDispatcher
        )

        // Update the dispatcher data cache as well
        queryClient.setQueryData(
          QUERY_KEYS.dispatchers.detail(`${dispatcherId}-data`),
          updatedDispatcher
        )
      }

      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.dispatchers.details(),
      })

      toast.success('Profile updated successfully')
    },
    onError: (error: any) => {
      console.error('Failed to update dispatcher:', error)
      toast.error(error.message || 'Failed to update profile')
    },
  })
}

/**
 * Hook to clear authentication cache
 */
export const useClearAuthCache = () => {
  const queryClient = useQueryClient()

  return () => {
    // Clear React Query cache
    queryClient.invalidateQueries({
      queryKey: QUERY_KEYS.dispatchers.all,
    })
    queryClient.invalidateQueries({
      queryKey: QUERY_KEYS.precinct.all,
    })

    console.log('Authentication cache cleared')
  }
}
