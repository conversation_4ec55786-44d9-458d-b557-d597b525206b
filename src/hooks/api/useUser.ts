/**
 * User API Hooks
 *
 * TanStack Query hooks for user-related operations
 */

import { useQuery } from '@tanstack/react-query'
import { userService } from '@/api/services/userService'

/**
 * Hook to get users
 */
export const useUsers = () => {
  return useQuery({
    queryKey: ['users'],
    queryFn: () => userService.getUsers(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
  })
}

/**
 * Hook to get users lookup
 */
export const useUsersLookup = () => {
  return useQuery({
    queryKey: ['usersLookup'],
    queryFn: () => userService.getUsersLookup(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
  })
}
