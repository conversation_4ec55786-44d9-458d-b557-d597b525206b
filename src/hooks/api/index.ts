/**
 * API Hooks Index
 *
 * Central export point for all TanStack Query API hooks
 */

// Authentication Hooks
export {
  useDispatcher,
  usePrecinct,
  useDispatcherData,
  useAutoFetchDispatcherData,
  useUpdateDispatcher,
  useClearAuthCache,
} from './useAuth'

// Cases Hooks
export {
  useCase,
  useCases,
  useInfiniteCases,
  useRecentCases,
  useActiveCases,
  useCasesByStatus,
  useCaseStats,
  useCaseActivity,
  useCaseTimeline,
  useSearchCases,
  useCreateCase,
  useUpdateCase,
  useApproveCase,
  useCloseCase,
  useUploadCases,
  useInitiateUssdCase,
} from './useCases'

// Notifications Hooks
export {
  useNotifications,
  useNotification,
  useRecentNotifications,
  useUnreadNotifications,
  useNotificationPreferences,
  useMarkNotificationRead,
  useBulkMarkAsRead,
  useMarkAllAsRead,
  useRegisterFirebaseToken as useRegisterNotificationToken,
  useUpdateNotificationPreferences,
} from './useNotifications'

// References Hooks
export {
  useReferences,
  useReferencesByCategory,
  useIncidentTypes,
  useProvisionalDiagnoses,
  useActionsTaken,
  useProbableCauses,
  useReferenceStats,
  useReferenceItem,
  useSearchReferences,
  useDiagnosisRecommendations,
  useClearReferenceCache,
  useRefreshReferences,
  useFindReferenceByCode,
  useValidateReferences,
  useCommonReferences,
} from './useReferences'

// Inquiries Hooks
export {
  useInquiries,
  useInquiry,
  useRecentInquiries,
  useActiveInquiries,
  useInquiriesByStatus,
  useInquiryStats,
  useInquiryActivity,
  useInquiryComments,
  useSearchInquiries,
  useCreateInquiry,
  useUpdateInquiry,
  useAssignInquiry,
  useResolveInquiry,
  useCloseInquiry,
  useAddInquiryComment,
} from './useInquiries'

// Ambulance Services Hooks
export {
  useAmbulanceProviders,
  useAmbulanceProvider,
  useActiveProviders,
  useProvidersByServiceLevel,
  useNearestProviders,
  useAmbulancePricing,
  useCachedPricing,
  useProviderStats,
  useVehicleAvailability,
  useSearchProviders,
  useGetDetailedPricing,
  useClearPricingCache,
  useRefreshProviders,
  useCommonPricingScenarios,
  useProviderRecommendations,
} from './useAmbulance'

// Case-related hooks
export {
  useSearchAllCases,
  useIncidentReport,
  useCaseResponders,
  useCaseMetrics,
  useDistanceMatrix,
} from './useCases'

// Additional ambulance hooks (extending existing ambulance hooks)
export {
  useAmbulanceRequests,
  useAmbulanceRequest,
  useAmbulanceMetrics,
  useCreateAmbulanceRequest,
  useUpdateAmbulanceRequest,
  useApproveAmbulanceRequest,
  useUpdateAmbulanceDropoffLocation,
  useUpdateAmbulancePayment,
  useCancelAmbulanceRequest,
} from './useAmbulance'

// Hospital-related hooks
export {
  useAllHospitalsInit,
  useAllHospitals,
  useHospitalCases,
  useAddHospitalTransfer,
  useAssignHospitalCase,
} from './useHospital'

// Responder-related hooks
export { useAllResponders, useNotifyResponder } from './useResponder'

// User-related hooks
export { useUsers, useUsersLookup } from './useUser'

// Re-export common hook utilities
export type {
  UseQueryResult,
  UseMutationResult,
  UseInfiniteQueryResult,
} from '@tanstack/react-query'
