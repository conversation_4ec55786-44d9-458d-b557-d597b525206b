/**
 * Additional API Hooks
 *
 * TanStack Query hooks for additional API operations
 */

import { useMutation } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import { additionalService } from '@/api/services/additionalService'
import { MUTATION_KEYS } from '@/config/api'

/**
 * Mutation hook to verify NHIS enrollment
 */
export const useVerifyNHIS = () => {
  return useMutation({
    mutationKey: [MUTATION_KEYS.VERIFY_NHIS],
    mutationFn: (phone: string) => additionalService.verifyNHIS(phone),
    onError: (error: any) => {
      console.error('NHIS verification failed:', error)
      toast.error(error.message || 'Failed to verify NHIS enrollment')
    },
  })
}
