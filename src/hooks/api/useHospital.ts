/**
 * Hospital API Hooks
 *
 * TanStack Query hooks for hospital-related operations
 */

import hospitalService from '@/api/services/hospitalService'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'

/**
 * Hook to get all hospitals (initial load)
 */
export const useAllHospitalsInit = () => {
  return useQuery({
    queryKey: ['hospitalsInit'],
    queryFn: () => hospitalService.getAllHospitalsInit(),
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
  })
}

/**
 * Hook to get all hospitals
 */
export const useAllHospitals = () => {
  return useQuery({
    queryKey: ['hospitals'],
    queryFn: () => hospitalService.getAllHospitals(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
  })
}

/**
 * Hook to get hospital cases
 */
export const useHospitalCases = () => {
  return useQuery({
    queryKey: ['hospitalCases'],
    queryFn: () => hospitalService.getHospitalCases(),
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    refetchInterval: 2 * 60 * 1000, // Refetch every 2 minutes
  })
}

// MUTATION HOOKS

/**
 * Mutation hook to add hospital transfer
 */
export const useAddHospitalTransfer = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ['addHospitalTransfer'],
    mutationFn: ({
      hospitalId,
      caseId,
      report,
      notes,
    }: {
      hospitalId: string
      caseId: string
      report: string
      notes: string
    }) =>
      hospitalService.addHospitalTransfer(hospitalId, caseId, report, notes),
    onSuccess: () => {
      // Invalidate hospital cases to show new transfer
      queryClient.invalidateQueries({
        queryKey: ['hospitalCases'],
      })

      toast.success('Hospital transfer added successfully')
    },
    onError: (error: any) => {
      console.error('Failed to add hospital transfer:', error)
      toast.error(error.message || 'Failed to add hospital transfer')
    },
  })
}

/**
 * Mutation hook to assign hospital case
 */
export const useAssignHospitalCase = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ['assignHospitalCase'],
    mutationFn: ({
      hospitalId,
      caseId,
    }: {
      hospitalId: string
      caseId: string
    }) => hospitalService.assignHospitalCase(hospitalId, caseId),
    onSuccess: () => {
      // Invalidate hospital cases to show assignment
      queryClient.invalidateQueries({
        queryKey: ['hospitalCases'],
      })

      toast.success('Hospital case assigned successfully')
    },
    onError: (error: any) => {
      console.error('Failed to assign hospital case:', error)
      toast.error(error.message || 'Failed to assign hospital case')
    },
  })
}
