/**
 * Ambulance Services API Hooks
 *
 * TanStack Query hooks for ambulance provider and pricing operations
 */

import React from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import { ambulanceService } from '@/api/services/ambulanceService'
import { QUERY_KEYS } from '@/config/api'
import {
  AmbulancePricingRequest,
  ProviderListParams,
  CreateAmbulanceRequestData,
  UpdateAmbulanceRequestData,
  UpdateDropoffLocationData,
} from '@/types/api/ambulance'

/**
 * Hook to get ambulance providers list
 */
export const useAmbulanceProviders = (params: ProviderListParams = {}) => {
  return useQuery({
    queryKey: QUERY_KEYS.ambulanceProviders.list(params),
    queryFn: () => ambulanceService.getAmbulanceProviders(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  })
}

/**
 * Hook to get a single ambulance provider
 */
export const useAmbulanceProvider = (providerId: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.ambulanceProviders.detail(providerId),
    queryFn: () => ambulanceService.getAmbulanceProvider(providerId),
    enabled: !!providerId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
  })
}

/**
 * Hook to get active ambulance providers
 */
export const useActiveProviders = (limit: number = 50) => {
  return useQuery({
    queryKey: QUERY_KEYS.ambulanceProviders.list({ active: true, limit }),
    queryFn: () => ambulanceService.getActiveProviders(limit),
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    refetchInterval: 2 * 60 * 1000, // Refetch every 2 minutes
  })
}

/**
 * Hook to get providers by service level
 */
export const useProvidersByServiceLevel = (
  serviceLevel: string,
  limit: number = 20
) => {
  return useQuery({
    queryKey: QUERY_KEYS.ambulanceProviders.list({ serviceLevel, limit }),
    queryFn: () =>
      ambulanceService.getProvidersByServiceLevel(serviceLevel, limit),
    enabled: !!serviceLevel,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  })
}

/**
 * Hook to get nearest providers
 */
export const useNearestProviders = (
  location?: { latitude: number; longitude: number },
  limit: number = 10
) => {
  return useQuery({
    queryKey: QUERY_KEYS.ambulanceProviders.list({
      nearest: true,
      location: location
        ? `${location.latitude},${location.longitude}`
        : undefined,
      limit,
    }),
    queryFn: () => ambulanceService.getNearestProviders(location!, limit),
    enabled: !!location,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    refetchInterval: 60 * 1000, // Refetch every minute
  })
}

/**
 * Hook to get ambulance pricing
 */
export const useAmbulancePricing = (request: AmbulancePricingRequest) => {
  return useQuery({
    queryKey: QUERY_KEYS.ambulancePricing.list(request),
    queryFn: () => ambulanceService.getAmbulancePricing(request),
    enabled: !!(request.request_category && request.request_type),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
  })
}

/**
 * Hook to get cached pricing (for common scenarios)
 */
export const useCachedPricing = (
  requestCategory: string,
  isCovidCase: boolean,
  requiresOxygen: boolean
) => {
  return useQuery({
    queryKey: QUERY_KEYS.ambulancePricing.list({
      cached: true,
      requestCategory,
      isCovidCase,
      requiresOxygen,
    }),
    queryFn: () =>
      ambulanceService.getCachedPricing(
        requestCategory,
        isCovidCase,
        requiresOxygen
      ),
    enabled: !!requestCategory,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
  })
}

/**
 * Hook to get provider statistics
 */
export const useProviderStats = (
  precinctId?: string,
  dateFrom?: string,
  dateTo?: string
) => {
  return useQuery({
    queryKey: QUERY_KEYS.ambulanceProviders.list({
      stats: true,
      precinctId,
      dateFrom,
      dateTo,
    }),
    queryFn: () =>
      ambulanceService.getProviderStats(precinctId, dateFrom, dateTo),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
  })
}

/**
 * Hook to get vehicle availability
 */
export const useVehicleAvailability = (
  location?: { latitude: number; longitude: number },
  radiusKm: number = 50
) => {
  return useQuery({
    queryKey: QUERY_KEYS.ambulanceProviders.list({
      availability: true,
      location: location
        ? `${location.latitude},${location.longitude}`
        : undefined,
      radiusKm,
    }),
    queryFn: () => ambulanceService.getVehicleAvailability(location, radiusKm),
    enabled: !!location,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
    refetchInterval: 60 * 1000, // Refetch every minute
  })
}

/**
 * Hook to search providers
 */
export const useSearchProviders = (
  query: string,
  filters?: Partial<ProviderListParams>
) => {
  return useQuery({
    queryKey: QUERY_KEYS.ambulanceProviders.list({ search: query, ...filters }),
    queryFn: () => ambulanceService.searchProviders(query, filters),
    enabled: !!query && query.length >= 2,
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

// MUTATION HOOKS

/**
 * Mutation hook to get detailed pricing
 */
export const useGetDetailedPricing = () => {
  return useMutation({
    mutationKey: ['getDetailedPricing'],
    mutationFn: (request: AmbulancePricingRequest) => {
      // Validate request before submission
      const errors = ambulanceService.validatePricingRequest(request)
      if (errors.length > 0) {
        throw new Error(`Validation failed: ${errors.join(', ')}`)
      }
      return ambulanceService.getDetailedPricing(request)
    },
    onError: (error: any) => {
      console.error('Failed to get detailed pricing:', error)
      toast.error(error.message || 'Failed to get pricing information')
    },
  })
}

/**
 * Hook to clear pricing cache
 */
export const useClearPricingCache = () => {
  const queryClient = useQueryClient()

  return () => {
    // Clear React Query cache
    queryClient.invalidateQueries({
      queryKey: QUERY_KEYS.ambulancePricing.all,
    })

    // Clear service cache
    ambulanceService.clearPricingCache()

    console.log('Ambulance pricing cache cleared')
    toast.success('Pricing data refreshed')
  }
}

/**
 * Hook to refresh provider data
 */
export const useRefreshProviders = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ['refreshProviders'],
    mutationFn: async () => {
      // Invalidate all provider queries to trigger refetch
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.ambulanceProviders.all,
      })

      // Return a promise that resolves when queries are refetched
      return Promise.resolve()
    },
    onSuccess: () => {
      toast.success('Provider data refreshed successfully')
    },
    onError: (error: any) => {
      console.error('Failed to refresh provider data:', error)
      toast.error(error.message || 'Failed to refresh provider data')
    },
  })
}

// UTILITY HOOKS

/**
 * Hook to get pricing for common scenarios
 */
export const useCommonPricingScenarios = () => {
  const emergencyPricing = useCachedPricing('emergency', false, false)
  const covidPricing = useCachedPricing('emergency', true, false)
  const oxygenPricing = useCachedPricing('emergency', false, true)
  const criticalPricing = useCachedPricing('critical_care', false, true)

  return {
    emergency: emergencyPricing.data || [],
    covid: covidPricing.data || [],
    oxygen: oxygenPricing.data || [],
    critical: criticalPricing.data || [],
    isLoading:
      emergencyPricing.isLoading ||
      covidPricing.isLoading ||
      oxygenPricing.isLoading ||
      criticalPricing.isLoading,
    error:
      emergencyPricing.error ||
      covidPricing.error ||
      oxygenPricing.error ||
      criticalPricing.error,
  }
}

/**
 * Hook to get provider recommendations based on criteria
 */
export const useProviderRecommendations = (
  location?: { latitude: number; longitude: number },
  serviceLevel?: string,
  maxResponseTime?: number
) => {
  const nearestProviders = useNearestProviders(location, 20)
  const serviceProviders = useProvidersByServiceLevel(serviceLevel || '', 20)

  // Filter and combine results
  const recommendations = React.useMemo(() => {
    if (!nearestProviders.data && !serviceProviders.data) return []

    let providers = nearestProviders.data || serviceProviders.data || []

    // Filter by response time if specified
    if (maxResponseTime) {
      providers = providers.filter(
        p => (p.average_response_time || 0) <= maxResponseTime
      )
    }

    // Sort by success rate and response time
    return providers
      .sort((a, b) => {
        const scoreA =
          (a.success_rate || 0) * 0.7 +
          (100 - (a.average_response_time || 0)) * 0.3
        const scoreB =
          (b.success_rate || 0) * 0.7 +
          (100 - (b.average_response_time || 0)) * 0.3
        return scoreB - scoreA
      })
      .slice(0, 10)
  }, [nearestProviders.data, serviceProviders.data, maxResponseTime])

  return {
    recommendations,
    isLoading: nearestProviders.isLoading || serviceProviders.isLoading,
    error: nearestProviders.error || serviceProviders.error,
  }
}

// Additional ambulance request hooks from useAdditional.ts

/**
 * Hook to get ambulance requests
 */
export const useAmbulanceRequests = (
  pageNumber: number = 1,
  numberPerPage: number = 10
) => {
  return useQuery({
    queryKey: ['ambulanceRequests', pageNumber, numberPerPage],
    queryFn: () =>
      ambulanceService.getAmbulanceRequests(pageNumber, numberPerPage),
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    refetchInterval: 2 * 60 * 1000, // Refetch every 2 minutes
  })
}

/**
 * Hook to get ambulance request by ID
 */
export const useAmbulanceRequest = (ambulanceRequestId: string) => {
  return useQuery({
    queryKey: ['ambulanceRequest', ambulanceRequestId],
    queryFn: () => ambulanceService.getAmbulanceRequestByID(ambulanceRequestId),
    enabled: !!ambulanceRequestId,
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  })
}

/**
 * Hook to get ambulance metrics
 */
export const useAmbulanceMetrics = () => {
  return useQuery({
    queryKey: ['ambulanceMetrics'],
    queryFn: () => ambulanceService.getAmbulanceMetrics(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
  })
}

// MUTATION HOOKS

/**
 * Mutation hook to create ambulance request
 */
export const useCreateAmbulanceRequest = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ['createAmbulanceRequest'],
    mutationFn: (data: CreateAmbulanceRequestData) =>
      ambulanceService.createAmbulanceRequest(data),
    onSuccess: () => {
      // Invalidate ambulance requests to show new request
      queryClient.invalidateQueries({
        queryKey: ['ambulanceRequests'],
      })

      toast.success('Ambulance request created successfully')
    },
    onError: (error: any) => {
      console.error('Failed to create ambulance request:', error)
      toast.error(error.message || 'Failed to create ambulance request')
    },
  })
}

/**
 * Mutation hook to update ambulance request
 */
export const useUpdateAmbulanceRequest = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ['updateAmbulanceRequest'],
    mutationFn: ({
      ambulanceRequestId,
      data,
    }: {
      ambulanceRequestId: string
      data: UpdateAmbulanceRequestData
    }) => ambulanceService.updateAmbulanceRequest(ambulanceRequestId, data),
    onSuccess: (_, { ambulanceRequestId }) => {
      // Update specific ambulance request in cache
      queryClient.invalidateQueries({
        queryKey: ['ambulanceRequest', ambulanceRequestId],
      })

      // Invalidate ambulance requests list
      queryClient.invalidateQueries({
        queryKey: ['ambulanceRequests'],
      })

      toast.success('Ambulance request updated successfully')
    },
    onError: (error: any) => {
      console.error('Failed to update ambulance request:', error)
      toast.error(error.message || 'Failed to update ambulance request')
    },
  })
}

/**
 * Mutation hook to approve ambulance request
 */
export const useApproveAmbulanceRequest = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ['approveAmbulanceRequest'],
    mutationFn: ({
      ambulanceRequestId,
      ambulanceProviderId,
    }: {
      ambulanceRequestId: string
      ambulanceProviderId: string
    }) =>
      ambulanceService.approveAmbulanceRequest(
        ambulanceRequestId,
        ambulanceProviderId
      ),
    onSuccess: (_, { ambulanceRequestId }) => {
      // Update specific ambulance request in cache
      queryClient.invalidateQueries({
        queryKey: ['ambulanceRequest', ambulanceRequestId],
      })

      // Invalidate ambulance requests list
      queryClient.invalidateQueries({
        queryKey: ['ambulanceRequests'],
      })

      toast.success('Ambulance request approved successfully')
    },
    onError: (error: any) => {
      console.error('Failed to approve ambulance request:', error)
      toast.error(error.message || 'Failed to approve ambulance request')
    },
  })
}

/**
 * Mutation hook to update ambulance dropoff location
 */
export const useUpdateAmbulanceDropoffLocation = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ['updateAmbulanceDropoffLocation'],
    mutationFn: ({
      ambulanceRequestId,
      caseId,
      data,
    }: {
      ambulanceRequestId: string
      caseId: string
      data: UpdateDropoffLocationData
    }) =>
      ambulanceService.updateAmbulanceDropoffLocation(
        ambulanceRequestId,
        caseId,
        data
      ),
    onSuccess: (_, { ambulanceRequestId }) => {
      // Update specific ambulance request in cache
      queryClient.invalidateQueries({
        queryKey: ['ambulanceRequest', ambulanceRequestId],
      })

      // Invalidate ambulance requests list
      queryClient.invalidateQueries({
        queryKey: ['ambulanceRequests'],
      })

      toast.success('Dropoff location updated successfully')
    },
    onError: (error: any) => {
      console.error('Failed to update dropoff location:', error)
      toast.error(error.message || 'Failed to update dropoff location')
    },
  })
}

/**
 * Mutation hook to update ambulance payment status
 */
export const useUpdateAmbulancePayment = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ['updateAmbulancePayment'],
    mutationFn: ({
      ambulanceRequestId,
      paymentStatus,
    }: {
      ambulanceRequestId: string
      paymentStatus: 'requires_payment' | 'confirmed'
    }) =>
      ambulanceService.updateAmbulancePayment(
        ambulanceRequestId,
        paymentStatus
      ),
    onSuccess: (_, { ambulanceRequestId }) => {
      // Update specific ambulance request in cache
      queryClient.invalidateQueries({
        queryKey: ['ambulanceRequest', ambulanceRequestId],
      })

      // Invalidate ambulance requests list
      queryClient.invalidateQueries({
        queryKey: ['ambulanceRequests'],
      })

      toast.success('Payment status updated successfully')
    },
    onError: (error: any) => {
      console.error('Failed to update payment status:', error)
      toast.error(error.message || 'Failed to update payment status')
    },
  })
}

/**
 * Mutation hook to cancel or restore ambulance request
 */
export const useCancelAmbulanceRequest = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ['cancelAmbulanceRequest'],
    mutationFn: ({
      ambulanceRequestId,
      requestData,
      requestStatus,
    }: {
      ambulanceRequestId: string
      requestData: { reason?: string; notes?: string }
      requestStatus: 'open' | 'cancelled'
    }) =>
      ambulanceService.cancelAmbulanceRequest(
        ambulanceRequestId,
        requestData,
        requestStatus
      ),
    onSuccess: (_, { ambulanceRequestId, requestStatus }) => {
      // Update specific ambulance request in cache
      queryClient.invalidateQueries({
        queryKey: ['ambulanceRequest', ambulanceRequestId],
      })

      // Invalidate ambulance requests list
      queryClient.invalidateQueries({
        queryKey: ['ambulanceRequests'],
      })

      const action = requestStatus === 'open' ? 'cancelled' : 'restored'
      toast.success(`Ambulance request ${action} successfully`)
    },
    onError: (error: any) => {
      console.error('Failed to cancel/restore ambulance request:', error)
      toast.error(error.message || 'Failed to update ambulance request')
    },
  })
}
