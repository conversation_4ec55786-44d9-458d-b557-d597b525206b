/**
 * Inquiries API Hooks
 *
 * TanStack Query hooks for inquiry management operations
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import { inquiryService } from '@/api/services/inquiryService'
import { QUERY_KEYS, MUTATION_KEYS } from '@/config/api'
import {
  CreateInquiryRequest,
  UpdateInquiryRequest,
  AssignInquiryRequest,
  ResolveInquiryRequest,
  InquiryListParams,
} from '@/types/api/inquiries'

/**
 * Hook to get inquiries list
 */
export const useInquiries = (params: InquiryListParams = {}) => {
  return useQuery({
    queryKey: QUERY_KEYS.inquiries.list(params),
    queryFn: () => inquiryService.getInquiries(params),
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

/**
 * Hook to get a single inquiry
 */
export const useInquiry = (inquiryId: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.inquiries.detail(inquiryId),
    queryFn: () => inquiryService.getInquiry(inquiryId),
    enabled: !!inquiryId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  })
}

/**
 * Hook to get recent inquiries
 */
export const useRecentInquiries = (limit: number = 10) => {
  return useQuery({
    queryKey: QUERY_KEYS.inquiries.list({ recent: true, limit }),
    queryFn: () => inquiryService.getRecentInquiries(limit),
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

/**
 * Hook to get active inquiries
 */
export const useActiveInquiries = () => {
  return useQuery({
    queryKey: QUERY_KEYS.inquiries.list({ active: true }),
    queryFn: () => inquiryService.getActiveInquiries(),
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
    refetchInterval: 60 * 1000, // Refetch every minute
  })
}

/**
 * Hook to get inquiries by status
 */
export const useInquiriesByStatus = (status: string, limit: number = 10) => {
  return useQuery({
    queryKey: QUERY_KEYS.inquiries.list({ status, limit }),
    queryFn: () => inquiryService.getInquiriesByStatus(status, limit),
    enabled: !!status,
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

/**
 * Hook to get inquiry statistics
 */
export const useInquiryStats = (
  precinctId?: string,
  dateFrom?: string,
  dateTo?: string
) => {
  return useQuery({
    queryKey: QUERY_KEYS.inquiries.list({
      stats: true,
      precinctId,
      dateFrom,
      dateTo,
    }),
    queryFn: () => inquiryService.getInquiryStats(precinctId, dateFrom, dateTo),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
  })
}

/**
 * Hook to get inquiry activity
 */
export const useInquiryActivity = (inquiryId: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.inquiries.detail(`${inquiryId}-activity`),
    queryFn: () => inquiryService.getInquiryActivity(inquiryId),
    enabled: !!inquiryId,
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  })
}

/**
 * Hook to get inquiry comments
 */
export const useInquiryComments = (inquiryId: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.inquiries.detail(`${inquiryId}-comments`),
    queryFn: () => inquiryService.getInquiryComments(inquiryId),
    enabled: !!inquiryId,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

/**
 * Hook to search inquiries
 */
export const useSearchInquiries = (
  query: string,
  filters?: Partial<InquiryListParams>
) => {
  return useQuery({
    queryKey: QUERY_KEYS.inquiries.list({ search: query, ...filters }),
    queryFn: () => inquiryService.searchInquiries(query, filters),
    enabled: !!query && query.length >= 2,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  })
}

// MUTATION HOOKS

/**
 * Mutation hook to create a new inquiry
 */
export const useCreateInquiry = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: [MUTATION_KEYS.CREATE_INQUIRY],
    mutationFn: (data: CreateInquiryRequest) => {
      // Validate data before submission
      const errors = inquiryService.validateInquiryData(data)
      if (errors.length > 0) {
        throw new Error(`Validation failed: ${errors.join(', ')}`)
      }
      return inquiryService.createInquiry(data)
    },
    onSuccess: newInquiry => {
      // Invalidate and refetch inquiries lists
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.inquiries.lists(),
      })

      // Add the new inquiry to the cache
      queryClient.setQueryData(
        QUERY_KEYS.inquiries.detail(newInquiry.id),
        newInquiry
      )

      toast.success('Inquiry created successfully')
    },
    onError: (error: any) => {
      console.error('Failed to create inquiry:', error)
      toast.error(error.message || 'Failed to create inquiry')
    },
  })
}

/**
 * Mutation hook to update an inquiry
 */
export const useUpdateInquiry = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: [MUTATION_KEYS.UPDATE_INQUIRY],
    mutationFn: ({
      inquiryId,
      data,
    }: {
      inquiryId: string
      data: UpdateInquiryRequest
    }) => {
      // Validate data before submission
      const errors = inquiryService.validateInquiryData(data)
      if (errors.length > 0) {
        throw new Error(`Validation failed: ${errors.join(', ')}`)
      }
      return inquiryService.updateInquiry(inquiryId, data)
    },
    onSuccess: (updatedInquiry, { inquiryId }) => {
      // Update the specific inquiry in cache
      queryClient.setQueryData(
        QUERY_KEYS.inquiries.detail(inquiryId),
        updatedInquiry
      )

      // Invalidate inquiries lists
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.inquiries.lists(),
      })

      toast.success('Inquiry updated successfully')
    },
    onError: (error: any) => {
      console.error('Failed to update inquiry:', error)
      toast.error(error.message || 'Failed to update inquiry')
    },
  })
}

/**
 * Mutation hook to assign an inquiry
 */
export const useAssignInquiry = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ['assignInquiry'],
    mutationFn: ({
      inquiryId,
      data,
    }: {
      inquiryId: string
      data: AssignInquiryRequest
    }) => inquiryService.assignInquiry(inquiryId, data),
    onSuccess: (assignedInquiry, { inquiryId }) => {
      // Update the specific inquiry in cache
      queryClient.setQueryData(
        QUERY_KEYS.inquiries.detail(inquiryId),
        assignedInquiry
      )

      // Invalidate inquiries lists
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.inquiries.lists(),
      })

      // Invalidate activity
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.inquiries.detail(`${inquiryId}-activity`),
      })

      toast.success('Inquiry assigned successfully')
    },
    onError: (error: any) => {
      console.error('Failed to assign inquiry:', error)
      toast.error(error.message || 'Failed to assign inquiry')
    },
  })
}

/**
 * Mutation hook to resolve an inquiry
 */
export const useResolveInquiry = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ['resolveInquiry'],
    mutationFn: ({
      inquiryId,
      data,
    }: {
      inquiryId: string
      data: ResolveInquiryRequest
    }) => inquiryService.resolveInquiry(inquiryId, data),
    onSuccess: (resolvedInquiry, { inquiryId }) => {
      // Update the specific inquiry in cache
      queryClient.setQueryData(
        QUERY_KEYS.inquiries.detail(inquiryId),
        resolvedInquiry
      )

      // Invalidate inquiries lists
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.inquiries.lists(),
      })

      // Invalidate activity
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.inquiries.detail(`${inquiryId}-activity`),
      })

      // Invalidate stats
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.inquiries.list({ stats: true }),
      })

      toast.success('Inquiry resolved successfully')
    },
    onError: (error: any) => {
      console.error('Failed to resolve inquiry:', error)
      toast.error(error.message || 'Failed to resolve inquiry')
    },
  })
}

/**
 * Mutation hook to close an inquiry
 */
export const useCloseInquiry = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ['closeInquiry'],
    mutationFn: ({ inquiryId, notes }: { inquiryId: string; notes?: string }) =>
      inquiryService.closeInquiry(inquiryId, notes),
    onSuccess: (closedInquiry, { inquiryId }) => {
      // Update the specific inquiry in cache
      queryClient.setQueryData(
        QUERY_KEYS.inquiries.detail(inquiryId),
        closedInquiry
      )

      // Invalidate inquiries lists
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.inquiries.lists(),
      })

      // Invalidate activity
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.inquiries.detail(`${inquiryId}-activity`),
      })

      toast.success('Inquiry closed successfully')
    },
    onError: (error: any) => {
      console.error('Failed to close inquiry:', error)
      toast.error(error.message || 'Failed to close inquiry')
    },
  })
}

/**
 * Mutation hook to add a comment to an inquiry
 */
export const useAddInquiryComment = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ['addInquiryComment'],
    mutationFn: ({
      inquiryId,
      content,
      attachments,
    }: {
      inquiryId: string
      content: string
      attachments?: File[]
    }) => inquiryService.addInquiryComment(inquiryId, content, attachments),
    onSuccess: (_, { inquiryId }) => {
      // Invalidate comments to refetch
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.inquiries.detail(`${inquiryId}-comments`),
      })

      // Invalidate activity
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.inquiries.detail(`${inquiryId}-activity`),
      })

      toast.success('Comment added successfully')
    },
    onError: (error: any) => {
      console.error('Failed to add comment:', error)
      toast.error(error.message || 'Failed to add comment')
    },
  })
}
