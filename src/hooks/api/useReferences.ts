/**
 * References API Hooks
 *
 * TanStack Query hooks for reference data operations
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import { referenceService } from '@/api/services/referenceService'
import { QUERY_KEYS } from '@/config/api'
import {
  ReferenceCategory,
  ReferenceSearchParams,
  DiagnosisRecommendationRequest,
} from '@/types/api/references'

/**
 * Hook to get all reference data
 */
export const useReferences = () => {
  return useQuery({
    queryKey: QUERY_KEYS.references.all,
    queryFn: () => referenceService.getCachedReferences(),
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
    retry: 2,
  })
}

/**
 * Hook to get references by category
 */
export const useReferencesByCategory = <T = any>(
  category: ReferenceCategory
) => {
  return useQuery({
    queryKey: QUERY_KEYS.references.list({ category }),
    queryFn: () => referenceService.getCachedReferenceCategory<T>(category),
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
  })
}

/**
 * Hook to get incident types
 */
export const useIncidentTypes = () => {
  return useQuery({
    queryKey: QUERY_KEYS.references.list({ category: 'incident_types' }),
    queryFn: () => referenceService.getIncidentTypes(),
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
  })
}

/**
 * Hook to get provisional diagnoses
 */
export const useProvisionalDiagnoses = () => {
  return useQuery({
    queryKey: QUERY_KEYS.references.list({ category: 'provisional_diagnosis' }),
    queryFn: () => referenceService.getProvisionalDiagnoses(),
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
  })
}

/**
 * Hook to get actions taken
 */
export const useActionsTaken = () => {
  return useQuery({
    queryKey: QUERY_KEYS.references.list({ category: 'actions_taken' }),
    queryFn: () => referenceService.getActionsTaken(),
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
  })
}

/**
 * Hook to get probable causes for a diagnosis
 */
export const useProbableCauses = (provisionalDiagnosisCode: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.references.list({
      category: 'probable_causes',
      provisionalDiagnosisCode,
    }),
    queryFn: () => referenceService.getProbableCauses(provisionalDiagnosisCode),
    enabled: !!provisionalDiagnosisCode,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 20 * 60 * 1000, // 20 minutes
    retry: 2,
  })
}

/**
 * Hook to get reference statistics
 */
export const useReferenceStats = () => {
  return useQuery({
    queryKey: QUERY_KEYS.references.list({ stats: true }),
    queryFn: () => referenceService.getReferenceStats(),
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
  })
}

/**
 * Hook to get a specific reference item
 */
export const useReferenceItem = (id: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.references.detail(id),
    queryFn: () => referenceService.getReferenceItem(id),
    enabled: !!id,
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
  })
}

/**
 * Hook to search references
 */
export const useSearchReferences = (params: ReferenceSearchParams) => {
  return useQuery({
    queryKey: QUERY_KEYS.references.list({ search: true, ...params }),
    queryFn: () => referenceService.searchReferences(params),
    enabled: !!(params.query && params.query.length >= 2),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
  })
}

// MUTATION HOOKS

/**
 * Mutation hook to get diagnosis recommendations
 */
export const useDiagnosisRecommendations = () => {
  return useMutation({
    mutationKey: ['getDiagnosisRecommendations'],
    mutationFn: (request: DiagnosisRecommendationRequest) =>
      referenceService.getDiagnosisRecommendations(request),
    onError: (error: any) => {
      console.error('Failed to get diagnosis recommendations:', error)
      toast.error(error.message || 'Failed to get diagnosis recommendations')
    },
  })
}

/**
 * Hook to clear reference cache
 */
export const useClearReferenceCache = () => {
  const queryClient = useQueryClient()

  return () => {
    // Clear React Query cache
    queryClient.invalidateQueries({
      queryKey: QUERY_KEYS.references.all,
    })

    // Clear service cache
    referenceService.clearCache()

    console.log('Reference cache cleared')
    toast.success('Reference data refreshed')
  }
}

/**
 * Hook to refresh all reference data
 */
export const useRefreshReferences = () => {
  const queryClient = useQueryClient()
  const clearCache = useClearReferenceCache()

  return useMutation({
    mutationKey: ['refreshReferences'],
    mutationFn: async () => {
      // Clear cache first
      clearCache()

      // Fetch fresh data
      return referenceService.getAllReferences()
    },
    onSuccess: () => {
      // Invalidate all reference queries to trigger refetch
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.references.all,
      })

      toast.success('Reference data updated successfully')
    },
    onError: (error: any) => {
      console.error('Failed to refresh reference data:', error)
      toast.error(error.message || 'Failed to refresh reference data')
    },
  })
}

// UTILITY HOOKS

/**
 * Hook to find reference by code
 */
export const useFindReferenceByCode = (
  category: ReferenceCategory,
  code: string
) => {
  const { data: references } = useReferences()

  return {
    reference: references
      ? referenceService.findReferenceByCode(references, category, code)
      : null,
    isLoading: !references,
  }
}

/**
 * Hook to get multiple reference categories at once
export const useMultipleReferenceCategories = (
  categories: ReferenceCategory[]
) => {
  // This would require useQueries from TanStack Query
  // For now, we'll return individual hooks
  return categories.reduce(
    (acc, category) => {
      acc[category] = useReferencesByCategory(category)
      return acc
    },
    {} as Record<ReferenceCategory, any>
  )
}
 */

/**
 * Hook to validate reference data
 */
export const useValidateReferences = () => {
  const { data: references, isLoading, error } = useReferences()

  return {
    isValid: references
      ? referenceService.validateReferenceData(references)
      : false,
    isLoading,
    error,
    references,
  }
}

/**
 * Hook for commonly used reference combinations
 */
export const useCommonReferences = () => {
  const incidentTypes = useIncidentTypes()
  const provisionalDiagnoses = useProvisionalDiagnoses()
  const actionsTaken = useActionsTaken()

  return {
    incidentTypes: incidentTypes.data || [],
    provisionalDiagnoses: provisionalDiagnoses.data || [],
    actionsTaken: actionsTaken.data || [],
    isLoading:
      incidentTypes.isLoading ||
      provisionalDiagnoses.isLoading ||
      actionsTaken.isLoading,
    error:
      incidentTypes.error || provisionalDiagnoses.error || actionsTaken.error,
  }
}
