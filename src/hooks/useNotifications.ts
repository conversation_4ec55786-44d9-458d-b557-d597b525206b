import { useEffect, useState, useCallback } from 'react'
import { useAuthStore } from '@/store/authStore'
import { notificationService } from '@/services/notificationService'
import { addBreadcrumb, captureException } from '@/config/sentry'

/**
 * Custom hook for managing push notifications in the ERA Dispatch Application
 *
 * This hook integrates notification management with the authentication system,
 * handling FCM token registration, permission management, and cleanup.
 */

export interface NotificationState {
  isSupported: boolean
  permission: NotificationPermission
  isEnabled: boolean
  token: string | null
  isLoading: boolean
  error: string | null
}

export function useNotifications() {
  const { user, isAuthenticated } = useAuthStore()
  const [state, setState] = useState<NotificationState>({
    isSupported: false,
    permission: 'default',
    isEnabled: false,
    token: null,
    isLoading: false,
    error: null,
  })

  // Initialize notification service when component mounts
  useEffect(() => {
    initializeNotifications()
  }, [])

  // Handle authentication changes
  useEffect(() => {
    if (isAuthenticated && user) {
      handleUserAuthenticated()
    } else {
      handleUserSignedOut()
    }
  }, [isAuthenticated, user])

  /**
   * Initialize notification service
   */
  const initializeNotifications = async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }))

      // Initialize the notification service
      await notificationService.initialize()

      // Update state with current status
      updateNotificationState()

      addBreadcrumb('Notifications hook initialized', 'notification', 'info')
    } catch (error) {
      console.error('Error initializing notifications:', error)
      setState(prev => ({
        ...prev,
        error: 'Failed to initialize notifications',
        isLoading: false,
      }))

      captureException(error as Error, {
        context: 'notifications_hook_initialization',
      })
    }
  }

  /**
   * Update notification state
   */
  const updateNotificationState = () => {
    const isSupported = 'Notification' in window && 'serviceWorker' in navigator
    const permission = notificationService.getPermissionStatus()
    const isEnabled = notificationService.isNotificationEnabled()
    const storedToken = notificationService.getStoredToken()

    setState(prev => ({
      ...prev,
      isSupported,
      permission,
      isEnabled,
      token: storedToken?.token || null,
      isLoading: false,
      error: null,
    }))
  }

  /**
   * Handle user authentication
   */
  const handleUserAuthenticated = async () => {
    try {
      if (!user) return

      addBreadcrumb(
        'User authenticated - checking notification token',
        'notification',
        'info',
        {
          userId: user.sub,
        }
      )

      // Check if we have a stored token
      const storedToken = notificationService.getStoredToken()

      if (storedToken && storedToken.userId === user.sub) {
        // Token exists for this user
        console.log('Existing FCM token found for user:', user.sub)
        setState(prev => ({ ...prev, token: storedToken.token }))
      } else if (state.permission === 'granted') {
        // Permission granted but no token for this user - get new token
        await requestNotificationPermission()
      }
    } catch (error) {
      console.error(
        'Error handling user authentication for notifications:',
        error
      )
      captureException(error as Error, {
        context: 'notification_user_authentication',
        userId: user?.sub,
      })
    }
  }

  /**
   * Handle user sign out
   */
  const handleUserSignedOut = async () => {
    try {
      addBreadcrumb(
        'User signed out - cleaning up notifications',
        'notification',
        'info'
      )

      // Clean up notification token
      await notificationService.deleteToken()

      setState(prev => ({
        ...prev,
        token: null,
      }))
    } catch (error) {
      console.error('Error cleaning up notifications on sign out:', error)
      captureException(error as Error, {
        context: 'notification_user_signout',
      })
    }
  }

  /**
   * Request notification permission and get token
   */
  const requestNotificationPermission =
    useCallback(async (): Promise<boolean> => {
      try {
        setState(prev => ({ ...prev, isLoading: true, error: null }))

        const token = await notificationService.requestPermissionAndGetToken(
          user?.sub
        )

        if (token) {
          setState(prev => ({
            ...prev,
            permission: 'granted',
            isEnabled: true,
            token,
            isLoading: false,
          }))

          addBreadcrumb(
            'Notification permission granted',
            'notification',
            'info',
            {
              userId: user?.sub,
            }
          )

          // Here you would typically send the token to your backend
          // await sendTokenToBackend(token, user?.sub)

          return true
        } else {
          setState(prev => ({
            ...prev,
            permission: notificationService.getPermissionStatus(),
            isEnabled: false,
            isLoading: false,
          }))

          return false
        }
      } catch (error) {
        console.error('Error requesting notification permission:', error)
        setState(prev => ({
          ...prev,
          error: 'Failed to enable notifications',
          isLoading: false,
        }))

        captureException(error as Error, {
          context: 'notification_permission_request',
          userId: user?.sub,
        })

        return false
      }
    }, [user?.sub])

  /**
   * Disable notifications
   */
  const disableNotifications = useCallback(async (): Promise<void> => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }))

      await notificationService.deleteToken()

      setState(prev => ({
        ...prev,
        token: null,
        isEnabled: false,
        isLoading: false,
      }))

      addBreadcrumb('Notifications disabled', 'notification', 'info', {
        userId: user?.sub,
      })
    } catch (error) {
      console.error('Error disabling notifications:', error)
      setState(prev => ({
        ...prev,
        error: 'Failed to disable notifications',
        isLoading: false,
      }))

      captureException(error as Error, {
        context: 'notification_disable',
        userId: user?.sub,
      })
    }
  }, [user?.sub])

  /**
   * Refresh notification state
   */
  const refreshState = useCallback(() => {
    updateNotificationState()
  }, [])

  /**
   * Check if notifications should be shown (banner)
   */
  const shouldShowPermissionBanner = useCallback((): boolean => {
    return (
      state.isSupported &&
      isAuthenticated &&
      state.permission === 'default' &&
      !state.token
    )
  }, [state.isSupported, isAuthenticated, state.permission, state.token])

  return {
    // State
    ...state,

    // Actions
    requestPermission: requestNotificationPermission,
    disableNotifications,
    refreshState,

    // Helpers
    shouldShowPermissionBanner,
  }
}

export default useNotifications
