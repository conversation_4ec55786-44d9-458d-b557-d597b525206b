/**
 * Dispatcher Provider
 *
 * Provider component that handles automatic dispatcher data fetching
 * and Firebase token registration when user logs in
 */

import React, { createContext, useContext, useEffect, useState } from 'react'
import { useAuthStore } from '@/store/authStore'
import { useAutoFetchDispatcherData } from '@/hooks/api/useAuth'
import { useRegisterFirebaseToken } from '@/hooks/api/useNotifications'
import {
  handleGetFirebaseToken,
  requestNotificationPermission,
} from '@/utils/firebaseUtils'
import { toast } from 'react-hot-toast'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'

// Dispatcher Context Types
interface DispatcherData {
  dispatcher_id: string
  email: string
  first_name: string
  last_name: string
  profile_image_url?: string
  precinct_id: string
  precinct_name: string
  firebasePermissionToken?: string
}

interface DispatcherContextType {
  dispatcher: DispatcherData | null
  isLoading: boolean
  error: string | null
  updateFirebaseToken: (token: string) => void
  refreshDispatcherData: () => void
}

// Create Context
const DispatcherContext = createContext<DispatcherContextType | undefined>(
  undefined
)

// Custom hook to use dispatcher context
export const useDispatcher = (): DispatcherContextType => {
  const context = useContext(DispatcherContext)
  if (context === undefined) {
    throw new Error('useDispatcher must be used within a DispatcherProvider')
  }
  return context
}

interface DispatcherProviderProps {
  children: React.ReactNode
}

export const DispatcherProvider: React.FC<DispatcherProviderProps> = ({
  children,
}) => {
  const { user, isAuthenticated } = useAuthStore()
  const registerFirebaseToken = useRegisterFirebaseToken()

  // Local state for dispatcher data and Firebase token
  const [dispatcher, setDispatcher] = useState<DispatcherData | null>(null)
  const [firebaseToken, setFirebaseToken] = useState<string | null>(null)

  // Automatically fetch dispatcher data when user logs in
  const {
    data: dispatcherApiData,
    isLoading: isLoadingDispatcher,
    error: dispatcherError,
    refetch: refetchDispatcherData,
  } = useAutoFetchDispatcherData()

  // Update dispatcher state when API data changes
  useEffect(() => {
    if (dispatcherApiData && isAuthenticated) {
      const newDispatcherData: DispatcherData = {
        dispatcher_id: dispatcherApiData.dispatcher_id,
        email: dispatcherApiData.email,
        first_name: dispatcherApiData.first_name,
        last_name: dispatcherApiData.last_name,
        profile_image_url: dispatcherApiData.profile_image_url,
        precinct_id: dispatcherApiData.precinct_id,
        precinct_name: dispatcherApiData.precinct_name,
        firebasePermissionToken: firebaseToken || undefined,
      }
      setDispatcher(newDispatcherData)
    } else if (!isAuthenticated) {
      // Clear dispatcher data when user logs out
      setDispatcher(null)
      setFirebaseToken(null)
    }
  }, [dispatcherApiData, isAuthenticated, firebaseToken])

  // Handle Firebase token registration
  useEffect(() => {
    const handleFirebaseTokenRegistration = async () => {
      try {
        // Only proceed if user is authenticated and we have user data
        if (!isAuthenticated || !user?.sub) {
          return
        }

        // Check if notification permission is granted
        if (
          'Notification' in window &&
          window.Notification?.permission !== 'granted'
        ) {
          toast(
            t => (
              <span>
                Please enable notifications for full functionality. <br />
                <button
                  onClick={async () => {
                    toast.dismiss(t.id)
                    const granted = await requestNotificationPermission()
                    if (granted) {
                      toast.success('Notifications enabled!')
                      // Retry registration after permission granted
                      handleFirebaseTokenRegistration()
                    } else {
                      toast.error('Notifications permission denied.')
                    }
                  }}
                  style={{
                    color: '#2563eb',
                    textDecoration: 'underline',
                    background: 'none',
                    border: 'none',
                    cursor: 'pointer',
                  }}
                >
                  Enable Now
                </button>
              </span>
            ),
            { duration: 10000 }
          )
          return
        }

        if (window.Notification?.permission === 'granted') {
          // If no cached token in state, generate and register new one
          if (!firebaseToken) {
            const token = await handleGetFirebaseToken()
            if (token) {
              setFirebaseToken(token)
              registerFirebaseToken.mutate({
                entity: 'dispatchers',
                token,
                id: user.sub,
              })
            }
          }
        }
      } catch (error) {
        console.error('Failed to handle Firebase token registration:', error)
      }
    }

    // Only run after dispatcher data is loaded to ensure we have all user info
    if (isAuthenticated && user?.sub && !isLoadingDispatcher) {
      handleFirebaseTokenRegistration()
    }
  }, [
    isAuthenticated,
    user?.sub,
    isLoadingDispatcher,
    registerFirebaseToken,
    firebaseToken,
  ])

  // Handle dispatcher data loading errors
  useEffect(() => {
    if (dispatcherError) {
      console.error('Failed to load dispatcher data:', dispatcherError)
      toast.error(
        'Failed to load user profile. Please try refreshing the page.'
      )
    }
  }, [dispatcherError])

  // Context methods
  const updateFirebaseToken = (token: string) => {
    setFirebaseToken(token)
  }

  const refreshDispatcherData = () => {
    refetchDispatcherData()
  }

  // Context value
  const contextValue: DispatcherContextType = {
    dispatcher,
    isLoading: isLoadingDispatcher,
    error: dispatcherError?.message || null,
    updateFirebaseToken,
    refreshDispatcherData,
  }

  // Show loading spinner if user is authenticated but dispatcher data is not yet loaded
  if (isAuthenticated && isLoadingDispatcher) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner />
      </div>
    )
  }

  // Show loading spinner if user is authenticated but dispatcher data is missing
  if (isAuthenticated && !dispatcher && !dispatcherError) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner />
      </div>
    )
  }

  return (
    <DispatcherContext.Provider value={contextValue}>
      {children}
    </DispatcherContext.Provider>
  )
}

export default DispatcherProvider
