import { ReactNode } from 'react'
import { ConfigProvider } from 'antd'
import { MantineProvider } from '@mantine/core'
import { Notifications } from '@mantine/notifications'
import { ModalsProvider } from '@mantine/modals'
import { antdTheme } from '@/config/theme'
import { mantineTheme } from '@/config/mantineTheme'

// Import Mantine styles
import '@mantine/core/styles.css'
import '@mantine/notifications/styles.css'
import '@mantine/dates/styles.css'
import '@mantine/dropzone/styles.css'
import '@mantine/spotlight/styles.css'

interface UIProviderProps {
  children: ReactNode
}

/**
 * Combined UI Provider that configures both Ant Design and Mantine
 * This ensures both libraries work together without conflicts
 */
export function UIProvider({ children }: UIProviderProps) {
  return (
    <ConfigProvider theme={antdTheme}>
      <MantineProvider theme={mantineTheme}>
        <ModalsProvider>
          <Notifications position="top-right" />
          {children}
        </ModalsProvider>
      </MantineProvider>
    </ConfigProvider>
  )
}

/**
 * Example usage of both libraries together:
 *
 * // Ant Design components
 * import { Button as AntButton, Table, Form } from 'antd'
 *
 * // Mantine components
 * import { Button as MantineButton, Card, Modal } from '@mantine/core'
 *
 * function ExampleComponent() {
 *   return (
 *     <div>
 *       <AntButton type="primary">Ant Design Button</AntButton>
 *       <MantineButton color="primary">Mantine Button</MantineButton>
 *
 *       <Card>
 *         <Table dataSource={data} columns={columns} />
 *       </Card>
 *     </div>
 *   )
 * }
 *
 * Best Practices:
 * 1. Use Ant Design for complex data components (Tables, Forms, DatePickers)
 * 2. Use Mantine for modern UI components (Cards, Modals, Notifications)
 * 3. Prefer consistent styling by using the same color palette
 * 4. Import components with aliases when names conflict (Button as AntButton)
 */
