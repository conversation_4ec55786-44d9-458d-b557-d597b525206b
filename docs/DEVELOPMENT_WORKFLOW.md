# Development Workflow Guide

This document outlines the development workflow, code quality standards, and tooling setup for the ERA Dispatch Application.

## Code Quality Tools

### ESLint Configuration
- **File**: `.eslintrc.cjs`
- **Purpose**: Static code analysis and error detection
- **Rules**: TypeScript-specific rules, React best practices, and custom project rules
- **Usage**: 
  ```bash
  npm run lint          # Check for linting errors
  npm run lint:fix      # Auto-fix linting errors
  ```

### Prettier Configuration
- **File**: `.prettierrc`
- **Purpose**: Code formatting and style consistency
- **Settings**: Single quotes, no semicolons, 80 character line width
- **Usage**:
  ```bash
  npm run format        # Format all files
  npm run format:check  # Check formatting without changes
  ```

### TypeScript Configuration
- **File**: `tsconfig.json`
- **Purpose**: Type checking and compilation settings
- **Usage**:
  ```bash
  npm run type-check    # Run TypeScript type checking
  ```

## Pre-commit Hooks

### Husky Setup
- **Directory**: `.husky/`
- **Purpose**: Git hooks management
- **Hook**: `pre-commit` - Runs before each commit

### Lint-staged Configuration
- **File**: `.lintstagedrc.json`
- **Purpose**: Run linting and formatting only on staged files
- **Configuration**:
  ```json
  {
    "*.{ts,tsx}": ["eslint --fix", "prettier --write"],
    "*.{js,jsx,json,css,md}": ["prettier --write"]
  }
  ```

### Pre-commit Workflow
1. Developer runs `git commit`
2. Husky triggers pre-commit hook
3. lint-staged runs on staged files:
   - ESLint fixes TypeScript/React files
   - Prettier formats all supported files
4. If no errors, commit proceeds
5. If errors exist, commit is blocked

## Font and Typography System

### Google Fonts Integration
- **Manrope**: Headings (h1-h6)
- **Poppins**: Body text and general UI
- **Inter**: Sparingly used for specific elements

### Usage in Components
```tsx
// Automatic via CSS
<h1>This uses Manrope</h1>
<p>This uses Poppins</p>

// Manual via utility classes
<div className="font-heading">Manrope font</div>
<div className="font-body">Poppins font</div>
<div className="font-inter">Inter font</div>
```

## UI Library Integration

### Ant Design + Mantine Setup
- **Provider**: `UIProvider` wraps both libraries
- **Usage**: Import components with aliases when names conflict
- **Example**:
  ```tsx
  import { Button as AntButton } from 'antd'
  import { Button as MantineButton } from '@mantine/core'
  ```

### Best Practices
1. Use Ant Design for complex data components (Tables, Forms)
2. Use Mantine for modern UI components (Cards, Modals)
3. Maintain consistent color palette across both libraries

## Color System

### Custom Color Palette
All colors are defined in:
- **Tailwind**: `src/styles/globals.css` (CSS custom properties)
- **TypeScript**: `src/utils/colors.ts` (constants and utilities)
- **Mantine**: `src/config/mantineTheme.ts` (theme integration)

### Usage Examples
```tsx
// Tailwind classes
<div className="bg-primary text-white">Primary background</div>

// TypeScript constants
import { colors } from '@/utils/colors'
const primaryColor = colors.primary // '#254769'

// Inline styles
<div style={{ backgroundColor: colors.secondary }}>
```

## Asset Management

### Optimized Asset System
- **File**: `src/utils/assets.ts`
- **Features**: 
  - Centralized asset registry
  - Performance optimization utilities
  - TypeScript support
  - Lazy loading support

### Usage Example
```tsx
import { assets, assetUtils } from '@/utils/assets'

// Optimized image props
<img {...assetUtils.getImageProps(assets.logos.era)} />

// Background images
<div style={assetUtils.getBackgroundImageCSS(assets.backgrounds.hospital)} />
```

## Development Commands

### Essential Commands
```bash
# Development
npm run dev              # Start development server
npm run dev:check        # Start dev server with TypeScript watching
npm run build           # Build for production
npm run preview         # Preview production build

# Code Quality
npm run lint            # Check linting errors
npm run lint:fix        # Fix linting errors
npm run format          # Format code
npm run format:check    # Check formatting
npm run type-check      # TypeScript checking
npm run type-check:watch # TypeScript checking in watch mode

# Comprehensive Checks
npm run check-all       # Run all checks (TypeScript, ESLint, Prettier)
npm run fix-all         # Fix all auto-fixable issues

# Testing
npm test               # Run tests
npm run test:ui        # Run tests with UI
npm run coverage       # Generate coverage report
```

### Recommended Development Workflow
```bash
# 1. Start development with error checking
npm run dev:check

# 2. Before committing, run comprehensive checks
npm run check-all

# 3. Fix any issues automatically
npm run fix-all

# 4. Commit (pre-commit hooks will run automatically)
git commit -m "Your commit message"
```

### Build Analysis
```bash
npm run build:analyze  # Analyze bundle size
```

## Git Workflow

### Commit Process
1. Make changes to code
2. Stage files: `git add .`
3. Commit: `git commit -m "message"`
4. Pre-commit hooks run automatically
5. If successful, commit is created

### Commit Message Guidelines
- Use present tense ("Add feature" not "Added feature")
- Keep first line under 50 characters
- Reference issues when applicable
- Use conventional commit format when possible

## Error Detection and Prevention

### Silent Import Error Prevention

The application includes enhanced error detection to prevent silent import failures:

#### 1. Enhanced ESLint Configuration
- **Import validation**: Detects invalid imports and module resolution errors
- **TypeScript integration**: Catches type-related import issues
- **Real-time checking**: Errors show up immediately in IDE

#### 2. Development Error Monitoring
- **Global error handler**: Catches unhandled errors and promise rejections
- **Module loading detection**: Monitors for chunk loading failures
- **Enhanced error boundaries**: Better error reporting with stack traces

#### 3. TypeScript Watch Mode
```bash
# Run TypeScript checking in watch mode during development
npm run dev:check
```

#### 4. Common Import Error Patterns to Avoid

**❌ Mixed Import Patterns:**
```typescript
// WRONG - Mixing relative and absolute imports
import { Component } from '../components/Component'
import { utils } from '@/utils/helpers'
```

**✅ Consistent Import Patterns:**
```typescript
// CORRECT - Use consistent absolute imports
import { Component } from '@/components/Component'
import { utils } from '@/utils/helpers'
```

### Error Detection Tools

#### Browser Console Monitoring
- Open Developer Tools (F12)
- Check Console tab for errors
- Look for red error messages
- Check Network tab for failed module loads

#### IDE Error Detection
- TypeScript errors show as red underlines
- ESLint errors show as yellow/red squiggles
- Import errors are highlighted immediately

#### Command Line Checking
```bash
# Check for TypeScript errors
npm run type-check

# Check for ESLint errors
npm run lint

# Check for formatting issues
npm run format:check

# Run all checks at once
npm run check-all
```

## Troubleshooting

### Silent Import Failures
If you encounter a blank screen or silent failures:

1. **Check Browser Console**: Look for import/module errors
2. **Run TypeScript Check**: `npm run type-check`
3. **Check ESLint**: `npm run lint`
4. **Verify Imports**: Ensure all imports are from correct packages
5. **Clear Cache**: Delete `node_modules/.cache` and restart

### Pre-commit Hook Issues
```bash
# If hooks don't run
chmod +x .husky/pre-commit

# If lint-staged fails
npx lint-staged --debug

# Reset hooks
npm run prepare
```

### ESLint Issues
```bash
# Clear ESLint cache
npx eslint --cache-location .eslintcache --cache

# Check specific files
npx eslint src/path/to/file.ts
```

### TypeScript Issues
```bash
# Clear TypeScript cache
rm -rf node_modules/.cache
npm run type-check
```

## IDE Setup Recommendations

### VS Code Extensions
- ESLint
- Prettier
- TypeScript and JavaScript Language Features
- Tailwind CSS IntelliSense
- Auto Rename Tag
- Bracket Pair Colorizer

### Settings
```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative"
}
```

## Performance Considerations

### Asset Optimization
- Use `?url` imports for explicit asset URLs
- Implement lazy loading for non-critical images
- Preload critical assets in components

### Code Splitting
- Use React.lazy() for route-based splitting
- Implement component-level splitting for large components
- Monitor bundle size with build analyzer

### Font Loading
- Fonts are loaded via Google Fonts CDN
- `font-display: swap` for better performance
- Preload critical fonts when needed

This workflow ensures consistent code quality, optimal performance, and maintainable codebase across the development team.
