# Sentry Integration Guide for ERA Dispatch Application

This document provides information about the streamlined Sentry integration implemented in the ERA Dispatch Application for error tracking.

## Overview

The Sentry integration provides:
- **Error Tracking**: Comprehensive error reporting with context
- **User Context**: Authentication-aware error reporting
- **React Error Boundaries**: Enhanced error boundaries with Sentry reporting
- **Global Error Handler**: Integration with the existing global error handler
- **Route Error Tracking**: Automatic reporting of routing errors

## Setup and Configuration

### 1. Environment Variables

Add the following environment variable to your `.env` file:

```bash
# Sentry Configuration
VITE_SENTRY_DSN=your_sentry_dsn_here
```

### 2. Features

#### Error Boundaries

Two error boundaries are enhanced with Sentry reporting:

1. **ErrorBoundary** (`src/components/ui/ErrorBoundary.tsx`)
   - Catches React component errors
   - Reports to Sentry with component stack traces
   - Provides user-friendly error UI

2. **RouteErrorBoundary** (`src/components/auth/RouteErrorBoundary.tsx`)
   - Catches routing errors
   - Reports navigation-related issues
   - Includes route context in error reports

#### Global Error Handler

The global error handler (`src/utils/errorHandling.ts`) is integrated with Sentry to:
- Capture unhandled JavaScript errors
- Track promise rejections
- Report network errors
- Filter out non-actionable errors

#### User Context Integration

Authentication integration provides:
- User identification in error reports
- Authentication state tracking
- User-specific error context
- Automatic context clearing on logout

## Usage Examples

### Basic Error Reporting
```typescript
import { captureException } from '@/config/sentry'

try {
  // Some operation
} catch (error) {
  captureException(error, {
    operation: 'order_processing',
    orderId: '12345',
    context: 'additional context'
  })
}
```

### Adding Breadcrumbs
```typescript
import { addBreadcrumb } from '@/config/sentry'

addBreadcrumb(
  'User clicked order button',
  'user',
  'info',
  { orderId: '12345', buttonType: 'primary' }
)
```

### Setting User Context
```typescript
import { setSentryUser } from '@/config/sentry'

setSentryUser({
  id: 'user-123',
  email: '<EMAIL>',
  username: '<EMAIL>'
})
```

### Clearing User Context
```typescript
import { clearSentryUser } from '@/config/sentry'

clearSentryUser()
```

## Configuration Details

The Sentry configuration includes:

- **Environment Detection**: Automatically detects development vs production
- **Performance Monitoring**: Basic performance tracking with appropriate sampling rates
- **React Router Integration**: Automatic route change tracking
- **Error Filtering**: Filters out common non-actionable errors
- **User Context**: Automatic user context management through authentication

## File Structure

```
src/
├── config/
│   └── sentry.ts              # Main Sentry configuration
├── utils/
│   └── errorHandling.ts       # Global error handler with Sentry
├── hooks/
│   └── useAuth.ts            # Auth hook with Sentry user context
├── store/
│   └── authStore.ts          # Auth store with Sentry integration
└── components/
    ├── ui/ErrorBoundary.tsx  # Enhanced error boundary
    └── auth/RouteErrorBoundary.tsx  # Route error boundary
```

## Best Practices

### Error Reporting
- Use `captureException` for manual error reporting
- Include relevant context (orderId, userId, etc.)
- Avoid reporting expected errors (validation, 4xx responses)

### Breadcrumbs
- Add breadcrumbs for user interactions
- Include navigation events
- Track form submissions and important actions

### Context Management
- Set user context after authentication
- Clear context on logout
- Include operation-specific context for errors

## Troubleshooting

### Common Issues

1. **Sentry not initializing**
   - Check DSN configuration in environment variables
   - Verify console for initialization messages

2. **No errors being reported**
   - Ensure DSN is correctly set
   - Check browser network tab for Sentry requests
   - Verify error is not being filtered out

3. **Missing user context**
   - Ensure `setSentryUser` is called after authentication
   - Check that `clearSentryUser` is called on logout

### Debug Mode

In development, Sentry runs with debug mode enabled, providing:
- Console logging of Sentry events
- Higher sampling rates
- Detailed error information

## Additional Resources

- [Sentry React Documentation](https://docs.sentry.io/platforms/javascript/guides/react/)
- [Error Tracking Best Practices](https://docs.sentry.io/product/issues/)
- [Performance Monitoring Guide](https://docs.sentry.io/product/performance/)
