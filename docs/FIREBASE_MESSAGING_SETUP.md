# Firebase Cloud Messaging (FCM) Setup Guide

This document provides comprehensive information about the Firebase Cloud Messaging integration implemented in the ERA Dispatch Application for push notifications.

## Overview

The FCM integration provides:
- **Foreground Notifications**: Toast notifications using react-hot-toast when app is active
- **Background Notifications**: System push notifications when app is closed/backgrounded
- **Permission Management**: User-friendly permission request system
- **Authentication Integration**: FCM token management tied to user authentication
- **Error Tracking**: Comprehensive error handling with Sentry integration

## Setup and Configuration

### 1. Environment Variables

Add the following environment variables to your `.env` file:

```bash
# Firebase Configuration
VITE_FIREBASE_API_KEY=your_firebase_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id
VITE_FIREBASE_VAPID_KEY=your_vapid_key_for_push_notifications
```

### 2. Firebase Console Setup

1. **Enable Cloud Messaging**: In Firebase Console, enable Cloud Messaging for your project
2. **Generate VAPID Key**: Go to Project Settings > Cloud Messaging > Web Push certificates
3. **Configure Domain**: Add your domain to authorized domains

### 3. Service Worker Configuration

The service worker (`public/firebase-messaging-sw.js`) handles background notifications. Update the Firebase configuration in this file to match your project settings.

## Architecture

### Core Components

#### 1. Firebase Configuration (`src/config/firebase.ts`)
- Initializes Firebase app and messaging
- Handles environment detection and error handling
- Provides utility functions for permission management

#### 2. Notification Service (`src/services/notificationService.ts`)
- Manages FCM token registration and storage
- Handles foreground message listening
- Provides methods for permission requests and cleanup

#### 3. Notifications Hook (`src/hooks/useNotifications.ts`)
- React hook for notification state management
- Integrates with authentication system
- Provides actions for permission management

#### 4. Permission Banner (`src/components/notifications/NotificationPermissionBanner.tsx`)
- User-friendly permission request UI
- Shows at top of app when permission needed
- Dismissible with session persistence
- Conditionally show permission banner

## Usage Examples

### Basic Integration

The notification system is automatically integrated into the app through the `AppLayout` component. No additional setup is required for basic functionality.

### Manual Permission Request

```typescript
import { useNotifications } from '@/hooks/useNotifications'

function MyComponent() {
  const { requestPermission, isEnabled, token } = useNotifications()

  const handleEnableNotifications = async () => {
    const success = await requestPermission()
    if (success) {
      console.log('Notifications enabled!')
    }
  }

  return (
    <button onClick={handleEnableNotifications} disabled={isEnabled}>
      {isEnabled ? 'Notifications Enabled' : 'Enable Notifications'}
    </button>
  )
}
```

### Notification Status Display

```typescript
import { NotificationStatus } from '@/components/notifications/NotificationPermissionBanner'

function SettingsPage() {
  return (
    <div>
      <h2>Notification Settings</h2>
      <NotificationStatus />
    </div>
  )
}
```

### Direct Service Usage

```typescript
import { notificationService } from '@/services/notificationService'

// Get current token
const token = notificationService.getStoredToken()

// Check if notifications are enabled
const isEnabled = notificationService.isNotificationEnabled()

// Request permission
const newToken = await notificationService.requestPermissionAndGetToken(userId)
```

## Notification Flow

### Foreground Notifications
1. FCM message received while app is active
2. `onMessage` listener in notification service triggers
3. Custom toast notification displayed using react-hot-toast
4. User can interact with toast (view/dismiss)

### Background Notifications
1. FCM message received while app is backgrounded/closed
2. Service worker (`firebase-messaging-sw.js`) handles message
3. System notification displayed by browser
4. User can click notification to open/focus app

## Token Management

### Token Lifecycle
1. **Registration**: Token generated when user grants permission
2. **Storage**: Token stored locally with user ID association
3. **Authentication**: Token linked to authenticated user
4. **Cleanup**: Token deleted on user sign-out
5. **Refresh**: Token automatically refreshed by Firebase SDK

### Backend Integration
Send the FCM token to your backend for message targeting:

```typescript
// In your authentication flow
const { token } = useNotifications()

if (token && user) {
  await sendTokenToBackend(token, user.id)
}
```

## Error Handling

All notification-related errors are automatically reported to Sentry with appropriate context:

- Permission request failures
- Token registration errors
- Service worker registration issues
- Message handling errors

## Browser Support

### Supported Browsers
- Chrome 50+
- Firefox 44+
- Safari 16+ (with limitations)
- Edge 17+

### Feature Detection
The system automatically detects browser capabilities:
- Service Worker support
- Push Manager API
- Notification API
- Firebase messaging compatibility

## Testing

### Development Testing
1. **Local HTTPS**: Use `https://localhost` for testing (required for notifications)
2. **Permission States**: Test with different permission states (default, granted, denied)
3. **Foreground/Background**: Test notifications in both app states

### Production Testing
1. **Domain Verification**: Ensure domain is authorized in Firebase Console
2. **VAPID Key**: Verify VAPID key is correctly configured
3. **Service Worker**: Confirm service worker is accessible at `/firebase-messaging-sw.js`

## Troubleshooting

### Common Issues

1. **Notifications not working**
   - Check browser console for errors
   - Verify Firebase configuration
   - Ensure HTTPS is used
   - Check notification permissions

2. **Service worker not registering**
   - Verify file exists at `/public/firebase-messaging-sw.js`
   - Check for JavaScript errors in service worker
   - Ensure proper HTTPS setup

3. **Token not generated**
   - Verify VAPID key configuration
   - Check Firebase project settings
   - Ensure user granted permission

4. **Background notifications not showing**
   - Verify service worker is active
   - Check Firebase configuration in service worker
   - Test with browser developer tools

### Debug Mode

Enable debug logging by setting:
```javascript
// In browser console
localStorage.setItem('debug', 'firebase-messaging')
```

## Security Considerations

1. **VAPID Key**: Keep VAPID key secure and rotate regularly
2. **Token Storage**: Tokens are stored locally and cleared on sign-out
3. **Permission Respect**: Always respect user permission choices
4. **Data Privacy**: Minimize data sent in notification payloads

## Performance Considerations

1. **Service Worker**: Lightweight service worker for fast loading
2. **Token Caching**: Tokens cached locally to avoid repeated requests
3. **Error Handling**: Graceful degradation when notifications unavailable
4. **Memory Management**: Proper cleanup on component unmount

## Additional Resources

- [Firebase Cloud Messaging Documentation](https://firebase.google.com/docs/cloud-messaging)
- [Web Push Protocol](https://tools.ietf.org/html/rfc8030)
- [Service Worker API](https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API)
