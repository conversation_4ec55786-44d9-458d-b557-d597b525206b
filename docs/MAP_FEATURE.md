# Map Feature Documentation

## Overview

The Map feature provides a comprehensive view of emergency cases, responders, and hospitals on an interactive Google Maps interface. This feature is designed to give dispatchers real-time visibility into the emergency response ecosystem.

## Features Implemented

### 🗺️ Interactive Google Maps
- **Google Maps Integration**: Uses Google Maps API with custom styling
- **Geolocation Support**: Automatically centers map on user's current location
- **Fallback Location**: Uses Lagos, Nigeria as default if geolocation fails
- **Map Controls**: Full zoom, street view, and map type controls

### 📍 Custom Markers
- **Case Markers**: Color-coded by severity (High: Red, Medium: Yellow, Low: Gray)
- **Responder Markers**: Status-based colors (Online: Blue, Busy: Orange, Offline: Gray)
- **Hospital Markers**: Type-based colors (Public: Green, Private: Blue, Non-Profit: Dark Green)
- **Interactive Info Windows**: Click markers to see detailed information

### 🔍 Address Search
- **Google Geocoding API**: Search for any address or location
- **Real-time Search**: Updates map center based on search results
- **Error Handling**: User-friendly error messages for failed searches
- **Responsive Design**: Adapts to different screen sizes

### 📊 Map Legend
- **Visual Guide**: Shows what different markers represent
- **Categorized Display**: Groups markers by type (Cases, Responders, Hospitals)
- **Responsive**: Hidden on mobile devices to save space

### 📱 Responsive Design
- **Mobile-First**: Optimized for mobile devices
- **Adaptive Layout**: Search bar and legend adjust to screen size
- **Touch-Friendly**: All interactions work on touch devices

## File Structure

```
src/
├── pages/
│   └── MapPage.tsx                 # Main map page component
├── components/
│   └── map/
│       ├── MapMarkers.tsx          # Custom marker utilities
│       └── MapLegend.tsx           # Map legend component
├── utils/
│   └── mockData/
│       └── mapData.ts              # Mock data for development
└── routes/
    └── index.tsx                   # Route configuration
```

## Mock Data

The feature includes comprehensive mock data for development and testing:

- **5 Emergency Cases**: Various severities and locations
- **5 Responders**: Different statuses and types (medic/non-medic)
- **5 Hospitals**: Different ownership types and capacities

## Environment Variables

Make sure to set the following environment variable:

```env
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here
```

## Usage

1. **Navigation**: Access via the sidebar menu item "Map"
2. **View Entities**: Different colored markers show cases, responders, and hospitals
3. **Get Details**: Click any marker to see detailed information
4. **Search Locations**: Use the search bar to find specific addresses
5. **Legend**: Refer to the legend (desktop) to understand marker meanings

## Technical Implementation

### Google Maps Integration
- Uses `@googlemaps/react-wrapper` for React integration
- Custom map styling to reduce visual clutter
- Proper error handling for API failures

### Marker System
- SVG-based custom markers for better scalability
- Dynamic icon selection based on entity properties
- Optimized marker creation and cleanup

### State Management
- React hooks for local state management
- Proper cleanup of map resources
- Error boundary protection

### Performance Considerations
- Lazy loading of map components
- Efficient marker updates
- Proper memory cleanup

## Future Enhancements

### Real-time Data Integration
- Replace mock data with live API endpoints
- WebSocket integration for real-time updates
- Automatic marker updates based on status changes

### Advanced Features
- **Marker Clustering**: Group nearby markers for better performance
- **Route Planning**: Show optimal routes between locations
- **Filtering**: Filter markers by type, status, or other criteria
- **Heatmaps**: Show density of cases or responders
- **Offline Support**: Cache map data for offline viewing

### User Experience
- **Detail Modals**: Full-screen modals for entity details
- **Bulk Actions**: Select multiple entities for batch operations
- **Export Features**: Export map data or screenshots
- **Customizable Views**: User preferences for marker visibility

## API Integration Points

When integrating with live APIs, replace the following:

1. **Mock Data Imports**: Replace with API service calls
2. **Static Markers**: Update markers based on API responses
3. **Real-time Updates**: Implement WebSocket or polling for live data
4. **Error Handling**: Add API-specific error handling

## Testing

The feature includes:
- TypeScript type safety
- Error boundary protection
- Responsive design testing
- Cross-browser compatibility

## Accessibility

- Keyboard navigation support
- Screen reader friendly
- High contrast marker colors
- Proper ARIA labels

## Browser Support

- Modern browsers with ES2020 support
- Mobile browsers (iOS Safari, Chrome Mobile)
- Desktop browsers (Chrome, Firefox, Safari, Edge)
