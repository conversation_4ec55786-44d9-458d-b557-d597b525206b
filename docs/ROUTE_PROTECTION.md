# Route Protection System

This document describes the comprehensive route protection system implemented for the ERA Dispatch Application.

## Overview

The route protection system ensures that:
- Unauthenticated users cannot access protected routes
- Authenticated users are redirected away from auth pages
- Users with pending authentication challenges are redirected to appropriate pages
- Proper loading states and error handling are provided

## Components

### 1. ProtectedRoute Component

**Location**: `src/components/auth/ProtectedRoute.tsx`

Wraps routes that require authentication. Features:
- Checks authentication status
- Redirects unauthenticated users to login with return URL
- Handles authentication challenges (NEW_PASSWORD_REQUIRED, MFA)
- Shows loading spinner during auth checks

**Usage**:
```tsx
<Route 
  path="/dashboard" 
  element={
    <ProtectedRoute>
      <DashboardPage />
    </ProtectedRoute>
  } 
/>
```

### 2. PublicRoute Component

**Location**: `src/components/auth/PublicRoute.tsx`

Wraps routes that should be accessible to unauthenticated users. Features:
- Can optionally redirect authenticated users away
- Handles authentication challenges for authenticated users
- Supports `allowAuthenticated` prop for mixed access routes

**Usage**:
```tsx
<Route 
  path="/login" 
  element={
    <PublicRoute>
      <LoginPage />
    </PublicRoute>
  } 
/>
```

### 3. Authentication Hook

**Location**: `src/hooks/useAuth.ts`

Provides authentication state and methods throughout the app. Features:
- Integrates with Zustand auth store
- Initializes auth state on app load
- Provides utility methods for auth status checks
- Handles token refresh

### 4. Enhanced AuthProvider

**Location**: `src/providers/AuthProvider.tsx`

Wraps the entire app and provides authentication context. Features:
- Shows loading spinner during initial auth check
- Provides authentication state to all components
- Integrates with the useAuth hook

## Route Configuration

### Route Constants

**Location**: `src/utils/constants/routes.ts`

Defines all application routes with categorization:

```typescript
// Public routes (no auth required)
export const PUBLIC_ROUTES = [
  ROUTES.home,
  ROUTES.login,
  ROUTES.dispatchLogin,
  // ... other auth routes
]

// Protected routes (auth required)
export const PROTECTED_ROUTES = [
  ROUTES.dashboard,
  ROUTES.incidents,
  ROUTES.resources,
  // ... other protected routes
]
```

### Route Utilities

**Location**: `src/utils/routeUtils.ts`

Provides utility functions for route management:
- `isPublicRoute()` - Check if route is public
- `isProtectedRoute()` - Check if route is protected
- `getRedirectRoute()` - Get appropriate redirect based on auth status
- `useRouteInfo()` - Hook for current route information

## Authentication Flow

### 1. Initial Load
1. App loads and AuthProvider initializes
2. useAuth hook checks authentication status
3. If authenticated, user info is loaded
4. Loading spinner is shown during this process

### 2. Route Access
1. User navigates to a route
2. Route protection component checks auth status
3. Redirects are performed based on:
   - Authentication status
   - Route protection level
   - Pending challenges

### 3. Authentication Challenges
The system handles various AWS Cognito challenges:
- `NEW_PASSWORD_REQUIRED` → Redirect to `/auth/set-password`
- `SMS_MFA` → Redirect to `/auth/verification`
- `SOFTWARE_TOKEN_MFA` → Redirect to `/auth/verification`

## Error Handling

### 1. Route Errors
**Location**: `src/components/auth/RouteErrorBoundary.tsx`
- Catches route-level errors
- Provides user-friendly error messages
- Offers retry and navigation options

### 2. Application Errors
**Location**: `src/components/ui/ErrorBoundary.tsx`
- Catches application-level errors
- Provides fallback UI
- Offers retry functionality

## Testing the System

### Manual Testing Scenarios

1. **Unauthenticated Access**:
   - Visit `/dashboard` → Should redirect to `/auth/login`
   - Visit `/auth/login` → Should show login page

2. **Authenticated Access**:
   - Login successfully → Should redirect to dashboard
   - Visit `/auth/login` while authenticated → Should redirect to dashboard

3. **Challenge Handling**:
   - Login with account requiring password change → Should redirect to set password
   - Login with MFA enabled → Should redirect to verification

4. **Error Scenarios**:
   - Visit non-existent route → Should show 404 page
   - Network errors → Should show error boundary

### Automated Testing

The system is designed to be testable with:
- Unit tests for utility functions
- Integration tests for route protection
- E2E tests for complete authentication flows

## Configuration

### Environment Variables
Ensure these are set in your `.env` file:
```
VITE_AWS_USER_POOL_ID=your_user_pool_id
VITE_AWS_USER_POOL_WEB_CLIENT_ID=your_client_id
VITE_AWS_OAUTH_DOMAIN=your_oauth_domain
```

### Route Customization
To add new routes:
1. Add route constant to `routes.ts`
2. Add to appropriate category (PUBLIC_ROUTES or PROTECTED_ROUTES)
3. Create route in `AppRoutes` component
4. Wrap with appropriate protection component

## Best Practices

1. **Always use route constants** instead of hardcoded paths
2. **Wrap protected routes** with ProtectedRoute component
3. **Handle loading states** appropriately
4. **Provide fallback UI** for error scenarios
5. **Test authentication flows** thoroughly
6. **Use TypeScript** for type safety

## Troubleshooting

### Common Issues

1. **Infinite redirect loops**: Check route categorization in constants
2. **Auth state not updating**: Verify AuthProvider wraps entire app
3. **Loading never ends**: Check for errors in auth initialization
4. **Routes not protected**: Ensure ProtectedRoute wrapper is used

### Debug Tools

- Check browser network tab for auth requests
- Use React DevTools to inspect auth state
- Check console for authentication errors
- Verify AWS Amplify configuration
