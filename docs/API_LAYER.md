# ERA Dispatch App v2 - API Layer

A comprehensive, type-safe API layer built with TanStack Query (React Query) for modern server state management.

## 🏗️ Architecture Overview

The API layer is organized into several key components:

- **API Client** (`client.ts`) - Axios-based HTTP client with interceptors
- **Services** (`services/`) - Business logic and API endpoint implementations
- **Hooks** (`../hooks/api/`) - TanStack Query hooks for React components
- **Types** (`../types/api/`) - TypeScript interfaces and type definitions
- **Configuration** (`../config/api.ts`) - Environment-based configuration

## 📁 Directory Structure

```
src/
├── api/
│   ├── client.ts              # Core API client with interceptors
│   ├── index.ts               # Main exports
│   └── services/
│       ├── authService.ts     # Authentication & user management
│       ├── casesService.ts    # Case management operations
│       ├── notificationsService.ts # Notification management
│       ├── referencesService.ts    # Reference data operations
│       ├── inquiriesService.ts     # Inquiry management
│       └── ambulanceService.ts     # Ambulance provider & pricing
├── hooks/api/
│   ├── index.ts               # Hook exports
│   ├── useAuth.ts             # Authentication hooks
│   ├── useCases.ts            # Case management hooks
│   ├── useNotifications.ts    # Notification hooks
│   ├── useReferences.ts       # Reference data hooks
│   ├── useInquiries.ts        # Inquiry hooks
│   └── useAmbulance.ts        # Ambulance service hooks
├── types/api/
│   ├── common.ts              # Shared types and interfaces
│   ├── auth.ts                # Authentication types
│   ├── cases.ts               # Case management types
│   ├── notifications.ts       # Notification types
│   ├── references.ts          # Reference data types
│   ├── inquiries.ts           # Inquiry types
│   └── ambulance.ts           # Ambulance service types
├── config/
│   └── api.ts                 # API configuration
└── utils/
    └── apiErrorHandling.ts    # Error handling utilities
```

## 🚀 Key Features

### ✅ Modern Architecture
- **TanStack Query v5** for server state management
- **TypeScript** for complete type safety
- **Axios** with request/response interceptors
- **Environment-based configuration**

### ✅ Authentication Integration
- **AWS Amplify v6** integration
- **Zustand auth store** compatibility
- **Automatic token refresh**
- **Request cancellation support**

### ✅ Error Handling
- **User-friendly error messages**
- **Sentry integration** for error reporting
- **Retry logic** with exponential backoff
- **Network error detection**

### ✅ Caching & Performance
- **Intelligent caching strategies**
- **Query invalidation patterns**
- **Optimistic updates**
- **Background refetching**

### ✅ Developer Experience
- **Complete TypeScript coverage**
- **Consistent naming conventions**
- **Comprehensive JSDoc documentation**
- **Easy-to-use React hooks**

## 🔧 Configuration

### Environment Variables

```env
# API Configuration
VITE_API_BASE_URL=https://api.era-dispatch.com
VITE_APP_ENVIRONMENT=production
VITE_APP_VERSION=2.0.0

# External APIs
VITE_NHIS_API_KEY=your_nhis_api_key
```

### API Configuration

```typescript
// src/config/api.ts
export const apiConfig = {
  baseUrl: import.meta.env.VITE_API_BASE_URL,
  timeout: 30000,
  retries: {
    attempts: 3,
    delay: 1000,
    backoff: 'exponential'
  },
  cache: {
    staleTime: 5 * 60 * 1000,  // 5 minutes
    gcTime: 10 * 60 * 1000,    // 10 minutes
  }
}
```

## 📖 Usage Examples

### Authentication

```typescript
import { useDispatcher, useUpdateDispatcher } from '@/hooks/api'

function UserProfile() {
  const { data: dispatcher, isLoading } = useDispatcher()
  const updateMutation = useUpdateDispatcher()

  const handleUpdate = (data) => {
    updateMutation.mutate({
      dispatcherId: dispatcher.id,
      data
    })
  }

  if (isLoading) return <div>Loading...</div>
  
  return <div>{dispatcher?.name}</div>
}
```

### Case Management

```typescript
import { useCases, useCreateCase } from '@/hooks/api'

function CasesList() {
  const { data: cases, isLoading } = useCases({
    status: ['pending', 'approved'],
    limit: 20
  })
  
  const createMutation = useCreateCase()

  const handleCreateCase = (caseData) => {
    createMutation.mutate(caseData)
  }

  return (
    <div>
      {cases?.items.map(case => (
        <div key={case.id}>{case.case_number}</div>
      ))}
    </div>
  )
}
```

### Notifications

```typescript
import { 
  useUnreadNotifications, 
  useMarkNotificationRead 
} from '@/hooks/api'

function NotificationsList() {
  const { data: notifications } = useUnreadNotifications()
  const markReadMutation = useMarkNotificationRead()

  const handleMarkRead = (notificationId) => {
    markReadMutation.mutate({ notificationId })
  }

  return (
    <div>
      {notifications?.map(notification => (
        <div 
          key={notification.id}
          onClick={() => handleMarkRead(notification.id)}
        >
          {notification.title}
        </div>
      ))}
    </div>
  )
}
```

## 🔄 Migration from v1

The v2 API layer provides automatic migration from v1 patterns:

### v1 → v2 Comparison

| Aspect | v1 | v2 |
|--------|----|----|
| State Management | Redux + manual localStorage | TanStack Query + Zustand |
| Error Handling | Basic try-catch | Comprehensive error utilities |
| Type Safety | Partial TypeScript | Complete TypeScript coverage |
| Caching | Manual implementation | Intelligent query caching |
| Authentication | Manual token management | Automatic AWS Amplify integration |

### Breaking Changes

1. **Import paths changed**:
   ```typescript
   // v1
   import { getCases } from '../utils/apis'
   
   // v2
   import { useCases } from '@/hooks/api'
   ```

2. **Hook-based API calls**:
   ```typescript
   // v1
   const cases = await getCases()
   
   // v2
   const { data: cases, isLoading } = useCases()
   ```

3. **Automatic error handling**:
   ```typescript
   // v1
   try {
     const result = await createCase(data)
   } catch (error) {
     toast.error(error.message)
   }
   
   // v2
   const createMutation = useCreateCase() // Error handling built-in
   createMutation.mutate(data)
   ```

## 🧪 Testing

The API layer includes comprehensive testing utilities:

```typescript
// Test utilities
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { renderHook } from '@testing-library/react'

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })
  
  return ({ children }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

// Test example
test('should fetch cases', async () => {
  const { result } = renderHook(() => useCases(), {
    wrapper: createWrapper(),
  })
  
  await waitFor(() => {
    expect(result.current.isSuccess).toBe(true)
  })
})
```

## 🔍 Debugging

### Query DevTools

```typescript
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'

function App() {
  return (
    <>
      <YourApp />
      <ReactQueryDevtools initialIsOpen={false} />
    </>
  )
}
```

### Logging

The API client includes comprehensive logging in development:

```typescript
// Enable detailed logging
const apiConfig = {
  enableLogging: process.env.NODE_ENV === 'development'
}
```

## 📚 Additional Resources

- [TanStack Query Documentation](https://tanstack.com/query/latest)
- [Axios Documentation](https://axios-http.com/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [AWS Amplify Auth](https://docs.amplify.aws/lib/auth/getting-started/)

## 🤝 Contributing

When adding new API endpoints:

1. **Create types** in `src/types/api/[module].ts`
2. **Implement service** in `src/api/services/[module]Service.ts`
3. **Create hooks** in `src/hooks/api/use[Module].ts`
4. **Add exports** to index files
5. **Write tests** for new functionality
6. **Update documentation**

## 📄 License

This API layer is part of the ERA Dispatch Application v2.
