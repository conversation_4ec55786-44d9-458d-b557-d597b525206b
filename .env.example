# App Configuration
VITE_APP_NAME=ERA Dispatch App v2
VITE_APP_VERSION=2.0.0
VITE_APP_ENVIRONMENT=development

# API Configuration
VITE_API_BASE_URL=https://api.example.com
VITE_API_TIMEOUT=30000

# AWS Configuration
VITE_AWS_REGION=your_region
VITE_AWS_USER_POOL_ID=your_user_pool_id
VITE_AWS_USER_POOL_WEB_CLIENT_ID=your_web_client_id
VITE_AWS_OAUTH_DOMAIN=your_oauth_domain

# Google Maps
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key
VITE_GOOGLE_MAPS_MAP_ID=your_map_id

# Firebase
VITE_FIREBASE_API_KEY=your_firebase_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id
VITE_FIREBASE_VAPID_KEY=your_vapid_key_for_push_notifications

# Sentry
VITE_SENTRY_DSN=your_sentry_dsn
VITE_SENTRY_ENVIRONMENT=development

# Feature Flags
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_MOCK_API=false